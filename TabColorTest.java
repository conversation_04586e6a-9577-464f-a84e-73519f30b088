// 测试TabOverlayDynamic的颜色代码支持
public class TabColorTest {
    public static void main(String[] args) {
        // 测试颜色代码解析
        String testText = "§c红色§f白色§a绿色";
        System.out.println("原始文本: " + testText);
        
        // 测试去除颜色代码
        String cleanText = testText.replaceAll("§.", "");
        System.out.println("清理后文本: " + cleanText);
        
        // 测试颜色代码检测
        for (int i = 0; i < testText.length(); i++) {
            char c = testText.charAt(i);
            if (c == '§' && i + 1 < testText.length()) {
                char colorCode = testText.charAt(i + 1);
                System.out.println("发现颜色代码: §" + colorCode);
            }
        }
    }
}
