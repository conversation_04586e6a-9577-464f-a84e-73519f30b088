package com.leave.ink.core;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.leave.ink.utils.file.FileUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

public record UserInfo(String account, String email, String hwid, String userParams, List<String> roles) {
    public static UserInfo userInfo = null;

    public boolean is(String role) {
        return roles.contains(role);
    }

    public static void getUserInfo(String token) {
        if(userInfo != null) return;
        try {
            String body = FileUtils.getSubString(token, "." , ".");
            String decode = new String(Base64.getDecoder().decode(body), StandardCharsets.UTF_8);

            JsonObject jsonObject = new Gson().fromJson(decode, JsonObject.class);

            String account = jsonObject.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name").getAsString();
            String email = jsonObject.get("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress").getAsString();
            String hwid = jsonObject.get("Hwid").getAsString();
            String params = jsonObject.get("UserParams").getAsString();
            List<String> nameList = new ArrayList<>();
            if (jsonObject.get("http://schemas.microsoft.com/ws/2008/06/identity/claims/role").isJsonArray()) {
                JsonArray roles = jsonObject.getAsJsonArray("http://schemas.microsoft.com/ws/2008/06/identity/claims/role");
                for (JsonElement element : roles) {
                    nameList.add(element.getAsString());
                }
            }else {
                nameList.add(jsonObject.get("http://schemas.microsoft.com/ws/2008/06/identity/claims/role").getAsString());
            }
            userInfo = new UserInfo(account, email, hwid, params,nameList);
        } catch (Exception ignored) {}
    }
}
