package com.leave.ink.language;

import com.leave.ink.features.module.modules.settings.ClientSetting;

public enum Language {
    English,
    Chinese;

    public static Language getLanguage() {
        return ClientSetting.language.getValue().equals("English") ? English : Chinese;
    }

    public static Language getDefaultLanguage() {
        return English;
    }

    public static String getLabel(Text[] texts, Language language) {
        for (Text text : texts) {
            if (text.language().equals(language))
                return text.label();
        }
        return "";
    }
}
