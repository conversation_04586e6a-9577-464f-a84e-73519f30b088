package com.leave.ink.features.friend;

import com.leave.ink.Main;
import com.leave.ink.features.module.modules.settings.ClientSetting;

import java.util.ArrayList;
import java.util.Arrays;

public class FriendsManager {
    public ArrayList<String> friends;

    public FriendsManager() {
        friends = new ArrayList<>();
    }

    public boolean isFriend(String name) {
        var clientSetting = (ClientSetting) Main.INSTANCE.moduleManager.getModule("ClientSetting");

        if (clientSetting.friendMode.getValue().equals("White")) {
            return friends.contains(name);
        } else {
            return !friends.contains(name);
        }
    }

    public void add(String... names) {
        friends.addAll(Arrays.asList(names));
    }

    public void removeAll() {
        friends.clear();
    }

    public void remove(String name) {
        friends.remove(name);
    }
}
