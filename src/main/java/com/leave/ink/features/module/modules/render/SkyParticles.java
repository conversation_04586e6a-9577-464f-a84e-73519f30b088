package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.misc.RandomUtils;
import com.leave.ink.utils.pathfinder.Vec3;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.Render3DEngine;
import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;

import com.mojang.math.Axis;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import org.joml.Matrix4f;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;
@SuppressWarnings("all")
@ModuleInfo(name = {
        @Text(label = "SkyParticles", language = Language.English),
        @Text(label = "天空粒子", language = Language.Chinese)
}, category = Category.Render)
public class SkyParticles extends Module {
    @SettingInfo(name = {
            @Text(label = "SnowBallParticles", language = Language.English),
            @Text(label = "雪球粒子数量", language = Language.Chinese)
    })
    private final NumberSetting sizeSnowBallValue = new NumberSetting(30.0, 0.0, 400.0, "#");
    @SettingInfo(name = {
            @Text(label = "SnowParticles", language = Language.English),
            @Text(label = "雪花粒子数量", language = Language.Chinese)
    })
    private final NumberSetting snowSizeValue = new NumberSetting(100.0, 0.0, 1000.0, "#");
    private final ArrayList<ParticleBase> fireFlies = new ArrayList<>();
    private final ArrayList<ParticleBase> particles = new ArrayList<>();

    public SkyParticles() {
        registerSetting(sizeSnowBallValue, snowSizeValue);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        fireFlies.removeIf(ParticleBase::tick);
        particles.removeIf(ParticleBase::tick);

        for (int i = fireFlies.size(); i < sizeSnowBallValue.getValue().intValue(); i++) {
            fireFlies.add(new FireFly(
                    (float) (mc.player.getX() + RandomUtils.random(-25f, 25f)),
                    (float) (mc.player.getY() + RandomUtils.random(2f, 15f)),
                    (float) (mc.player.getZ() + RandomUtils.random(-25f, 25f)),
                    RandomUtils.random(-0.2f, 0.2f),
                    RandomUtils.random(-0.1f, 0.1f),
                    RandomUtils.random(-0.2f, 0.2f)));
        }

        for (int j = particles.size(); j < snowSizeValue.getValue().intValue(); j++) {
            particles.add(new ParticleBase(
                    (float) (mc.player.getX() + RandomUtils.random(-48f, 48f)),
                    (float) (mc.player.getY() + RandomUtils.random(2, 48f)),
                    (float) (mc.player.getZ() + RandomUtils.random(-48f, 48f)),
                    RandomUtils.random(-0.4f, 0.4f),
                    RandomUtils.random(-0.1f, 0.1f),
                    RandomUtils.random(-0.4f, 0.4f)));
        }
    }

    public void onRender3D(PoseStack stack) {
        stack.pushPose();
        RenderSystem.setShaderTexture(0, new ResourceLocation("/firefly.png"));
        RenderSystem.enableBlend();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE);
        RenderSystem.enableDepthTest();
        RenderSystem.depthMask(false);
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        BufferBuilder bufferBuilder = Tesselator.getInstance().getBuilder();
        bufferBuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_TEX_COLOR);
        fireFlies.forEach(p -> p.render(bufferBuilder));
        RenderUtils.endBuilding(Tesselator.getInstance());
        RenderSystem.depthMask(true);
        RenderSystem.disableDepthTest();
        RenderSystem.disableBlend();
        stack.popPose();

        stack.pushPose();
        RenderSystem.enableBlend();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE);
        RenderSystem.enableDepthTest();
        RenderSystem.depthMask(false);
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        BufferBuilder bufferBuilder1 = Tesselator.getInstance().getBuilder();
        bufferBuilder1.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_TEX_COLOR);
        particles.forEach(p -> p.render(bufferBuilder));
        RenderUtils.endBuilding(Tesselator.getInstance());
        RenderSystem.depthMask(true);
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
        RenderSystem.disableDepthTest();
        RenderSystem.disableBlend();
        stack.popPose();
    }

    public class FireFly extends ParticleBase {
        private final List<Trail> trails = new ArrayList<>();


        public FireFly(float posX, float posY, float posZ, float motionX, float motionY, float motionZ) {
            super(posX, posY, posZ, motionX, motionY, motionZ);
        }

        @Override
        public boolean tick() {
            if (mc.player.distanceToSqr(posX, posY, posZ) > 100) age -= 4;
            else if (!mc.level.getBlockState(new BlockPos((int) posX, (int) posY, (int) posZ)).isAir()) age -= 8;
            else age--;

            if (age < 0)
                return true;

            trails.removeIf(Trail::update);

            prevposX = posX;
            prevposY = posY;
            prevposZ = posZ;

            posX += motionX;
            posY += motionY;
            posZ += motionZ;

            trails.add(new Trail(new Vec3(prevposX, prevposY, prevposZ), new Vec3(posX, posY, posZ),  new Color(255,255,255,255)));

            motionX *= 0.99f;
            motionY *= 0.99f;
            motionZ *= 0.99f;

            return false;
        }

        @Override
        public void render(BufferBuilder bufferBuilder) {
            RenderSystem.setShaderTexture(0, new ResourceLocation("/firefly.png"));
            if (!trails.isEmpty()) {
                Camera camera = mc.gameRenderer.getMainCamera();
                for (Trail ctx : trails) {
                    Vec3 pos = ctx.interpolate(1f);
                    PoseStack matrices = new PoseStack();
                    matrices.mulPose(Axis.XP.rotationDegrees(camera.getXRot()));
                    matrices.mulPose(Axis.YP.rotationDegrees(camera.getYRot() + 180.0F));
                    matrices.translate(pos.getX(), pos.getY(), pos.getZ());
                    matrices.mulPose(Axis.YP.rotationDegrees(-camera.getYRot()));
                    matrices.mulPose(Axis.XP.rotationDegrees(camera.getXRot()));
                    Matrix4f matrix = matrices.last().pose();

                    bufferBuilder.vertex(matrix, 0, -1, 0).uv(0f, 1f).color(RenderUtils.injectAlpha(ctx.color(), (int) (255 * ((float) age / (float) maxAge) * ctx.animation(Render3DEngine.getTickDelta()))).getRGB()).endVertex();
                    bufferBuilder.vertex(matrix, -1, -1, 0).uv(1f, 1f).color(RenderUtils.injectAlpha(ctx.color(), (int) (255 * ((float) age / (float) maxAge) * ctx.animation(Render3DEngine.getTickDelta()))).getRGB()).endVertex();
                    bufferBuilder.vertex(matrix, -1, 0, 0).uv(1f, 0).color(RenderUtils.injectAlpha(ctx.color(), (int) (255 * ((float) age / (float) maxAge) * ctx.animation(Render3DEngine.getTickDelta()))).getRGB()).endVertex();
                    bufferBuilder.vertex(matrix, 0, 0, 0).uv(0, 0).color(RenderUtils.injectAlpha(ctx.color(), (int) (255 * ((float) age / (float) maxAge) * ctx.animation(Render3DEngine.getTickDelta()))).getRGB()).endVertex();
                }
            }
        }
    }

    public static class Trail {
        private final Vec3 from;
        private final Vec3 to;
        private final Color color;
        private int ticks, prevTicks;

        public Trail(Vec3 from, Vec3 to, Color color) {
            this.from = from;
            this.to = to;
            this.ticks = 10;
            this.color = color;
        }

        public Vec3 interpolate(float pt) {
            double x = from.getX() + ((to.getX() - from.getX()) * pt) - mc.getEntityRenderDispatcher().camera.getPosition().x();
            double y = from.getY() + ((to.getY() - from.getY()) * pt) - mc.getEntityRenderDispatcher().camera.getPosition().y();
            double z = from.getZ() + ((to.getZ() - from.getZ()) * pt) - mc.getEntityRenderDispatcher().camera.getPosition().z();
            return new Vec3(x, y, z);
        }

        public double animation(float pt) {
            return (this.prevTicks + (this.ticks - this.prevTicks) * pt) / 10.;
        }

        public boolean update() {
            this.prevTicks = this.ticks;
            return this.ticks-- <= 0;
        }

        public Color color() {
            return color;
        }
    }

    public class ParticleBase {

        protected float prevposX, prevposY, prevposZ, posX, posY, posZ, motionX, motionY, motionZ;
        protected int age, maxAge;

        public ParticleBase(float posX, float posY, float posZ, float motionX, float motionY, float motionZ) {
            this.posX = posX;
            this.posY = posY;
            this.posZ = posZ;
            prevposX = posX;
            prevposY = posY;
            prevposZ = posZ;
            this.motionX = motionX;
            this.motionY = motionY;
            this.motionZ = motionZ;
            age = (int) RandomUtils.random(100, 300);
            maxAge = age;
        }

        public boolean tick() {
            if (mc.player.distanceToSqr(posX, posY, posZ) > 4096) age -= 8;
            else age--;

            if (age < 0)
                return true;

            prevposX = posX;
            prevposY = posY;
            prevposZ = posZ;

            posX += motionX;
            posY += motionY;
            posZ += motionZ;

            motionX *= 0.9f;
            motionZ *= 0.9f;
            motionY -= 0.001f;

            return false;
        }

        public void render(BufferBuilder bufferBuilder) {
            RenderSystem.setShaderTexture(0, new ResourceLocation("/snowflake.png"));

            Camera camera = mc.gameRenderer.getMainCamera();
            Color color1 = new Color(255,255,255,255);
            net.minecraft.world.phys.Vec3 pos = Render3DEngine.interpolatePos(prevposX, prevposY, prevposZ, posX, posY, posZ);

            PoseStack matrices = new PoseStack();
            matrices.mulPose(Axis.XP.rotationDegrees(camera.getXRot()));
            matrices.mulPose(Axis.YP.rotationDegrees(camera.getYRot() + 180.0F));
            matrices.translate(pos.x, pos.y, pos.z);
            matrices.mulPose(Axis.YP.rotationDegrees(-camera.getYRot()));
            matrices.mulPose(Axis.XP.rotationDegrees(camera.getXRot()));
            Matrix4f matrix1 = matrices.last().pose();
            bufferBuilder.vertex(matrix1, 0, -1, 0).uv(0f, 1f).color(RenderUtils.injectAlpha(color1, (int) (255 * ((float) age / (float) maxAge))).getRGB()).endVertex();
            bufferBuilder.vertex(matrix1, -1, -1, 0).uv(1f, 1f).color(RenderUtils.injectAlpha(color1, (int) (255 * ((float) age / (float) maxAge))).getRGB()).endVertex();
            bufferBuilder.vertex(matrix1, -1, 0, 0).uv(1f, 0).color(RenderUtils.injectAlpha(color1, (int) (255 * ((float) age / (float) maxAge))).getRGB()).endVertex();
            bufferBuilder.vertex(matrix1, 0, 0, 0).uv(0, 0).color(RenderUtils.injectAlpha(color1, (int) (255 * ((float) age / (float) maxAge))).getRGB()).endVertex();
        }
    }
}
