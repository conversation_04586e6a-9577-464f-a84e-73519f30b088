package com.leave.ink.features.module.modules.combat;

import com.leave.ink.Main;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

import java.util.Random;

@ModuleInfo(name = {
        @Text(label = "Reach", language = Language.English),
        @Text(label = "距离", language = Language.Chinese)
}, category = Category.Combat)
public class Reach extends Module {
    @SettingInfo(name = {
            @Text(label = "MinRange", language = Language.English),
            @Text(label = "最小距离", language = Language.Chinese)
    })
    public static final NumberSetting minRangeValue = new NumberSetting(3.1f, 3.0, 6.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "MaxRange", language = Language.English),
            @Text(label = "最大距离", language = Language.Chinese)
    })
    public static final NumberSetting maxRangeValue = new NumberSetting(3.2f, 3.0, 6.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "OnlySprint", language = Language.English),
            @Text(label = "仅疾跑时", language = Language.Chinese)
    })
    public static final BooleanSetting onlySprint = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Test", language = Language.English),
            @Text(label = "Test", language = Language.Chinese)
    })
    public static final BooleanSetting test = new BooleanSetting(false);

    @Override
    protected void onEnable() {
        super.onEnable();
//        if(mc.player != null)
//            ChatUtils.displayAlert(mc.player.getEntityReach() + " " + mc.player.getBlockReach());
    }
    private static Reach reach = null;
    public static boolean shouldReach() {
        if(reach == null) {
            reach = ((Reach) Main.INSTANCE.moduleManager.getModule("Reach"));
        }
        if(mc.player.isInWater()) return false;
        if(!reach.isEnable()) return false;
        if(onlySprint.getValue()) {
            return mc.player.isSprinting();
        }
        return true;
    }

    public double getRange() {
        return random(minRangeValue.getValue().doubleValue(), maxRangeValue.getValue().doubleValue());
    }
    private final Random random = new Random();
    private double random(double min, double max) {
        if(min == max) return max;
        return min + (max - min) * random.nextDouble();
    }
    public Reach() {
        registerSetting(maxRangeValue, minRangeValue, onlySprint);
    }
}
