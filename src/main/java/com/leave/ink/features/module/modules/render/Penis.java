package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import com.mojang.math.Axis;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import org.lwjgl.opengl.GL11;

import java.awt.*;

@ModuleInfo(name = {
        @Text(label = "Penis", language = Language.English),
        @Text(label = "屌", language = Language.Chinese)
}, category = Category.Render)
public class Penis extends Module {
    @SettingInfo(name = {
            @Text(label = "ShowInFirstPerson", language = Language.English),
            @Text(label = "第一人称显示", language = Language.Chinese)
    })
    public BooleanSetting renderInFirstPerson = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Exquisite", language = Language.English),
            @Text(label = "精致度大小", language = Language.Chinese)
    })
    private final NumberSetting exquisite = new NumberSetting(35, 1, 300, "#");
    @SettingInfo(name = {
            @Text(label = "BallSize", language = Language.English),
            @Text(label = "球大小", language = Language.Chinese)
    })
    private final NumberSetting ballSize = new NumberSetting(0.2f, 0.1f, 2.0f, "#.00");
    @SettingInfo(name = {
            @Text(label = "Length", language = Language.English),
            @Text(label = "长度", language = Language.Chinese)
    })
    private final NumberSetting length = new NumberSetting(1f, 0.5f, 3.0f, "#.00");
    @SettingInfo(name = {
            @Text(label = "Uncircumcised", language = Language.English),
            @Text(label = "未阉割", language = Language.Chinese)
    })
    public BooleanSetting uncircumcised = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "ShaftColor", language = Language.English),
            @Text(label = "轴长颜色", language = Language.Chinese)
    })
    private final ColorSetting selfShaftColor = new ColorSetting(new Color(95, 67, 63, 255));
    @SettingInfo(name = {
            @Text(label = "TipColor", language = Language.English),
            @Text(label = "球颜色", language = Language.Chinese)
    })
    private final ColorSetting selfTipColor = new ColorSetting(new Color(160, 99, 98, 255));

    public Penis() {
        registerSetting(renderInFirstPerson, exquisite, length, ballSize, uncircumcised, selfShaftColor, selfTipColor);
    }

    @EventTarget
    public void onRender(EventRender3D event) {
        for (final Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof Player) {
                if (mc.options.getCameraType().isFirstPerson() && !renderInFirstPerson.getValue() && entity == mc.player)
                    continue;

                EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
                double x = entity.xOld + (entity.getX() - entity.xOld) * mc.getFrameTime() - renderManager.camera.getPosition().x;
                double y = entity.yOld + (entity.getY() - entity.yOld) * mc.getFrameTime() - renderManager.camera.getPosition().y + entity.getBbHeight() / 2.0f - 0.22499999403953552;
                double z = entity.zOld + (entity.getZ() - entity.zOld) * mc.getFrameTime() - renderManager.camera.getPosition().z;
                PoseStack poseStack = event.getPoseStack();
                poseStack.pushPose();
                RenderSystem.enableBlend();
                RenderSystem.defaultBlendFunc();
                RenderSystem.disableDepthTest();
                poseStack.translate(x, y, z);
                poseStack.mulPose(Axis.YP.rotationDegrees(-entity.getYRot()));
                poseStack.mulPose(Axis.XP.rotationDegrees(90));
                onRender(poseStack);
                poseStack.popPose();
                RenderSystem.enableDepthTest();
                RenderSystem.depthFunc(GL11.GL_LEQUAL);
                RenderSystem.disableBlend();
            }
        }
    }

    private void onRender(PoseStack poseStack) {
        float size = ballSize.getValue().floatValue();
        poseStack.translate(0.0, 0, 0.07500000298023224);
        renderCylinder(poseStack, size - 0.05f, length.getValue().floatValue(), exquisite.getValue().intValue(), selfShaftColor.getValue());
        poseStack.translate(0.0, 0.0, 0.02500000298023223);
        poseStack.translate(-0.09000000074505805, 0.0, 0.0);
        renderSphere(poseStack, size, 20, selfShaftColor.getValue());
        poseStack.translate(0.16000000149011612, 0.0, 0.0);
        renderSphere(poseStack, size, 20, selfShaftColor.getValue());
        poseStack.translate(-0.07000000074505806, length.getValue().floatValue() + (uncircumcised.getValue() ? 0f : -0.25f), 0.0);
        renderSphere(poseStack, size + 0.02f, 20, selfTipColor.getValue());
    }

    private void renderSphere(PoseStack poseStack, float radius, int segments, Color color) {
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        buffer.begin(VertexFormat.Mode.TRIANGLE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        for (int i = 0; i <= segments; i++) {
            double lat0 = Math.PI * (-0.5 + (double) (i - 1) / segments);
            double z0 = Math.sin(lat0);
            double zr0 = Math.cos(lat0);

            double lat1 = Math.PI * (-0.5 + (double) i / segments);
            double z1 = Math.sin(lat1);
            double zr1 = Math.cos(lat1);

            for (int j = 0; j <= segments; j++) {
                double lng = 2 * Math.PI * (double) (j - 1) / segments;
                double x = Math.cos(lng);
                double y = Math.sin(lng);

                buffer.vertex(poseStack.last().pose(), (float) (radius * x * zr0), (float) (radius * y * zr0), (float) (radius * z0)).uv(mc.player.getYRot(), mc.player.getXRot())
                        .color(color.getRGB()).endVertex();
                buffer.vertex(poseStack.last().pose(), (float) (radius * x * zr1), (float) (radius * y * zr1), (float) (radius * z1))
                        .color(color.getRGB()).endVertex();
            }
        }

        tesselator.end();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
    }

    public static void renderCylinder(PoseStack poseStack, float radius, float height, int segments, Color color) {
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        buffer.begin(VertexFormat.Mode.TRIANGLE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        for (int i = 0; i <= segments; i++) {
            double angle = 2 * Math.PI * i / segments;
            double x = Math.cos(angle) * radius;
            double z = Math.sin(angle) * radius;

            buffer.vertex(poseStack.last().pose(), (float) x, 0, (float) z)
                    .color(color.getRGB())
                    .endVertex();
            buffer.vertex(poseStack.last().pose(), (float) x, height, (float) z)
                    .color(color.getRGB())
                    .endVertex();
        }
        tesselator.end();

        buffer.begin(VertexFormat.Mode.TRIANGLE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        for (int i = 0; i <= segments / 2; i++) {
            double lat = Math.PI * i / (segments / 2);
            double sinLat = Math.sin(lat);
            double cosLat = Math.cos(lat);

            for (int j = 0; j <= segments; j++) {
                double lng = 2 * Math.PI * j / segments;
                double sinLng = Math.sin(lng);
                double cosLng = Math.cos(lng);

                float x = (float) (cosLng * sinLat * radius);
                float y = (float) (cosLat * radius);
                float z = (float) (sinLng * sinLat * radius);

                buffer.vertex(poseStack.last().pose(), x, height + y, z)
                        .color(color.getRGB())
                        .endVertex();
                buffer.vertex(poseStack.last().pose(), x, height, z)
                        .color(color.getRGB())
                        .endVertex();
            }
        }
        tesselator.end();

        buffer.begin(VertexFormat.Mode.TRIANGLE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        for (int i = 0; i <= segments / 2; i++) {
            double lat = Math.PI * i / (segments / 2);
            double sinLat = Math.sin(lat);
            double cosLat = Math.cos(lat);

            for (int j = 0; j <= segments; j++) {
                double lng = 2 * Math.PI * j / segments;
                double sinLng = Math.sin(lng);
                double cosLng = Math.cos(lng);

                float x = (float) (cosLng * sinLat * radius);
                float y = (float) (cosLat * radius);
                float z = (float) (sinLng * sinLat * radius);

                buffer.vertex(poseStack.last().pose(), x, -y, z)
                        .color(color.getRGB())
                        .endVertex();
                buffer.vertex(poseStack.last().pose(), x, 0, z)
                        .color(color.getRGB())
                        .endVertex();
            }
        }

        tesselator.end();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
    }
}
