package com.leave.ink.features.module.modules.other;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventMotion;
import com.leave.ink.events.EventType;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.misc.RandomUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import net.minecraft.world.phys.HitResult;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "AimTop", language = Language.English),
        @Text(label = "大陀螺", language = Language.Chinese)
}, category = Category.Other)
public class AimTop extends Module {
    @SettingInfo(name = {
            @Text(label = "YawMode", language = Language.English),
            @Text(label = "Yaw轴模式", language = Language.Chinese)
    })
    private final ModeSetting yawModeValue = new ModeSetting("Spin", Arrays.asList("Jitter", "Spin", "Back", "BackJitter"));
    @SettingInfo(name = {
            @Text(label = "PitchMode", language = Language.English),
            @Text(label = "Pitch轴模式", language = Language.Chinese)
    })
    private final ModeSetting pitchModeValue = new ModeSetting("Down", Arrays.asList("Down", "Up", "Jitter", "AnotherJitter"));
    @SettingInfo(name = {
            @Text(label = "SpinSpeed", language = Language.English),
            @Text(label = "Spin模式速度", language = Language.Chinese)
    })
    private final NumberSetting spinSpeedValue = new NumberSetting(20, 1, 90, "#");
    @SettingInfo(name = {
            @Text(label = "SilentRotate", language = Language.English),
            @Text(label = "静态转头", language = Language.Chinese)
    })
    private final BooleanSetting rotateValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Strict", language = Language.English),
            @Text(label = "Strict移动", language = Language.Chinese)
    })
    private final BooleanSetting rotationStrafe = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "DisableAura", language = Language.English),
            @Text(label = "杀戮光环时不运行", language = Language.Chinese)
    })
    private final BooleanSetting disableAura = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "DisableBlock", language = Language.English),
            @Text(label = "瞄方块时不运行", language = Language.Chinese)
    })
    private final BooleanSetting disableBlock = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "DisableEntity", language = Language.English),
            @Text(label = "瞄实体时不运行", language = Language.Chinese)
    })
    private final BooleanSetting disableEntity = new BooleanSetting(true);
    private float yaw = 0f;
    private float pitch = 0f;

    public AimTop() {
        registerSetting(yawModeValue, pitchModeValue, spinSpeedValue, rotateValue, rotationStrafe, disableAura, disableBlock, disableEntity);
    }

    @EventTarget
    public void onUpdate(EventMotion event) {
        if (Utils.isNull()) return;

        if (event.getEventType() == EventType.PRE) {
            switch (yawModeValue.getValue().toLowerCase()) {
                case "spin" -> {
                    yaw += spinSpeedValue.getValue().floatValue();
                    if (yaw > 180.0f) {
                        yaw = -180.0f;
                    } else if (yaw < -180.0f) {
                        yaw = 180.0f;
                    }
                }

                case "jitter" -> yaw = mc.player.getYRot() + (mc.player.tickCount % 2 == 0 ? 90F : -90F);
                case "back" -> yaw = mc.player.getYRot() + 180f;
                case "backjitter" -> yaw = (float) (mc.player.getYRot() + 180f + RandomUtils.nextDouble(-3.0, 3.0));
            }

            switch (pitchModeValue.getValue().toLowerCase()) {
                case "up" -> pitch = -90.0f;
                case "down" -> pitch = 90.0f;
                case "anotherjitter" -> pitch = (float) (60f + RandomUtils.nextDouble(-3.0, 3.0));
                case "jitter" -> {
                    pitch += 30.0f;
                    if (pitch > 90.0f) {
                        pitch = -90.0f;
                    } else if (pitch < -90.0f) {
                        pitch = 90.0f;
                    }
                }
            }

            if (disableAura.getValue()) {
                KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
                if (killAura.isEnable())
                    return;
            }

            if (disableBlock.getValue() && mc.hitResult != null && mc.hitResult.getType() == HitResult.Type.BLOCK) {
                return;
            }

            if (disableEntity.getValue() && mc.hitResult != null && mc.hitResult.getType() == HitResult.Type.ENTITY) {
                return;
            }

            if (mc.player.isDeadOrDying() || mc.player.isSpectator()) {
                return;
            }

            if (rotateValue.getValue()) {
                RotationUtils.setRotation(new Rotation(yaw, pitch), 10, rotationStrafe.getValue() ? MovementFix.NORMAL : MovementFix.OFF, false);
            } else {
                mc.player.setYRot(yaw);
                mc.player.setXRot(pitch);
            }
        }
    }
}
