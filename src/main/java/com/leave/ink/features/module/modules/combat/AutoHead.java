package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.InventoryUtils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.network.protocol.game.ServerboundSetCarriedItemPacket;
import net.minecraft.network.protocol.game.ServerboundUseItemPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.level.block.Blocks;

@ModuleInfo(name = {
        @Text(label = "AutoHead", language = Language.English),
        @Text(label = "自动金头", language = Language.Chinese)
}, category = Category.Combat)
public class AutoHead extends Module {
    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量", language = Language.Chinese)
    })
    private final NumberSetting healthValue = new NumberSetting(15, 0, 20, "#.0");
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delayValue = new NumberSetting(150, 0, 8000, "#");
    private final TimeUtils timer = new TimeUtils();

    public AutoHead() {
        registerSetting(healthValue, delayValue);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (!timer.hasTimeElapsed(delayValue.getValue().longValue())) {
            return;
        }

        int skullInHotbar = InventoryUtils.findBlock(0, 9, Blocks.PLAYER_HEAD);
        if (mc.player.getHealth() <= healthValue.getValue().floatValue() && skullInHotbar != -1) {
            mc.player.connection.send(new ServerboundSetCarriedItemPacket(skullInHotbar));
            mc.player.connection.send(new ServerboundUseItemPacket(InteractionHand.MAIN_HAND,0));
            mc.player.connection.send(new ServerboundSetCarriedItemPacket(mc.player.getInventory().selected));
            timer.reset();
        }
    }
}
