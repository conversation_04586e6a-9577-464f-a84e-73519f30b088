package com.leave.ink.features.module.modules.settings;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.Tag;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Targets", language = Language.English),
        @Text(label = "目标", language = Language.Chinese)
}, category = Category.Settings)
public class Targets extends Module {
    @SettingInfo(name = {
            @Text(label = "Animals", language = Language.English),
            @Text(label = "动物 ", language = Language.Chinese)
    })
    public static final BooleanSetting animals = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Mobs", language = Language.English),
            @Text(label = "怪物 ", language = Language.Chinese)
    })
    public static final BooleanSetting mobs = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Player", language = Language.English),
            @Text(label = "玩家 ", language = Language.Chinese)
    })
    public static final BooleanSetting player = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Teams", language = Language.English),
            @Text(label = "队伍", language = Language.Chinese)
    })
    private static final ModeSetting teamsMode = new ModeSetting("Off", Arrays.asList("Off", "Armor", "Color", "Heypixel-SH"));
    @SettingInfo(name = {
            @Text(label = "Villagers", language = Language.English),
            @Text(label = "村民 ", language = Language.Chinese)
    })
    public static final BooleanSetting villagers = new BooleanSetting(false);

    public Targets() {
        registerSetting(animals, mobs, player, teamsMode, villagers);
    }

    @Override
    protected void onEnable() {
        setEnable(false);
    }

    public static boolean isTeam(LivingEntity entity) {
        if (teamsMode.getValue().equals("Off"))
            return false;

        if (entity instanceof Player entityPlayer) {
            switch (teamsMode.getValue()) {
                case "Armor" -> {
                    ItemStack myHead = mc.player.getInventory().armor.get(3);
                    ItemStack entityHead = entityPlayer.getInventory().armor.get(3);
                    int myArmorColor = getArmorColor(myHead);
                    int entityArmorColor = getArmorColor(entityHead);
                    return getArmorColor(myHead) == getArmorColor(entityHead) && myArmorColor != -1 && entityArmorColor != -1;
                }

                case "Color" -> {
                    return getEntityNameColor(mc.player).equals(getEntityNameColor(entityPlayer));
                }

                case "Heypixel-SH" -> {
                    return Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText()).contains("§a");
                }
            }
        }

        return false;
    }

    @Override
    public String getDescription() {
        return "对哪些目标生效";
    }

    private static String getEntityNameColor(Player entity) {
        String name = Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText());
        if (name.contains("§")) {
            if (name.contains("§1")) {
                return "§1";
            } else if (name.contains("§2")) {
                return "§2";
            } else if (name.contains("§3")) {
                return "§3";
            } else if (name.contains("§4")) {
                return "§4";
            } else if (name.contains("§5")) {
                return "§5";
            } else if (name.contains("§6")) {
                return "§6";
            } else if (name.contains("§7")) {
                return "§7";
            } else if (name.contains("§8")) {
                return "§8";
            } else if (name.contains("§9")) {
                return "§9";
            } else if (name.contains("§0")) {
                return "§0";
            } else if (name.contains("§e")) {
                return "§e";
            } else if (name.contains("§d")) {
                return "§d";
            } else if (name.contains("§a")) {
                return "§a";
            } else if (name.contains("§b")) {
                return "§b";
            } else if (name.contains("§c")) {
                return "§c";
            } else if (name.contains("§f")) {
                return "§f";
            }
        }
        return "null";
    }

    private static int getArmorColor(ItemStack armorStack) {
        CompoundTag tag = armorStack.getOrCreateTag();

        if (tag.contains("display", Tag.TAG_COMPOUND)) {
            CompoundTag displayTag = tag.getCompound("display");
            if (displayTag.contains("color", Tag.TAG_INT)) {
                return displayTag.getInt("color");
            }
        }

        return -1;
    }
}
