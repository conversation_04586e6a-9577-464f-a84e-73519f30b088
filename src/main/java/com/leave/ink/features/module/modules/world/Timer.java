package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPlayerTick;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.timer.MinecraftTimer;

@ModuleInfo(name = {
        @Text(label = "Timer", language = Language.English),
        @Text(label = "变速齿轮", language = Language.Chinese)
}, category = Category.World)
public class Timer extends Module {
    @SettingInfo(name = {
            @Text(label = "Speed", language = Language.English),
            @Text(label = "速度", language = Language.Chinese)
    })
    private final NumberSetting speedValue = new NumberSetting(2.5f, 0.1f, 5f, "#.0");

    public Timer() {
        registerSetting(speedValue);
    }

    @Override
    public void onDisable() {
        if (mc.player == null || mc.level == null)
            return;

        MinecraftTimer.setTimerSpeed(1f);
    }

    @EventTarget
    public void onUpdate(EventPlayerTick event) {
        if (mc.player == null || mc.level == null)
            return;

        MinecraftTimer.setTimerSpeed(speedValue.getValue().floatValue());
    }
}
