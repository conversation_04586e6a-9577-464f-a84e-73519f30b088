package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.MovementUtils;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Fly", language = Language.English),
        @Text(label = "飞行", language = Language.Chinese)
}, category = Category.Movement)
public class Fly extends Module {
    @SettingInfo(name = {
            @Text(label = "Speed", language = Language.English),
            @Text(label = "速度", language = Language.Chinese)
    })
    private final NumberSetting vanillaSpeed = new NumberSetting(2F, 0F, 10F, "#.00");
    @SettingInfo(name = {
            @Text(label = "SpeedY", language = Language.English),
            @Text(label = "升降速度", language = Language.Chinese)
    })
    private final NumberSetting vanillaSpeedY = new NumberSetting(1F, 0F, 10F, "#.00");
    @SettingInfo(name = {
            @Text(label = "Reset", language = Language.English),
            @Text(label = "重置移动", language = Language.Chinese)
    })
    private final BooleanSetting reset = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    public final ModeSetting mode = new ModeSetting("Vanilla", Arrays.asList("Jump", "Vanilla"),
            new SettingAttribute<>(vanillaSpeed, "Vanilla"),
            new SettingAttribute<>(vanillaSpeedY, "Vanilla"),
            new SettingAttribute<>(reset, "Vanilla"));

    public Fly() {
        registerSetting(mode);
    }

    @Override
    public void onDisable() {
        if (mc.player == null || mc.level == null)
            return;

        if (mode.getValue().equals("Jump"))
            return;

        mc.player.getAbilities().flying = false;

        if (reset.getValue()) {
            mc.player.setDeltaMovement(0.0, 0.0, 0.0);
        }
    }

    @EventTarget
    public void onTick(EventUpdate event) {
        if (mc.player == null || mc.level == null)
            return;

        if (mode.getValue().equals("Jump")) {
            if (mc.options.keyJump.isDown())
                mc.player.jumpFromGround();

            return;
        }

        mc.player.getAbilities().flying = false;
        mc.player.setDeltaMovement(0.0, 0.0, 0.0);

        if (mc.options.keyJump.isDown())
            mc.player.setDeltaMovement(mc.player.getDeltaMovement().add(0, vanillaSpeedY.getValue().floatValue(), 0));
        if (mc.options.keyShift.isDown())
            mc.player.setDeltaMovement(mc.player.getDeltaMovement().add(0, -vanillaSpeedY.getValue().floatValue(), 0));

        MovementUtils.strafe(vanillaSpeed.getValue().floatValue());
    }
}
