package com.leave.ink.features.module.modules.settings;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "GuiSetting", language = Language.English),
        @Text(label = "Gui设置", language = Language.Chinese)
}, category = Category.Settings)
public class GuiSetting extends Module {



}
