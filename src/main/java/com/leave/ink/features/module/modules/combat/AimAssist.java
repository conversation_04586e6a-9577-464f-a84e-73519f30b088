package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.events.EventPlayerTick;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.rotation.*;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.phys.Vec3;
import java.util.*;

@ModuleInfo(name = {
        @Text(label = "AimAssist", language = Language.English),
        @Text(label = "自瞄", language = Language.Chinese)
}, category = Category.Combat)
public class AimAssist extends Module {
    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting rangeValue = new NumberSetting(3.5, 3.0, 6.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Fov", language = Language.English),
            @Text(label = "视角", language = Language.Chinese)
    })
    private final NumberSetting fov = new NumberSetting(180.0, 1.0, 180.0, "#");
    @SettingInfo(name = {
            @Text(label = "ClickAim", language = Language.English),
            @Text(label = "单击触发", language = Language.Chinese)
    })
    private final BooleanSetting clickAim = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "ClickTime", language = Language.English),
            @Text(label = "单击触发时间", language = Language.Chinese)
    })
    private final NumberSetting clickTime = new NumberSetting(500.0, 0.0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "Priority", language = Language.English),
            @Text(label = "优先级", language = Language.Chinese)
    })
    private final ModeSetting priorityValue = new ModeSetting(
            "Distance", Arrays.asList(
            "Health",
            "Distance",
            "LivingTime",
            "Fov",
            "Armor",
            "HurtResistance",
            "HurtTime",
            "RegenAmplifier"
    )
    );
    @SettingInfo(name = {
            @Text(label = "SmoothSpeed", language = Language.English),
            @Text(label = "平滑速度", language = Language.Chinese)
    })
    private final NumberSetting smoothSpeed = new NumberSetting(0.2, 0.01, 1.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "OnlyWeapon", language = Language.English),
            @Text(label = "拿武器时触发", language = Language.Chinese)
    })
    private final BooleanSetting onlyWeapon = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "BowAssistDistance", language = Language.English),
            @Text(label = "弓箭自瞄距离", language = Language.Chinese)
    })
    private final NumberSetting bowAssistDistance = new NumberSetting(20, 3.0, 200.0, "#");
    @SettingInfo(name = {
            @Text(label = "Predict", language = Language.English),
            @Text(label = "预瞄", language = Language.Chinese)
    })
    private final BooleanSetting predict = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "BowAssist", language = Language.English),
            @Text(label = "弓箭自瞄", language = Language.Chinese)
    })
    private final BooleanSetting bowAssist = new BooleanSetting(true,
            new SettingAttribute<>(bowAssistDistance, true),
            new SettingAttribute<>(predict, true)
    );

    public AimAssist() {
        registerSetting(rangeValue, fov, clickAim, clickTime,
                smoothSpeed, priorityValue, onlyWeapon, bowAssist);
    }

    private final TimeUtils clickTimer = new TimeUtils();
    private final List<LivingEntity> targets = new ArrayList<>();

    @Override
    public String getDescription() {
        return "自动瞄准目标";
    }

    public Rotation rotation = null;

    @EventTarget
    public void onUpdate(EventPlayerTick eventPlayerTick) {
        float range = rangeValue.getValue().floatValue();

        List<LivingEntity> bowTargets = new ArrayList<>();
        List<LivingEntity> combatTargets = new ArrayList<>();

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity livingEntity) {
                if (!Utils.isValidEntity(livingEntity))
                    continue;

                if (Targets.isTeam(livingEntity))
                    continue;

                if (RotationUtils.getRotationDifference(livingEntity) <= fov.getValue().floatValue() &&
                        livingEntity != mc.player) {
                    if (RotationUtils.getDistanceToEntityBox(livingEntity) <= bowAssistDistance.getValue().floatValue() && RotationUtils.canWallAttack(entity, 0)) {
                        bowTargets.add(livingEntity);
                    }
                    if (RotationUtils.getDistanceToEntityBox(livingEntity) <= range) {
                        combatTargets.add(livingEntity);
                    }
                }
            }
        }

        if (bowTargets.isEmpty() && combatTargets.isEmpty()) {
            targets.clear();
        } else {
            targets.clear();

            targets.addAll(isUsingBow() ? bowTargets : combatTargets);

            targets.removeIf(LivingEntity::isDeadOrDying);
            switch (priorityValue.getValue().toLowerCase()) {
                case "distance" -> targets.sort(Comparator.comparingDouble(RotationUtils::getDistanceToEntityBox));
                case "health" -> targets.sort(Comparator.comparingDouble(e -> e.getHealth() + e.getAbsorptionAmount()));
                case "fov" -> targets.sort(Comparator.comparingDouble(RotationUtils::getRotationDifference));
                case "livingtime" -> targets.sort((e1, e2) -> Integer.compare(e2.tickCount, e1.tickCount));
                case "armor" -> targets.sort(Comparator.comparingInt(LivingEntity::getArmorValue));
                case "hurttime" -> targets.sort(Comparator.comparingInt(e -> e.hurtTime));
                case "hurtresistance" -> targets.sort(Comparator.comparingInt(e -> e.invulnerableTime));
                case "regenamplifier" ->
                        targets.sort(Comparator.comparingInt(e -> e.hasEffect(MobEffects.REGENERATION) ? e.getEffect(MobEffects.REGENERATION).getAmplifier() : -1));
            }
        }

        if (!targets.isEmpty()) {
            LivingEntity target = targets.get(0);
            if (bowAssist.getValue() && mc.player.getMainHandItem().getItem() instanceof BowItem ||
                    mc.player.getMainHandItem().getItem() instanceof CrossbowItem) {

                if (mc.player.isUsingItem()) {
                    Vec3 targetPos = target.getEyePosition();
                    if (predict.getValue()) {
                        final double tick = (float) PredictUtils.estimateProjectileHitTime(target, 0.05d, 3.0d);
                        final PlayerInfo playerInfo = mc.getConnection().getPlayerInfo(mc.player.getUUID());
                        if (playerInfo == null) return;
                        int ping = playerInfo.getLatency();
                        if (ping < 0) ping = 100;
                        final double finalTick = tick + (ping / 50.0) + 4;
                        targetPos = PredictUtils.forecastPlayerLocation(target, finalTick);
                    }

                    if (targetPos == null) return;

                    rotation = RotationUtils.calculateWithGravity(mc.player.getEyePosition(), targetPos, 0.05d, 3.0d);
                    return;
                }
            }

            ClipRotation clipRotation = ClipRotation.getClipEntity(target, 0);

            if (clipRotation == null) {
                rotation = null;
                return;
            }

            if (RotationUtils.getDistanceToEntityBox(target) <= range && clipRotation.isCanSee()) {
                rotation = clipRotation.getRotation();
            } else {
                rotation = null;
            }
        } else {
            rotation = null;
        }
    }

    private boolean isUsingBow() {
        if (bowAssist.getValue() && mc.player.getMainHandItem().getItem() instanceof BowItem ||
                mc.player.getMainHandItem().getItem() instanceof CrossbowItem) {
            return mc.player.isUsingItem();
        }

        return false;
    }

    @EventTarget
    public void onUpdate(EventRender2D event) {
        if (mc.player == null)
            return;

        if (mc.options.keyAttack.isDown()) {
            clickTimer.reset();
        }

        if (rotation == null)
            return;

        if (!isUsingBow()) {
            if (onlyWeapon.getValue() && !(mc.player.getMainHandItem().getItem() instanceof SwordItem || mc.player.getMainHandItem().getItem() instanceof AxeItem)) {
                return;
            }

            if (clickAim.getValue() && clickTimer.hasTimeElapsed(clickTime.getValue().longValue()))
                return;
        }

        Rotation smoothRotation = smooth(rotation, new Rotation(mc.player.getYRot(), mc.player.getXRot()), smoothSpeed.getValue().floatValue());
        mc.player.setXRot(smoothRotation.getPitch());
        mc.player.setYRot(Rotation.getFixedYaw(smoothRotation.getYaw()));
    }

    private Rotation smooth(Rotation target, Rotation current, float speed) {
        speed = Math.max(0.0f, Math.min(1.0f, speed));
        float smoothedYaw = smoothAngle(target.getYaw(), current.getYaw(), speed);
        float smoothedPitch = smoothAngle(target.getPitch(), current.getPitch(), speed);

        return new Rotation(smoothedYaw, smoothedPitch);
    }

    private float smoothAngle(float targetAngle, float currentAngle, float speed) {
        targetAngle = normalizeAngle(targetAngle);
        currentAngle = normalizeAngle(currentAngle);
        float angleDiff = shortestAngleDifference(targetAngle, currentAngle);
        return currentAngle + angleDiff * speed;
    }

    private float normalizeAngle(float angle) {
        angle %= 360.0f;
        if (angle > 180.0f) {
            angle -= 360.0f;
        } else if (angle < -180.0f) {
            angle += 360.0f;
        }
        return angle;
    }

    private float shortestAngleDifference(float target, float current) {
        float diff = target - current;
        if (diff > 180.0f) {
            diff -= 360.0f;
        } else if (diff < -180.0f) {
            diff += 360.0f;
        }
        return diff;
    }
}
