package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventStrafe;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.world.Scaffold;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.utils.Utils;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundPlayerPositionPacket;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.decoration.ArmorStand;
import net.minecraft.world.phys.AABB;

import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Speed", language = Language.English),
        @Text(label = "速度", language = Language.Chinese)
}, category = Category.Movement)
public class Speed extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting modeValue = new ModeSetting("Vanilla", Arrays.asList("Vanilla", "Grim"));
    @SettingInfo(name = {
            @Text(label = "Speed", language = Language.English),
            @Text(label = "速度", language = Language.Chinese)
    })
    private final NumberSetting speed = new NumberSetting(1.25, 1, 5.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "OnlyAir", language = Language.English),
            @Text(label = "只在空中", language = Language.Chinese)
    })
    private final BooleanSetting onlyAir = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Reset", language = Language.English),
            @Text(label = "重置移动", language = Language.Chinese)
    })
    private final BooleanSetting fastStop = new BooleanSetting(false);

    public Speed() {
        registerSetting(modeValue, speed, onlyAir, fastStop);
    }

    private boolean s08 = false;

    @Override
    public void onDisable() {
        if (fastStop.getValue() && !MovementUtils.isMoving()) {
            mc.player.setDeltaMovement(0, 0, 0);
        }
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (modeValue.getValue().equals("Vanilla")) {
            if (onlyAir.getValue() && mc.player.onGround())
                return;

            this.mc.player.setDeltaMovement(this.mc.player.getDeltaMovement().multiply(speed.getValue().floatValue(), 1.0, speed.getValue().floatValue()));
        }
    }

    @EventTarget
    public void onStrafe(EventStrafe event) {
        if (modeValue.getValue().equals("Grim")) {
            if (onlyAir.getValue() && mc.player.onGround())
                return;

            if (mc.player.hurtTime > 0)
                return;

            Scaffold scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");
            if (s08 || scaffold.isEnable())
                return;

            if (mc.player.input.forwardImpulse == 0.0f && mc.player.input.leftImpulse == 0.0f)
                return;

            AABB box = mc.player.getBoundingBox().expandTowards(1.0, 1.0, 1.0);
            for (Entity entity : mc.level.entitiesForRendering()) {
                AABB entityBox = entity.getBoundingBox();
                if (canCauseSpeed(entity) && box.intersects(entityBox)) {
                    double multiply = this.mc.player.onGround() ? 1.06 : 1.04;
                    this.mc.player.setDeltaMovement(this.mc.player.getDeltaMovement().multiply(multiply, 1.0, multiply));
                }
            }
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        Packet<?> packet = event.getPacket();
        if (packet instanceof ClientboundPlayerPositionPacket) {
            s08 = true;
        } else {
            new Thread(() -> {
                try {
                    Thread.sleep(100);
                    s08 = false;
                } catch (InterruptedException ignored) {
                }
            }).start();
        }
    }

    private boolean canCauseSpeed(Entity entity) {
        return entity != mc.player && entity instanceof LivingEntity && !(entity instanceof ArmorStand) && Utils.isValidEntity((LivingEntity) entity);
    }

    @Override
    public String getTag() {
        return modeValue.getValue();
    }
}
