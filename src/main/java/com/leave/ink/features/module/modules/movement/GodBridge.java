package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.CollisionContext;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ModuleInfo(name = {
        @Text(label = "GodBridge", language = Language.English),
        @Text(label = "神桥", language = Language.Chinese)
}, category = Category.Movement)
public class GodBridge extends Module {
    @SettingInfo(name = {
            @Text(label = "PlaceTime", language = Language.English),
            @Text(label = "放置间隔时间", language = Language.Chinese)
    })
    private final NumberSetting placeTime = new NumberSetting(4.0, 0.0, 10.0, "#");
    private int rightClickDelayTimer = 0;

    public GodBridge() {
        registerSetting(placeTime);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (mc.player == null || mc.level == null)
            return;

        run();
    }

    private void run() {
        if (this.rightClickDelayTimer > 0) {
            --this.rightClickDelayTimer;
        } else {
            this.rightClickDelayTimer = placeTime.getValue().intValue();
        }

        final BlockHitResult movingObjectPosition = (BlockHitResult) mc.player.pick(mc.gameMode.getPickRange(), 1.0f, false);
        boolean isKeyUseDown;
        int keyCode = mc.options.keyUse.getDefaultKey().getValue();

        if (keyCode < GLFW.GLFW_MOUSE_BUTTON_LAST) {
            isKeyUseDown = GLFW.glfwGetMouseButton(mc.getWindow().getWindow(), keyCode) == GLFW.GLFW_PRESS;
        } else {
            isKeyUseDown = GLFW.glfwGetKey(mc.getWindow().getWindow(), keyCode) == GLFW.GLFW_PRESS;
        }

        if (movingObjectPosition != null && movingObjectPosition.getType() == HitResult.Type.BLOCK && movingObjectPosition.getDirection() == Direction.UP && isKeyUseDown) {
            final ItemStack itemstack = mc.player.getInventory().getSelected();
            final int i = (itemstack != null) ? itemstack.getMaxStackSize() : 0;
            final BlockPos blockPos = movingObjectPosition.getBlockPos();
            final BlockState blockState = mc.level.getBlockState(blockPos);
            BlockPlaceContext blockPlaceContext = new BlockPlaceContext(mc.level, mc.player, InteractionHand.MAIN_HAND, itemstack, movingObjectPosition);
            if (itemstack != null && itemstack.getItem() instanceof BlockItem) {
                BlockPos placePos = blockPlaceContext.getClickedPos().relative(blockPlaceContext.getClickedFace());
                if (!placePos.equals(blockPos)) {
                    final AABB axisalignedbb = blockState.getBlock().getShape(mc.level.getBlockState(blockPos), mc.level, blockPos, CollisionContext.of(mc.player)).bounds().move(blockPos);
                    if (axisalignedbb == null || mc.level.isEmptyBlock(blockPos)) {
                        return;
                    }
                    Vec3 targetVec3 = null;
                    final Vec3 eyeVec3 = mc.player.getEyePosition(1.0f);
                    final double x1 = axisalignedbb.minX;
                    final double x2 = axisalignedbb.maxX;
                    final double y1 = axisalignedbb.minY;
                    final double y2 = axisalignedbb.maxY;
                    final double z1 = axisalignedbb.minZ;
                    final double z2 = axisalignedbb.maxZ;
                    class Data implements Comparable<Data> {
                        public BlockPos blockPos;
                        public Direction enumFacing;
                        public double cost;

                        public Data(BlockPos blockPos, Direction enumFacing, double d) {
                            this.blockPos = blockPos;
                            this.enumFacing = enumFacing;
                            this.cost = d;
                        }

                        @Override
                        public int compareTo(final Data data) {
                            return (this.cost - data.cost > 0.0) ? -1 : ((this.cost - data.cost < 0.0) ? 1 : 0);
                        }
                    }
                    final List<Data> list = new ArrayList<>();
                    if (x1 > eyeVec3.x || eyeVec3.x > x2 || y1 > eyeVec3.y || eyeVec3.y > y2 || z1 > eyeVec3.z || eyeVec3.z > z2) {
                        final double xCost = Math.abs(eyeVec3.x - 0.5 * (axisalignedbb.minX + axisalignedbb.maxX));
                        final double zCost = Math.abs(eyeVec3.z - 0.5 * (axisalignedbb.minZ + axisalignedbb.maxZ));
                        if (eyeVec3.x < x1) {
                            list.add(new Data(blockPos.west(), Direction.WEST, xCost));
                        } else if (eyeVec3.x > x2) {
                            list.add(new Data(blockPos.east(), Direction.EAST, xCost));
                        }
                        if (eyeVec3.z < z1) {
                            list.add(new Data(blockPos.north(), Direction.NORTH, zCost));
                        } else if (eyeVec3.z > z2) {
                            list.add(new Data(blockPos.south(), Direction.SOUTH, zCost));
                        }
                        Collections.sort(list);
                        final double border = 0.05;
                        double x3 = Mth.clamp(eyeVec3.x, x1 + border, x2 - border);
                        double y3 = Mth.clamp(eyeVec3.y, y1 + border, y2 - border);
                        double z3 = Mth.clamp(eyeVec3.z, z1 + border, z2 - border);
                        for (final Data data : list) {
                            if (!mc.level.isEmptyBlock(data.blockPos)) {
                                continue;
                            }
                            if (data.enumFacing == Direction.WEST || data.enumFacing == Direction.EAST) {
                                x3 = Mth.clamp(eyeVec3.x, x1, x2);
                            } else if (data.enumFacing == Direction.UP || data.enumFacing == Direction.DOWN) {
                                y3 = Mth.clamp(eyeVec3.y, y1, y2);
                            } else {
                                z3 = Mth.clamp(eyeVec3.z, z1, z2);
                            }
                            targetVec3 = new Vec3(x3, y3, z3);
                            break;
                        }
                        if (targetVec3 != null) {
                            final double d0 = targetVec3.x - eyeVec3.x;
                            final double d2 = targetVec3.y - eyeVec3.y;
                            final double d3 = targetVec3.z - eyeVec3.z;
                            final double d4 = Mth.sqrt((float) (d0 * d0 + d3 * d3));
                            final float f = (float) (Mth.atan2(d3, d0) * 180.0 / 3.141592653589793) - 90.0f;
                            final float f2 = (float) (-(Mth.atan2(d2, d4) * 180.0 / 3.141592653589793));
                            final float f3 = mc.player.getYRot();
                            final float f4 = mc.player.getXRot();
                            mc.player.setYRot(f);
                            mc.player.setXRot(f2);
                            final BlockHitResult movingObjectPosition2 = (BlockHitResult) mc.player.pick(mc.gameMode.getPickRange(), 1.0f, false);
                            if (movingObjectPosition2.getType() == HitResult.Type.BLOCK && movingObjectPosition2.getBlockPos().getX() == blockPos.getX() && movingObjectPosition2.getBlockPos().getY() == blockPos.getY() && movingObjectPosition2.getBlockPos().getZ() == blockPos.getZ()) {
                                if (rightClickDelayTimer == 0) {
                                    if (mc.gameMode.useItemOn(mc.player,InteractionHand.MAIN_HAND, movingObjectPosition2).consumesAction()) {
                                        mc.player.swing(InteractionHand.MAIN_HAND);
                                    }
                                }
                                if (!itemstack.isEmpty()) {
                                    if (itemstack.getCount() == 0) {
                                        mc.player.getInventory().setItem(mc.player.getInventory().selected, ItemStack.EMPTY);
                                    } else if (itemstack.getCount() != i || mc.gameMode.getPlayerMode().isCreative()) {
                                        mc.gameRenderer.itemInHandRenderer.itemUsed(InteractionHand.MAIN_HAND);
                                    }
                                }
                            }
                            mc.player.setYRot(f3);
                            mc.player.setXRot(f4);
                            final double pitchDelta = 2.5;
                            final double targetPitch = 75.5;
                            if (targetPitch - pitchDelta < mc.player.getXRot() && mc.player.getXRot() < targetPitch + pitchDelta) {
                                double mod = mc.player.getYRot() % 45.0;
                                if (mod < 0.0) {
                                    mod += 45.0;
                                }
                                final double delta = 5.0;
                                if (mod < delta) {
                                    mc.player.setYRot(mc.player.getYRot() - (float) mod);
                                    mc.player.setXRot((float) targetPitch);
                                } else if (45.0 - mod < delta) {
                                    mc.player.setYRot((float) (mc.player.getYRot() + (45.0 - mod)));
                                    mc.player.setXRot((float) targetPitch);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
