package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventTick;
import com.leave.ink.events.EventType;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;

@ModuleInfo(
        name = {
                @Text(label = "Stronghold Detector", language = Language.English),
                @Text(label = "要塞探测器", language = Language.Chinese)
        },
        category = Category.World
)
public class StrongholdDetector extends Module {

    // Server-specific portal generation ring distances
    private static final double SERVER_MIN_RING_DIST = 256.0;
    private static final double SERVER_MAX_RING_DIST = 768.0;
    private static final long GUIDANCE_MESSAGE_INTERVAL = 30000; // 30 seconds
    private static final double SMALL_RADIUS_THRESHOLD = 128.0; // Radius <= this is considered small scan
    private static final int NUM_Y_SLICES = 4; // Number of Y-slices for smoother scanning

    // --- Settings for General Proximity Alert ---
    @SettingInfo(name = {
            @Text(label = "Enable General Proximity Alert", language = Language.English),
            @Text(label = "启用大致距离提醒", language = Language.Chinese)
    })
    public BooleanSetting enableGeneralProximityAlert = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "General Alert Interval (ms)", language = Language.English),
            @Text(label = "大致提醒间隔 (毫秒)", language = Language.Chinese)
    })
    public NumberSetting generalAlertInterval = new NumberSetting(5000.0, 1000.0, 30000.0, "#");

    @SettingInfo(name = {
            @Text(label = "Max Y Level for Ring Alert", language = Language.English),
            @Text(label = "环形提醒最大Y值", language = Language.Chinese)
    })
    public NumberSetting maxYLevelForRingAlert = new NumberSetting(40.0, -64.0, 100.0, "#");

    // --- Settings for Specific Structure Scan ---
    @SettingInfo(name = {
            @Text(label = "Enable Specific Structure Scan", language = Language.English),
            @Text(label = "启用精确结构扫描", language = Language.Chinese)
    })
    public BooleanSetting enableSpecificStructureScan = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Specific Scan Radius", language = Language.English),
            @Text(label = "精确扫描半径", language = Language.Chinese)
    })
    public NumberSetting specificScanRadius = new NumberSetting(128.0, 32.0, 600.0, "#");

    @SettingInfo(name = {
            @Text(label = "Structure Scan Interval (ms)", language = Language.English),
            @Text(label = "结构扫描间隔 (毫秒)", language = Language.Chinese)
    })
    public NumberSetting structureScanInterval = new NumberSetting(2000.0, 500.0, 10000.0, "#");

    @SettingInfo(name = {
            @Text(label = "Specific Alert Cooldown (ms)", language = Language.English),
            @Text(label = "精确提醒冷却 (毫秒)", language = Language.Chinese)
    })
    public NumberSetting specificAlertCooldown = new NumberSetting(60000.0, 10000.0, 300000.0, "#");



    private long lastGeneralCheckTime = 0;
    private boolean hasNotifiedFirstRing = false;

    private final Set<BlockPos> detectedStrongholdLocations = ConcurrentHashMap.newKeySet();
    private long lastSpecificAlertTime = 0;
    private long lastSpecificScanTime = 0;
    private boolean lastScanFoundStronghold = false;
    private long lastGuidanceMessageTime = 0;
    private int currentYScanSlice = 0;
    private int ySlicesSinceLastFoundOrFullCycle = 0;
    private Set<BlockPos> foundIronDoorPositionsThisCycle = new HashSet<>();
    private boolean ironDoorHintCurrentlyActive = false;
    private long lastIronDoorHintAlertTime = 0;
    private long lastSmallRadiusFullCycleNoPortalMessageTime = 0;

    public StrongholdDetector() {
        registerSetting(
                enableGeneralProximityAlert, generalAlertInterval, maxYLevelForRingAlert,
                enableSpecificStructureScan, specificScanRadius, structureScanInterval,
                specificAlertCooldown
        );
        ironDoorHintCurrentlyActive = false;
        lastIronDoorHintAlertTime = 0;
        lastSmallRadiusFullCycleNoPortalMessageTime = 0;
    }

    @Override
    public void onEnable() {
        super.onEnable();
        lastGeneralCheckTime = 0;
        hasNotifiedFirstRing = false;
        detectedStrongholdLocations.clear();
        lastSpecificAlertTime = 0;
        lastSpecificScanTime = 0;
        currentYScanSlice = 0;
        ySlicesSinceLastFoundOrFullCycle = 0;
        foundIronDoorPositionsThisCycle.clear();
        ironDoorHintCurrentlyActive = false;
        lastIronDoorHintAlertTime = 0;
        lastSmallRadiusFullCycleNoPortalMessageTime = 0;
    }

    @Override
    public void onDisable() {
        super.onDisable();
    }

    @EventTarget
    public void onTick(EventTick event) {
        if (event.getEventType() != EventType.PRE || mc.player == null || mc.level == null) return;

        if (enableGeneralProximityAlert.getValue()) {
            performGeneralProximityCheck();
        }

        if (enableSpecificStructureScan.getValue()) {
            lastScanFoundStronghold = false;

            long currentTimeForGuidance = System.currentTimeMillis();
            BlockPos playerPosForGuidance = mc.player.blockPosition();
            double playerDistFromOriginXZ = Math.sqrt(playerPosForGuidance.getX() * playerPosForGuidance.getX() + playerPosForGuidance.getZ() * playerPosForGuidance.getZ());

            if (playerDistFromOriginXZ < SERVER_MIN_RING_DIST - 10 || playerDistFromOriginXZ > SERVER_MAX_RING_DIST + 10) { // Added a small buffer
                if (currentTimeForGuidance - lastGuidanceMessageTime > GUIDANCE_MESSAGE_INTERVAL) {
                    ChatUtils.displayAlert("提示: 精确扫描在距服务器设定传送门环带(XZ距原点 " + (int)SERVER_MIN_RING_DIST + "-" + (int)SERVER_MAX_RING_DIST + "格) 内最有效。建议先移动至该区域。");
                    lastGuidanceMessageTime = currentTimeForGuidance;
                }
            }

            performSpecificStructureScan();

            if (enableSpecificStructureScan.getValue()) {
                int currentScanRadiusValue = specificScanRadius.getValue().intValue();

                if (ironDoorHintCurrentlyActive && !lastScanFoundStronghold && System.currentTimeMillis() - lastIronDoorHintAlertTime > 6000) {
                    ChatUtils.displayAlert("仍在根据类似末地特征物标记扫描末地传送门...");
                    lastIronDoorHintAlertTime = System.currentTimeMillis();
                }

                if (!lastScanFoundStronghold) {
                    ySlicesSinceLastFoundOrFullCycle++;
                    if (currentScanRadiusValue > SMALL_RADIUS_THRESHOLD) {
                        if (ySlicesSinceLastFoundOrFullCycle >= NUM_Y_SLICES) {
                            if (ironDoorHintCurrentlyActive) {
                                ChatUtils.displayAlert("类似末地特征物标记已发现 (扫描半径 " + currentScanRadiusValue + "格)，但在附近全周期扫描后未找到末地传送门，自动禁用。");
                            } else {
                                ChatUtils.displayAlert("要塞探测器 (" + currentScanRadiusValue + "格扫描, Y轴全周期扫描完成): 未发现末地传送门或类似末地特征物标记，自动禁用。");
                            }
                            this.toggle();
                        }
                    } else {
                        if (ySlicesSinceLastFoundOrFullCycle >= NUM_Y_SLICES) {
                            if (System.currentTimeMillis() - lastSmallRadiusFullCycleNoPortalMessageTime > 6000) {
                                ChatUtils.displayAlert("小范围全周期扫描完成 (半径 " + currentScanRadiusValue + "格)，未发现末地传送门。继续探测...");
                                lastSmallRadiusFullCycleNoPortalMessageTime = System.currentTimeMillis();
                            }
                            foundIronDoorPositionsThisCycle.clear();
                            ironDoorHintCurrentlyActive = false;
                            ySlicesSinceLastFoundOrFullCycle = 0;
                        }
                    }
                }
            }
        }
    }

    private void performGeneralProximityCheck() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastGeneralCheckTime < generalAlertInterval.getValue().longValue()) {
            return;
        }
        lastGeneralCheckTime = currentTime;

        BlockPos playerPos = mc.player.blockPosition();
        double distFromOrigin = Math.sqrt(playerPos.getX() * playerPos.getX() + playerPos.getZ() * playerPos.getZ());
        boolean inRingRange = distFromOrigin > SERVER_MIN_RING_DIST &&
                distFromOrigin < SERVER_MAX_RING_DIST;

        if (inRingRange && playerPos.getY() < maxYLevelForRingAlert.getValue().doubleValue()) {
            if (!hasNotifiedFirstRing) {
                ChatUtils.displayAlert("您已进入末地传送门大致生成地带 (XZ距原点 " + (int)SERVER_MIN_RING_DIST + "-" + (int)SERVER_MAX_RING_DIST + "格，建议Y<" + String.format("%.0f", maxYLevelForRingAlert.getValue().doubleValue()) + ")。可以考虑启用精确扫描功能来寻找附近的传送门。");
                hasNotifiedFirstRing = true;
            }
        } else {
            if (hasNotifiedFirstRing) {
                hasNotifiedFirstRing = false;
            }
        }
    }

    private void performSpecificStructureScan() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSpecificScanTime < structureScanInterval.getValue().longValue()) {
            return;
        }
        lastSpecificScanTime = currentTime;

        BlockPos playerPos = mc.player.blockPosition();
        int radius = specificScanRadius.getValue().intValue();
        performSparseThenDenseScan(playerPos, radius, currentTime);
    }

    private void performSparseThenDenseScan(BlockPos playerPos, int overallRadius, long currentTime) {
        final int stepXZ = 3;
        final int stepY_increment = NUM_Y_SLICES;
        final int gatewayVicinity = 1;

        if (this.currentYScanSlice == 0) {
            this.foundIronDoorPositionsThisCycle.clear();
            this.ironDoorHintCurrentlyActive = false;
        }

        System.out.println("[StrongholdDetector] Scanning Y-slice " + (this.currentYScanSlice + 1) + "/" + NUM_Y_SLICES + " for End Portals (and Iron Door hints) in " + overallRadius + "-block radius. XZstep " + stepXZ + ", Y-increment " + stepY_increment + ".");

        int playerX = playerPos.getX();
        int playerY = playerPos.getY();
        int playerZ = playerPos.getZ();

        int minY = Math.max(mc.level.getMinBuildHeight(), playerY - overallRadius);
        int maxY = Math.min(mc.level.getMaxBuildHeight(), playerY + overallRadius);

        for (int x = playerX - overallRadius; x <= playerX + overallRadius; x += stepXZ) {
            for (int y = minY + this.currentYScanSlice; y <= maxY; y += stepY_increment) {
                for (int z = playerZ - overallRadius; z <= playerZ + overallRadius; z += stepXZ) {
                    if (Thread.currentThread().isInterrupted()) return;
                    BlockPos currentSparseCenter = new BlockPos(x, y, z);
                    for (int dx = -gatewayVicinity; dx <= gatewayVicinity; dx++) {
                        for (int dy = 0; dy <= 0; dy++) {
                            for (int dz = -gatewayVicinity; dz <= gatewayVicinity; dz++) {
                                BlockPos checkPos = new BlockPos(currentSparseCenter.getX() + dx, currentSparseCenter.getY() + dy, currentSparseCenter.getZ() + dz);

                                Block blockInVicinity = mc.level.getBlockState(checkPos).getBlock();
                                if (blockInVicinity == Blocks.END_PORTAL) {
                                    handleEndPortalFound(checkPos, currentTime, overallRadius, playerPos);
                                    return;
                                } else if (blockInVicinity == Blocks.IRON_DOOR) {
                                    BlockPos doorBasePos = getIronDoorBase(checkPos);
                                    if (doorBasePos != null && foundIronDoorPositionsThisCycle.add(doorBasePos)) {
                                        if (foundIronDoorPositionsThisCycle.size() >= 2 && !ironDoorHintCurrentlyActive) {
                                            ironDoorHintCurrentlyActive = true;
                                            lastIronDoorHintAlertTime = System.currentTimeMillis();
                                            BlockPos firstDoorPos = foundIronDoorPositionsThisCycle.iterator().next();
                                            ChatUtils.displayAlert(String.format("类似末地特征物标记对已发现！其中一个标记坐标: (X:%d, Y:%d, Z:%d)。正在该区域扫描末地传送门...", firstDoorPos.getX(), firstDoorPos.getY(), firstDoorPos.getZ()));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        this.currentYScanSlice = (this.currentYScanSlice + 1) % NUM_Y_SLICES;
    }

    private void handleEndPortalFound(BlockPos foundPos, long currentTime, int scanRadius, BlockPos playerPos) {
        lastScanFoundStronghold = true;
        ySlicesSinceLastFoundOrFullCycle = 0;

        foundIronDoorPositionsThisCycle.clear();
        ironDoorHintCurrentlyActive = false;

        if (currentTime - lastSpecificAlertTime > specificAlertCooldown.getValue().longValue()) {
            boolean veryCloseToPreviousAlert = false;
            for (BlockPos detectedPos : detectedStrongholdLocations) {
                if (foundPos.distManhattan(detectedPos) < 10) {
                    veryCloseToPreviousAlert = true;
                    break;
                }
            }

            if (!veryCloseToPreviousAlert) {
                detectedStrongholdLocations.add(foundPos);
                while (detectedStrongholdLocations.size() > 50) {
                    if (!detectedStrongholdLocations.isEmpty()) {
                        detectedStrongholdLocations.remove(detectedStrongholdLocations.iterator().next());
                    } else {
                        break;
                    }
                }

                String message = String.format("### 末地传送门找到! ###  坐标: (X:%d, Y:%d, Z:%d)",
                        foundPos.getX(), foundPos.getY(), foundPos.getZ());
                ChatUtils.displayAlert(message);
                String contextMessage = String.format("扫描中心 (%d,%d,%d)，扫描半径 %d格。",
                        playerPos.getX(), playerPos.getY(), playerPos.getZ(), scanRadius);
                ChatUtils.displayAlert(contextMessage);
                lastSpecificAlertTime = currentTime;
                if (scanRadius > SMALL_RADIUS_THRESHOLD) {
                    ChatUtils.displayAlert("末地传送门已找到 (扫描半径 " + scanRadius + "格)，要塞探测器自动禁用。");
                    this.toggle();
                } else {
                    ChatUtils.displayAlert("末地传送门已找到 (扫描半径 " + scanRadius + "格)，保持探测模式。");
                }
            }
        }
    }

    private BlockPos getIronDoorBase(BlockPos doorPos) {
        if (mc.level == null) return doorPos;
        BlockState state = mc.level.getBlockState(doorPos);
        if (state.getBlock() == Blocks.IRON_DOOR) {
            return doorPos;
        }
        return null;
    }
}