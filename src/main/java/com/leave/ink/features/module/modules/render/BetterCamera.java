package com.leave.ink.features.module.modules.render;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "BetterCamera", language = Language.English),
        @Text(label = "更好的摄像机", language = Language.Chinese)
}, category = Category.Render)
public class BetterCamera extends Module {
    @SettingInfo(name = {
            @Text(label = "Interpolation", language = Language.English),
            @Text(label = "Interpolation", language = Language.Chinese)
    })
    public final NumberSetting interpolation = new NumberSetting(0.10f, 0.01f, 0.35f, "#.00");

    public BetterCamera() {
        registerSetting(interpolation);
    }
}
