package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventSlow;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.Gapple;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;

import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.world.item.*;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "NoSlow", language = Language.English),
        @Text(label = "无减速", language = Language.Chinese)
}, category = Category.Movement)
public class NoSlow extends Module {
   @SettingInfo(name = {
           @Text(label = "Mode", language = Language.English),
           @Text(label = "模式", language = Language.Chinese)
   })
   public final ModeSetting mode = new ModeSetting("Grim", Arrays.asList("Grim"));

   @SettingInfo(name = {
           @Text(label = "Bow", language = Language.English),
           @Text(label = "弓", language = Language.Chinese)
   })
   public final BooleanSetting bow = new BooleanSetting(false);

   @SettingInfo(name = {
           @Text(label = "Crossbow", language = Language.English),
           @Text(label = "冲击弓", language = Language.Chinese)
   })
   public final BooleanSetting crossbow = new BooleanSetting(false);

   @SettingInfo(name = {
           @Text(label = "Food", language = Language.English),
           @Text(label = "食物", language = Language.Chinese)
   })
   public final BooleanSetting food = new BooleanSetting(false);

   @SettingInfo(name = {
           @Text(label = "Potion", language = Language.English),
           @Text(label = "药水", language = Language.Chinese)
   })
   public final BooleanSetting potion = new BooleanSetting(false);

   public NoSlow() {
      registerSetting(mode, bow, crossbow, food, potion);
   }

   @EventTarget
   public void onSlow(EventSlow event) {
      if (Utils.isNull()) return;

      var gapple = (Gapple) Main.INSTANCE.moduleManager.getModule("Gapple");

      if (mode.is("Grim")) {
         if (mc.player.isUsingItem() || (gapple.isEnable() && gapple.eatTick != 0)) {
            Item currentItem = mc.player.getUseItem().getItem();

            boolean isBow = currentItem instanceof BowItem;
            boolean isCrossbow = currentItem instanceof CrossbowItem;
            boolean isFood = currentItem.getFoodProperties(mc.player.getUseItem(), mc.player) != null;
            boolean isPotion = currentItem instanceof PotionItem;

            if (isBow && bow.getValue()) {
               event.setSlowDown(!(mc.player.tickCount % 2 == 0));
            } else if (isCrossbow && crossbow.getValue()) {
               event.setSlowDown(!(mc.player.tickCount % 2 == 0));
            } else if (isFood && food.getValue() || isPotion && potion.getValue()) {
               event.setSlowDown(!(mc.player.getUseItemRemainingTicks() < 30 && mc.player.tickCount % 2 == 0));
            } else if (gapple.isEnable() && gapple.eatTick != 0) {
               event.setSlowDown(!(mc.player.getUseItemRemainingTicks() < 30 && mc.player.tickCount % 2 == 0));
            }

            mc.player.setSprinting(true);
         }
      }
   }

   @Override
   public String getTag() {
      return "Grim";
   }
}
