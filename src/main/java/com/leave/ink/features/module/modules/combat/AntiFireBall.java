package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.network.protocol.game.ServerboundInteractPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.projectile.Fireball;

@ModuleInfo(name = {
        @Text(label = "AntiFireBall", language = Language.English),
        @Text(label = "反火焰球", language = Language.Chinese)
}, category = Category.Combat)
public class AntiFireBall extends Module {
    private final TimeUtils timer = new TimeUtils();

    @Override
    public String getDescription() {
        return "自动反弹烈焰弹";
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof Fireball && mc.player.distanceTo(entity) < 5.5 && timer.hasTimeElapsed(300)) {
                mc.getConnection().send(ServerboundInteractPacket.createAttackPacket(entity, false));
                mc.player.swing(InteractionHand.MAIN_HAND);
                timer.reset();
                break;
            }
        }
    }
}
