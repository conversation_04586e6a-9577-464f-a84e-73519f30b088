package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventJump;
import com.leave.ink.events.EventStrafe;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.module.modules.world.Scaffold;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.utils.rotation.vector.Vector3d;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;

@ModuleInfo(name = {
        @Text(label = "TargetStrafe", language = Language.English),
        @Text(label = "目标移动", language = Language.Chinese)
}, category = Category.Movement)
public class TargetStrafe extends Module {
    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(1, 0.2, 6, "#.0");
    @SettingInfo(name = {
            @Text(label = "HoldJump", language = Language.English),
            @Text(label = "只有跳跃", language = Language.Chinese)
    })
    public final BooleanSetting holdJump = new BooleanSetting(true);
    private float yaw;
    public Entity target;
    private boolean left, colliding;
    public boolean active;

    public TargetStrafe() {
        registerSetting(range, holdJump);
    }

    @Override
    protected void onDisable() {
        active = false;
        target = null;
    }

    @EventTarget(0)
    public void onJump(EventJump event) {
        if (target != null && active) {
            event.setYaw(yaw);
        }
    }

    @EventTarget(0)
    public void onStrafe(EventStrafe event) {
        if (target != null && active) {
            event.setYaw(yaw);
        }
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        Scaffold scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");
        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");

        if (scaffold == null || scaffold.isEnable() || killAura == null || !killAura.isEnable()) {
            active = false;
            return;
        }

        active = true;

        if (holdJump.getValue() && !mc.player.onGround()) {
            target = null;
            return;
        }

        if (killAura.target == null) {
            target = null;
            return;
        }

        if (mc.player.horizontalCollision || !MovementUtils.isBlockUnder(5, false)) {
            if (!colliding) {
                MovementUtils.strafe((float) Math.hypot(mc.player.getDeltaMovement().x, mc.player.getDeltaMovement().z));
                left = !left;
            }
            colliding = true;
        } else {
            colliding = false;
        }

        target = killAura.target;
        if (target == null) {
            return;
        }

        float yaw = RotationUtils.calculate(target).getYaw() + (90 + 45) * (left ? -1 : 1);

        final double range = this.range.getValue().doubleValue();
        final double posX = -Mth.sin((float) Math.toRadians(yaw)) * range + target.getX();
        final double posZ = Mth.cos((float) Math.toRadians(yaw)) * range + target.getZ();
        yaw = RotationUtils.calculate(new Vector3d(posX, target.getY(), posZ)).getYaw();
        this.yaw = yaw;
    }
}
