package com.leave.ink.features.module.modules.other;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.SwordItem;

import java.util.ArrayList;
import java.util.List;

@ModuleInfo(name = {
        @Text(label = "KillerCheck", language = Language.English),
        @Text(label = "杀手检测", language = Language.Chinese)
}, category = Category.Other)
public class KillerCheck extends Module {
    @SettingInfo(name = {
            @Text(label = "DeBug", language = Language.English),
            @Text(label = "输出日记", language = Language.Chinese)
    })
    private final BooleanSetting debugValue = new BooleanSetting(true);
    private static final List<Entity> entityList = new ArrayList<>();
    private static final List<Entity> deBugName = new ArrayList<>();

    public KillerCheck() {
        registerSetting(debugValue);
    }

    @Override
    protected void onDisable() {
        entityList.clear();
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        entityList.clear();

    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        deBugName.clear();

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof Player player && player != mc.player) {
                if (player.getMainHandItem().getItem() instanceof SwordItem) {
                    if (!entityList.contains(player)) {
                        entityList.add(player);
                        deBugName.add(player);
                    }
                }
            }
        }

        if (!deBugName.isEmpty()) {
            for (Entity entity : deBugName) {
                if (debugValue.getValue()) {
                    ChatUtils.displayAlert("玩家:" + entity.getName().getString() + "是杀手");
                }
            }
        }
    }

    public static boolean isKiller(LivingEntity entity) {
        if (entity instanceof Player player) {
            return Main.INSTANCE.moduleManager.getModule("KillerCheck").isEnable() && entityList.contains(player);
        }
        return false;
    }
}
