package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.AutoArmor;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.utils.player.InventoryUtils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.network.protocol.game.ServerboundContainerClosePacket;
import net.minecraft.network.protocol.game.ServerboundPlayerCommandPacket;
import net.minecraft.world.inventory.ClickType;
import net.minecraft.world.item.*;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.block.*;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.level.material.Fluid;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;
import java.util.AbstractMap;

@ModuleInfo(name = {
        @Text(label = "InvClear", language = Language.English),
        @Text(label = "背包清理", language = Language.Chinese)
}, category = Category.World)
@SuppressWarnings("all")
public class InvClear extends Module {
    @SettingInfo(name = {
            @Text(label = "MaxDelay", language = Language.English),
            @Text(label = "最大延迟", language = Language.Chinese)
    })
    private final NumberSetting maxDelayValue = new NumberSetting(600.0, 0.0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "MinDelay", language = Language.English),
            @Text(label = "最小延迟", language = Language.Chinese)
    })
    private final NumberSetting minDelayValue = new NumberSetting(400.0, 0.0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "BlocksLimit", language = Language.English),
            @Text(label = "方块上限", language = Language.Chinese)
    })
    private final NumberSetting blockLimit = new NumberSetting(64, 64, 1000.0, "#");

    public int getBlockLimit() {
        return blockLimit.getValue().intValue();
    }
    @SettingInfo(name = {
            @Text(label = "InvOpen", language = Language.English),
            @Text(label = "只在背包界面", language = Language.Chinese)
    })
    private final BooleanSetting invOpenValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "InvPacket", language = Language.English),
            @Text(label = "模拟背包", language = Language.Chinese)
    })
    private final BooleanSetting simulateInventory = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "NoMove", language = Language.English),
            @Text(label = "无移动时", language = Language.Chinese)
    })
    private final BooleanSetting noMoveValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Sort", language = Language.English),
            @Text(label = "槽", language = Language.Chinese)
    })
    private final BooleanSetting sortValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "IgnoreMouseInput", language = Language.English),
            @Text(label = "忽略鼠标输入", language = Language.Chinese)
    })
    private final BooleanSetting ignoreMouseInput = new BooleanSetting(true);
    private final List<String> items = Arrays.asList("Ignore", "None", "Sword", "Bow", "PowerBow", "PunchBow", "Pickaxe", "Axe", "Shovel", "Hoe", "Food", "Block", "Water", "LavaBucket", "Gapple", "Pearl", "SlimeBall", "FireBall", "EndCrystal", "TNT", "CrossBow", "Throwable");
    @SettingInfo(name = {
            @Text(label = "Slot-1", language = Language.English),
            @Text(label = "槽1号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot1Value = new ModeSetting("Sword", items);
    @SettingInfo(name = {
            @Text(label = "Slot-2", language = Language.English),
            @Text(label = "槽2号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot2Value = new ModeSetting("Block", items);
    @SettingInfo(name = {
            @Text(label = "Slot-3", language = Language.English),
            @Text(label = "槽3号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot3Value = new ModeSetting("Gapple", items);
    @SettingInfo(name = {
            @Text(label = "Slot-4", language = Language.English),
            @Text(label = "槽4号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot4Value = new ModeSetting("Bow", items);
    @SettingInfo(name = {
            @Text(label = "Slot-5", language = Language.English),
            @Text(label = "槽5号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot5Value = new ModeSetting("Pearl", items);
    @SettingInfo(name = {
            @Text(label = "Slot-6", language = Language.English),
            @Text(label = "槽6号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot6Value = new ModeSetting("Ignore", items);
    @SettingInfo(name = {
            @Text(label = "Slot-7", language = Language.English),
            @Text(label = "槽7号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot7Value = new ModeSetting("Ignore", items);
    @SettingInfo(name = {
            @Text(label = "Slot-8", language = Language.English),
            @Text(label = "槽8号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot8Value = new ModeSetting("Pickaxe", items);
    @SettingInfo(name = {
            @Text(label = "Slot-9", language = Language.English),
            @Text(label = "槽9号", language = Language.Chinese)
    })
    private final ModeSetting sortSlot9Value = new ModeSetting("Axe", items);
    @SettingInfo(name = {
            @Text(label = "Slot-OFF", language = Language.English),
            @Text(label = "副手槽", language = Language.Chinese)
    })
    private final ModeSetting sortSlotOFFValue = new ModeSetting("Throwable", items);
    private final TimeUtils clickTimer = new TimeUtils();
    private long delay;
    boolean finishedSort = false;
    private final Set<Integer> slotsToDiscardForBlockLimit = new HashSet<>();

    public InvClear() {
        registerSetting(maxDelayValue, minDelayValue, invOpenValue, simulateInventory, noMoveValue, sortValue, sortSlot1Value, sortSlot2Value, sortSlot3Value, sortSlot4Value, sortSlot5Value, sortSlot6Value, sortSlot7Value, sortSlot8Value, sortSlot9Value, sortSlotOFFValue, blockLimit, ignoreMouseInput);
    }

    // 添加获取clickTimer的方法，供其他模块使用
    public TimeUtils getClickTimer() {
        return clickTimer;
    }

    /**
     * 获取是否忽略鼠标输入设置
     * @return 忽略鼠标输入设置
     */
    public BooleanSetting getIgnoreMouseInput() {
        return ignoreMouseInput;
    }

    /**
     * 获取整理是否已完成
     * @return 整理完成状态
     */
    public boolean isSortFinished() {
        return finishedSort;
    }

    @Override
    public String getSpaceName() {
        return "Inv Clear";
    }
    private AutoArmor autoArmor = null;

    @Override
    protected void onEnable() {
        super.onEnable();
        finishedSort = false;
        slotsToDiscardForBlockLimit.clear();
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {

        if (!clickTimer.hasTimeElapsed(delay) || (!(mc.screen instanceof InventoryScreen) && invOpenValue.getValue()) || (noMoveValue.getValue() && MovementUtils.isMoving()) || (mc.player.containerMenu != null && mc.player.containerMenu.containerId != 0)) {
            return;
        }

        // 确保AutoArmor先执行
        if (autoArmor == null) {
            autoArmor = ((AutoArmor) Main.INSTANCE.moduleManager.getModule("AutoArmor"));
        }

        if (autoArmor.isEnable()) {
            if (!autoArmor.isDone() || !autoArmor.getTimer().hasTimeElapsed(150)) {
                return;
            }
        }

        //排序
        if (sortValue.getValue()) {
            sortInventory();

            if (!finishedSort) {
                return;
            }
        }

        //丢弃
        slotsToDiscardForBlockLimit.clear();
        calculateSlotsToDiscardForBlockLimit();

        Map.Entry[] entries = new Map.Entry[mc.player.getInventory().getContainerSize()];
        int index = 0;

        for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
            if (entry.getKey() >= 36 && entry.getKey() <= 39) {
                continue;
            }

            if (!isUseful(entry.getValue(), entry.getKey().intValue())) {
                entries[index] = entry;
                index++;
            }
        }
        entries = Arrays.copyOf(entries, index);

        if (entries.length == 0) {
            return;
        }

        // 物品丢弃逻辑
        for (final Map.Entry<Integer, ItemStack> stackEntry : entries) {
            final int slot = stackEntry.getKey();
            final boolean openInventory = !(mc.screen instanceof InventoryScreen) && simulateInventory.getValue();

            // 设置基本延迟
            delay = TimeUtils.randomDelay(
                    minDelayValue.getValue().intValue(),
                    maxDelayValue.getValue().intValue()
            );

            // 执行物品丢弃操作
            if (openInventory)
                mc.player.connection.send(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.OPEN_INVENTORY));

            // 直接丢弃物品
            if (slot == 45) {
                mc.gameMode.handleInventoryMouseClick(mc.player.containerMenu.containerId, slot, 1, ClickType.THROW, mc.player);
            } else {
                mc.gameMode.handleInventoryMouseClick(mc.player.containerMenu.containerId, slot < 9 ? slot + 36 : slot, 1, ClickType.THROW, mc.player);
            }

            if (openInventory)
                mc.player.connection.send(new ServerboundContainerClosePacket(mc.player.containerMenu.containerId));

            clickTimer.reset();
            break;
        }
    }

    public boolean isUseful(ItemStack itemStack, int slot) {
        final Item item = itemStack.getItem();

        if (item instanceof SwordItem) {
            int configuredCount = getConfiguredSlotCountForItemType("Sword");
            if (configuredCount == 0) return false;

            if (slot == -1) {
                List<RankedItemStack> existingSwords = getAllItemsOfType(SwordItem.class, true);
                float newItemScore = getToolScore(itemStack);

                if (existingSwords.size() < configuredCount) {
                    return true;
                }

                existingSwords.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                if (existingSwords.size() >= configuredCount) {
                    if (newItemScore > existingSwords.get(configuredCount - 1).getScore()) {
                        return true;
                    }
                }
                return false;
            } else {
                List<RankedItemStack> allSwordsRanked = getAllItemsOfType(SwordItem.class, true);
                allSwordsRanked.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                final int maxToKeep = configuredCount;
                boolean foundInTopK = false;
                for (int i = 0; i < Math.min(maxToKeep, allSwordsRanked.size()); i++) {
                    RankedItemStack topSwordCandidate = allSwordsRanked.get(i);
                    if (topSwordCandidate.getSlot() == slot) {
                        foundInTopK = true;
                        break;
                    }
                }
                return foundInTopK;
            }
        } else if (item instanceof AxeItem) {
            int configuredCount = getConfiguredSlotCountForItemType("Axe");
            int minToKeep = Math.max(configuredCount, 1);

            if (slot == -1) { // 来自ChestStealer
                List<RankedItemStack> existingAxes = getAllItemsOfType(AxeItem.class, true);
                float newItemScore = getToolScore(itemStack);

                if (existingAxes.size() < minToKeep) {
                    return true;
                }
                existingAxes.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                if (newItemScore > existingAxes.get(minToKeep - 1).getScore()) {
                    return true;
                }
                return false;
            } else { // 物品已在玩家物品栏中
                List<RankedItemStack> allAxesRanked = getAllItemsOfType(AxeItem.class, true);
                allAxesRanked.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());

                boolean foundInTopK = false;
                for (int i = 0; i < Math.min(minToKeep, allAxesRanked.size()); i++) {
                    RankedItemStack topAxeCandidate = allAxesRanked.get(i);
                    if (topAxeCandidate.getSlot() == slot) {
                        foundInTopK = true;
                        break;
                    }
                }
                return foundInTopK;
            }
        } else if (item instanceof PickaxeItem) {
            final float currentItemScore = getToolScore(itemStack);
            List<RankedItemStack> existingPickaxesInventory = getAllItemsOfType(PickaxeItem.class, true);

            int configuredCount = getConfiguredSlotCountForItemType("Pickaxe");
            int minToKeep = Math.max(configuredCount, 1);

            if (slot == -1) { // 来自ChestStealer
                if (existingPickaxesInventory.size() < minToKeep) {
                    return true;
                }

                existingPickaxesInventory.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                if (currentItemScore > existingPickaxesInventory.get(minToKeep - 1).getScore()) {
                    return true;
                }
                return false;

            } else {
                List<RankedItemStack> allPickaxesRanked = getAllItemsOfType(PickaxeItem.class, true);
                allPickaxesRanked.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                boolean foundInTopK = false;
                for (int i = 0; i < Math.min(minToKeep, allPickaxesRanked.size()); i++) {
                    RankedItemStack topPickaxeCandidate = allPickaxesRanked.get(i);
                    if (topPickaxeCandidate.getSlot() == slot) {
                        foundInTopK = true;
                        break;
                    }
                }
                return foundInTopK;
            }
        } else if (item instanceof ShovelItem) {
            int configuredCount = getConfiguredSlotCountForItemType("Shovel");
            int minToKeep = Math.max(configuredCount, 1);

            if (slot == -1) { // 来自ChestStealer
                List<RankedItemStack> existingShovels = getAllItemsOfType(ShovelItem.class, true);
                float newItemScore = getToolScore(itemStack);

                if (existingShovels.size() < minToKeep) {
                    return true;
                }

                existingShovels.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                if (newItemScore > existingShovels.get(minToKeep - 1).getScore()) {
                    return true;
                }
                return false;

            } else {
                List<RankedItemStack> allShovelsRanked = getAllItemsOfType(ShovelItem.class, true);
                allShovelsRanked.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                boolean foundInTopK = false;
                for (int i = 0; i < Math.min(minToKeep, allShovelsRanked.size()); i++) {
                    RankedItemStack topShovelCandidate = allShovelsRanked.get(i);
                    if (topShovelCandidate.getSlot() == slot) {
                        foundInTopK = true;
                        break;
                    }
                }
                return foundInTopK;
            }
        } else if (item instanceof HoeItem) {
            int configuredCount = getConfiguredSlotCountForItemType("Hoe");
            int minToKeep = Math.max(configuredCount, 1);

            if (slot == -1) { // 来自ChestStealer
                List<RankedItemStack> existingHoes = getAllItemsOfType(HoeItem.class, true);
                float newItemScore = getToolScore(itemStack);

                if (existingHoes.size() < minToKeep) {
                    return true;
                }

                existingHoes.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                if (newItemScore > existingHoes.get(minToKeep - 1).getScore()) {
                    return true;
                }
                return false;

            } else {
                List<RankedItemStack> allHoesRanked = getAllItemsOfType(HoeItem.class, true);
                allHoesRanked.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                boolean foundInTopK = false;
                for (int i = 0; i < Math.min(minToKeep, allHoesRanked.size()); i++) {
                    RankedItemStack topHoeCandidate = allHoesRanked.get(i);
                    if (topHoeCandidate.getSlot() == slot) {
                        foundInTopK = true;
                        break;
                    }
                }
                return foundInTopK;
            }
        } else if (item instanceof CrossbowItem) {
            for (final ItemStack anotherStack : getItems(0, mc.player.getInventory().items.size()).values()) {
                if (itemStack.equals(anotherStack) || !(anotherStack.getItem() instanceof CrossbowItem))
                    continue;

                if (getBestCrossBow(anotherStack) >= getBestCrossBow(itemStack))
                    return false;
            }
            return true;
        } else if (item instanceof BowItem) {
            final int bowPower = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, itemStack);
            final int bowPunch = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, itemStack);
            int powerBowCount = getConfiguredSlotCountForItemType("PowerBow");
            int punchBowCount = getConfiguredSlotCountForItemType("PunchBow");
            int regularBowCount = getConfiguredSlotCountForItemType("Bow");
            int minBowsToKeep = Math.max(1, regularBowCount);
            if (slot == -1) {
                List<RankedItemStack> existingBows = new ArrayList<>();

                for (int i = 0; i < mc.player.getInventory().items.size(); i++) {
                    ItemStack invStack = mc.player.getInventory().getItem(i);
                    if (invStack.getItem() instanceof BowItem) {
                        existingBows.add(new RankedItemStack(invStack, i, getToolScore(invStack)));
                    }
                }
                ItemStack offhandItem = mc.player.getInventory().offhand.get(0);
                if (offhandItem.getItem() instanceof BowItem) {
                    existingBows.add(new RankedItemStack(offhandItem, 45, getToolScore(offhandItem)));
                }

                float newBowScore = getToolScore(itemStack);

                if (regularBowCount > 0) {
                    existingBows.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                    if (existingBows.size() < minBowsToKeep) {
                        return true;
                    } else if (existingBows.size() >= minBowsToKeep && newBowScore > existingBows.get(minBowsToKeep - 1).getScore()) {
                        return true;
                    }
                }
                if (bowPower > 0 && powerBowCount > 0) {
                    int bestPowerLevel = 0;
                    for (RankedItemStack bow : existingBows) {
                        int power = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, bow.getItemStack());
                        if (power > bestPowerLevel) {
                            bestPowerLevel = power;
                        }
                    }
                    if (bowPower > bestPowerLevel || (bowPower > 0 && bestPowerLevel == 0)) {
                        return true;
                    }
                }
                if (bowPunch > 0 && punchBowCount > 0) {
                    int bestPunchLevel = 0;
                    for (RankedItemStack bow : existingBows) {
                        int punch = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, bow.getItemStack());
                        if (punch > bestPunchLevel) {
                            bestPunchLevel = punch;
                        }
                    }
                    if (bowPunch > bestPunchLevel || (bowPunch > 0 && bestPunchLevel == 0)) {
                        return true;
                    }
                }

                return false;
            } else {
                Map<Integer, ItemStack> allItems = getInventoryItemsWithOffhand();
                List<RankedItemStack> allBows = new ArrayList<>();
                for (Map.Entry<Integer, ItemStack> entry : allItems.entrySet()) {
                    if (entry.getValue().getItem() instanceof BowItem) {
                        int slotId = entry.getKey();
                        ItemStack bowStack = entry.getValue();
                        float score = getToolScore(bowStack);
                        allBows.add(new RankedItemStack(bowStack, slotId, score));
                    }
                }
                allBows.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());
                if (regularBowCount > 0) {
                    for (int i = 0; i < Math.min(regularBowCount, allBows.size()); i++) {
                        if (allBows.get(i).getSlot() == slot) {
                            return true;
                        }
                    }
                }
                if (bowPower > 0 && powerBowCount > 0) {
                    List<RankedItemStack> powerBows = new ArrayList<>();
                    for (RankedItemStack bow : allBows) {
                        int power = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, bow.getItemStack());
                        if (power > 0) {
                            powerBows.add(bow);
                        }
                    }

                    powerBows.sort(Comparator.comparingInt((RankedItemStack b) ->
                            EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, b.getItemStack())).reversed());

                    for (int i = 0; i < Math.min(powerBowCount, powerBows.size()); i++) {
                        if (powerBows.get(i).getSlot() == slot) {
                            return true;
                        }
                    }
                }
                if (bowPunch > 0 && punchBowCount > 0) {
                    List<RankedItemStack> punchBows = new ArrayList<>();
                    for (RankedItemStack bow : allBows) {
                        int punch = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, bow.getItemStack());
                        if (punch > 0) {
                            punchBows.add(bow);
                        }
                    }

                    punchBows.sort(Comparator.comparingInt((RankedItemStack b) ->
                            EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, b.getItemStack())).reversed());

                    for (int i = 0; i < Math.min(punchBowCount, punchBows.size()); i++) {
                        if (punchBows.get(i).getSlot() == slot) {
                            return true;
                        }
                    }
                }
                if (regularBowCount == 0 && powerBowCount == 0 && punchBowCount == 0) {
                    for (int i = 0; i < Math.min(1, allBows.size()); i++) {
                        if (allBows.get(i).getSlot() == slot) {
                            return true;
                        }
                    }
                }

                return false;
            }
        } else if (item instanceof ArmorItem) {
            if(slot == -1) return false;
            int[] slots = AutoArmor.getBestArmor();
            for (int i : slots) {
                if(i<0) continue;
                if(slot == i) return true;
            }
            return false;
        } else if (item instanceof BucketItem bucket) {
            Fluid fluidType = bucket.getFluid();
            if (fluidType == Fluids.WATER) {
                List<Integer> waterBucketSlots = new ArrayList<>();
                Map<Integer, ItemStack> allItems = getInventoryItemsWithOffhand();
                for (Map.Entry<Integer, ItemStack> entry : allItems.entrySet()) {
                    Item currentItem = entry.getValue().getItem();
                    if (currentItem instanceof BucketItem && ((BucketItem)currentItem).getFluid() == Fluids.WATER) {
                        waterBucketSlots.add(entry.getKey());
                    }
                }

                if (slot == -1) {
                    return waterBucketSlots.isEmpty();
                } else {
                    if (waterBucketSlots.isEmpty()) return true;
                    waterBucketSlots.sort(Comparator.naturalOrder());
                    return waterBucketSlots.get(0).equals(slot);
                }
            } else if (fluidType == Fluids.LAVA) {
                List<Integer> lavaBucketSlots = new ArrayList<>();
                Map<Integer, ItemStack> allItems = getInventoryItemsWithOffhand();
                for (Map.Entry<Integer, ItemStack> entry : allItems.entrySet()) {
                    Item currentItem = entry.getValue().getItem();
                    if (currentItem instanceof BucketItem && ((BucketItem)currentItem).getFluid() == Fluids.LAVA) {
                        lavaBucketSlots.add(entry.getKey());
                    }
                }

                if (slot == -1) {
                    return lavaBucketSlots.isEmpty();
                } else {
                    if (lavaBucketSlots.isEmpty()) return true;
                    lavaBucketSlots.sort(Comparator.naturalOrder());
                    return lavaBucketSlots.get(0).equals(slot);
                }
            }
            return true;
        } else if (item instanceof BlockItem) {
            Block block = ((BlockItem) item).getBlock();

            // 处理铁砧
            if (block instanceof AnvilBlock) {
                if(slot == -1) {
                    boolean hasAnvil = false;
                    for(ItemStack stack : mc.player.getInventory().items) {
                        if(stack.getItem() instanceof BlockItem && ((BlockItem) stack.getItem()).getBlock() instanceof AnvilBlock) {
                            hasAnvil = true;
                            break;
                        }
                    }
                    return !hasAnvil;
                } else {
                    List<Integer> anvilSlots = new ArrayList<>();
                    List<ItemStack> anvils = new ArrayList<>();

                    for(int i = 0; i < mc.player.getInventory().items.size(); i++) {
                        ItemStack stack = mc.player.getInventory().getItem(i);
                        if(stack.getItem() instanceof BlockItem && ((BlockItem) stack.getItem()).getBlock() instanceof AnvilBlock) {
                            anvilSlots.add(i);
                            anvils.add(stack);
                        }
                    }
                    if(anvils.size() <= 1) return true;
                    int bestDurability = -1;
                    int bestAnvilSlot = -1;

                    for(int i = 0; i < anvils.size(); i++) {
                        ItemStack anvil = anvils.get(i);
                        int durability = anvil.getMaxDamage() - anvil.getDamageValue();

                        if(durability > bestDurability) {
                            bestDurability = durability;
                            bestAnvilSlot = anvilSlots.get(i);
                        }
                    }

                    return slot == bestAnvilSlot;
                }
            }

            if (block instanceof EnchantmentTableBlock || block instanceof FurnaceBlock || block instanceof CraftingTableBlock || block == Blocks.HOPPER || block instanceof ChestBlock)
                return false;

            // 判断无用的方块物品
            if (item == Items.WHEAT_SEEDS ||
                    item instanceof BlockItem && (
                            // 花类物品
                            block instanceof FlowerBlock ||
                                    block instanceof TallFlowerBlock ||
                                    // 压力板
                                    block instanceof PressurePlateBlock ||
                                    block instanceof WeightedPressurePlateBlock ||
                                    // 门
                                    block instanceof DoorBlock ||
                                    // 活版门
                                    block instanceof TrapDoorBlock ||
                                    // 栅栏相关
                                    block instanceof FenceBlock ||
                                    block instanceof FenceGateBlock ||
                                    // 墙类
                                    block instanceof WallBlock ||
                                    // 地毯
                                    block instanceof CarpetBlock ||
                                    // 楼梯类
                                    block instanceof StairBlock ||
                                    // 红石相关
                                    block instanceof RedStoneWireBlock ||
                                    block instanceof RedstoneTorchBlock ||
                                    block instanceof ComparatorBlock ||
                                    block instanceof RepeaterBlock ||
                                    block instanceof ObserverBlock ||
                                    block instanceof ButtonBlock ||
                                    block instanceof LeverBlock ||
                                    block instanceof TripWireBlock ||
                                    block instanceof TripWireHookBlock
                    )) {
                return false;
            }
            if(block == Blocks.COBWEB) return true;
            if(block == Blocks.TNT) return true;

            if (InventoryUtils.isBlocked(block)) {
                return false;
            }
            if (slotsToDiscardForBlockLimit.contains(slot)) {
                return false;
            }
            return true;
        }
        else {
            if(item instanceof FishingRodItem) {
                if(slot == -1) {
                    boolean hasFishingRod = false;
                    for(ItemStack stack : mc.player.getInventory().items) {
                        if(stack.getItem() instanceof FishingRodItem) {
                            hasFishingRod = true;
                            break;
                        }
                    }
                    return !hasFishingRod;
                } else {
                    List<Integer> fishingRodSlots = new ArrayList<>();
                    List<ItemStack> fishingRods = new ArrayList<>();

                    for(int i = 0; i < mc.player.getInventory().items.size(); i++) {
                        ItemStack stack = mc.player.getInventory().getItem(i);
                        if(stack.getItem() instanceof FishingRodItem) {
                            fishingRodSlots.add(i);
                            fishingRods.add(stack);
                        }
                    }

                    if(fishingRods.size() <= 1) return true;
                    int bestDurability = -1;
                    int bestRodSlot = -1;

                    for(int i = 0; i < fishingRods.size(); i++) {
                        ItemStack rod = fishingRods.get(i);
                        int durability = rod.getMaxDamage() - rod.getDamageValue();

                        if(durability > bestDurability) {
                            bestDurability = durability;
                            bestRodSlot = fishingRodSlots.get(i);
                        }
                    }
                    return slot == bestRodSlot;
                }
            }

            return item == Items.ARROW || item instanceof PotionItem ||
                    item instanceof EnderpearlItem || item instanceof BucketItem ||
                    item == Items.GOLDEN_APPLE || item == Items.ENCHANTED_GOLDEN_APPLE ||
                    item == Items.FIRE_CHARGE || item == Items.END_CRYSTAL ||
                    item == Items.SLIME_BALL || item == Items.TNT ||
                    item == Items.EGG || item == Items.SNOWBALL ||
                    item == Items.TOTEM_OF_UNDYING || item == Items.EXPERIENCE_BOTTLE;
        }
    }

    private void sortInventory() {
        if (!sortValue.getValue()) {
            finishedSort = true;
            return;
        }

        final ItemStack currentOffhandStack = mc.player.getInventory().offhand.get(0);
        final SortCallback offhandSortCallback = searchSortItem(45, currentOffhandStack);
        boolean needsSortForOffhand = offhandSortCallback != null &&
                offhandSortCallback.getItemSlot() != 45 &&
                (offhandSortCallback.isReplaceCurrentItem() || currentOffhandStack.isEmpty());

        if (needsSortForOffhand) {
            final boolean openInventory = !(mc.screen instanceof InventoryScreen) && simulateInventory.getValue();
            if (openInventory) mc.player.connection.send(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.OPEN_INVENTORY));

            int sourceItemSlot = offhandSortCallback.getItemSlot();
            int clickSourceSlot = sourceItemSlot < 9 ? sourceItemSlot + 36 : sourceItemSlot;

            mc.gameMode.handleInventoryMouseClick(
                    0,
                    clickSourceSlot,
                    40,
                    ClickType.SWAP,
                    mc.player
            );

            if (openInventory) mc.player.connection.send(new ServerboundContainerClosePacket(mc.player.containerMenu.containerId));

            clickTimer.reset();
            delay = TimeUtils.randomDelay(minDelayValue.getValue().intValue(), maxDelayValue.getValue().intValue());
            finishedSort = false;
            return;
        }

        int settledSlotsCount = 0;
        if (!needsSortForOffhand) {
            settledSlotsCount++;
        }

        for (int targetHotbarSlot = 0; targetHotbarSlot < 9; targetHotbarSlot++) {
            final ItemStack currentHotbarStack = mc.player.getInventory().getItem(targetHotbarSlot);
            final SortCallback hotbarSortCallback = searchSortItem(targetHotbarSlot, currentHotbarStack);
            boolean needsSortForHotbar = hotbarSortCallback != null &&
                    hotbarSortCallback.getItemSlot() != targetHotbarSlot &&
                    (hotbarSortCallback.isReplaceCurrentItem() || currentHotbarStack.isEmpty());

            if (needsSortForHotbar) {
                final boolean openInventory = !(mc.screen instanceof InventoryScreen) && simulateInventory.getValue();
                if (openInventory) mc.player.connection.send(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.OPEN_INVENTORY));

                int sourceItemActualSlot = hotbarSortCallback.getItemSlot();
                int clickSourceArg;
                int clickButtonArg;

                if (sourceItemActualSlot == 45) {
                    clickSourceArg = targetHotbarSlot + 36;
                    clickButtonArg = 40;
                } else {
                    clickSourceArg = sourceItemActualSlot < 9 ? sourceItemActualSlot + 36 : sourceItemActualSlot;
                    clickButtonArg = targetHotbarSlot;
                }

                mc.gameMode.handleInventoryMouseClick(
                        0,
                        clickSourceArg,
                        clickButtonArg,
                        ClickType.SWAP,
                        mc.player
                );

                if (openInventory) mc.player.connection.send(new ServerboundContainerClosePacket(mc.player.containerMenu.containerId));

                clickTimer.reset();
                delay = TimeUtils.randomDelay(minDelayValue.getValue().intValue(), maxDelayValue.getValue().intValue());
                finishedSort = false;
                return;
            }

            if (!needsSortForHotbar) {
                settledSlotsCount++;
            }
        }

        finishedSort = (settledSlotsCount == 10);
    }

    private String getType(final int targetSlot) {
        return switch (targetSlot) {
            case 0 -> sortSlot1Value.getValue();
            case 1 -> sortSlot2Value.getValue();
            case 2 -> sortSlot3Value.getValue();
            case 3 -> sortSlot4Value.getValue();
            case 4 -> sortSlot5Value.getValue();
            case 5 -> sortSlot6Value.getValue();
            case 6 -> sortSlot7Value.getValue();
            case 7 -> sortSlot8Value.getValue();
            case 8 -> sortSlot9Value.getValue();
            case 45 -> sortSlotOFFValue.getValue();
            default -> "";
        };
    }

    private SortCallback searchSortItem(final int targetSlot, final ItemStack slotStack) {
        final String targetType = getType(targetSlot);

        switch (targetType.toLowerCase()) {
            case "sword":
            case "axe": {
                boolean isSword = targetType.toLowerCase().equals("sword");
                Class<? extends Item> itemClass = isSword ? SwordItem.class : AxeItem.class;

                int desiredRank = 1;
                if (targetSlot >= 0 && targetSlot < 9) {
                    for (int i = 0; i < targetSlot; i++) {
                        if (getType(i).equalsIgnoreCase(targetType)) {
                            desiredRank++;
                        }
                    }
                } else if (targetSlot == 45) {
                    for (int i = 0; i < 9; i++) {
                        if (getType(i).equalsIgnoreCase(targetType)) {
                            desiredRank++;
                        }
                    }
                }

                List<RankedItemStack> allItemsOfType = getAllItemsOfType(itemClass, true);
                allItemsOfType.sort(Comparator.comparingDouble(RankedItemStack::getScore).reversed());

                if (allItemsOfType.size() >= desiredRank) {
                    RankedItemStack nthBestItem = allItemsOfType.get(desiredRank - 1);
                    if (nthBestItem.getSlot() == targetSlot) return null;
                    boolean shouldReplace = slotStack.isEmpty() ||
                            slotStack.getItem().getClass() != itemClass ||
                            (getToolScore(slotStack) < nthBestItem.getScore());

                    return new SortCallback(nthBestItem.getSlot(), shouldReplace);
                }
                return null;
            }
            case "pickaxe": {
                int bestTool = -1;
                float bestScore = -1f;

                List<RankedItemStack> allPickaxes = getAllItemsOfType(PickaxeItem.class, false);

                for (RankedItemStack pickaxe : allPickaxes) {
                    if (bestTool == -1 || pickaxe.getScore() > bestScore) {
                        bestScore = pickaxe.getScore();
                        bestTool = pickaxe.getSlot();
                    }
                }

                if (bestTool != -1 && bestTool != targetSlot) {
                    boolean shouldReplace = slotStack.isEmpty() ||
                            !(slotStack.getItem() instanceof PickaxeItem) ||
                            (getToolScore(slotStack) < bestScore);
                    return new SortCallback(bestTool, shouldReplace);
                }
                return null;
            }
            case "shovel": {
                int bestTool = -1;
                float bestScore = -1f;

                List<RankedItemStack> allShovels = getAllItemsOfType(ShovelItem.class, false);

                for (RankedItemStack shovel : allShovels) {
                    if (bestTool == -1 || shovel.getScore() > bestScore) {
                        bestScore = shovel.getScore();
                        bestTool = shovel.getSlot();
                    }
                }

                if (bestTool != -1 && bestTool != targetSlot) {
                    boolean shouldReplace = slotStack.isEmpty() ||
                            !(slotStack.getItem() instanceof ShovelItem) ||
                            (getToolScore(slotStack) < bestScore);
                    return new SortCallback(bestTool, shouldReplace);
                }
                return null;
            }
            case "hoe": {
                int bestTool = -1;
                float bestScore = -1f;

                List<RankedItemStack> allHoes = getAllItemsOfType(HoeItem.class, false);

                for (RankedItemStack hoe : allHoes) {
                    if (bestTool == -1 || hoe.getScore() > bestScore) {
                        bestScore = hoe.getScore();
                        bestTool = hoe.getSlot();
                    }
                }

                if (bestTool != -1 && bestTool != targetSlot) {
                    boolean shouldReplace = slotStack.isEmpty() ||
                            !(slotStack.getItem() instanceof HoeItem) ||
                            (getToolScore(slotStack) < bestScore);
                    return new SortCallback(bestTool, shouldReplace);
                }
                return null;
            }
            case "crossbow": {
                int bestWeapon = -1;
                float bestScore = -1f;
                List<RankedItemStack> allCrossbows = getAllItemsOfType(CrossbowItem.class, true);
                for(RankedItemStack crossbow : allCrossbows) {
                    if(bestWeapon == -1 || crossbow.getScore() > bestScore) {
                        bestScore = crossbow.getScore();
                        bestWeapon = crossbow.getSlot();
                    }
                }

                if (bestWeapon != -1 && bestWeapon != targetSlot) {
                    boolean shouldReplace = slotStack.isEmpty() ||
                            !(slotStack.getItem() instanceof CrossbowItem) ||
                            (getToolScore(slotStack) < bestScore);
                    return new SortCallback(bestWeapon, shouldReplace);
                }
                return null;
            }
            case "bow": {
                List<Map.Entry<Integer, Float>> allBowsWithScores = new ArrayList<>();

                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    if (entry.getValue().getItem() instanceof BowItem) {
                        int slotId = entry.getKey();
                        ItemStack bowStack = entry.getValue();
                        float bowScore = getToolScore(bowStack);
                        String slotType = getType(slotId);
                        if (slotType.equalsIgnoreCase("powerbow") || slotType.equalsIgnoreCase("punchbow")) {
                            continue;
                        }

                        allBowsWithScores.add(new AbstractMap.SimpleEntry<>(slotId, bowScore));
                    }
                }
                allBowsWithScores.sort((a, b) -> Float.compare(b.getValue(), a.getValue()));
                if (!allBowsWithScores.isEmpty()) {
                    int bestBow = allBowsWithScores.get(0).getKey();
                    float bestScore = allBowsWithScores.get(0).getValue();

                    if (bestBow != targetSlot) {
                        boolean shouldReplace = slotStack.isEmpty() ||
                                !(slotStack.getItem() instanceof BowItem) ||
                                getToolScore(slotStack) < bestScore;

                        return new SortCallback(bestBow, shouldReplace);
                    }
                }
                return null;
            }
            case "food": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem().isEdible() && !getType(sourceSlot).equalsIgnoreCase("food"))
                        return new SortCallback(sourceSlot, slotStack == null || !(slotStack.getItem().isEdible()));
                }
                break;
            }
            case "block": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (!itemStack.is(Items.TNT))
                        if (itemStack != null && itemStack.getItem() instanceof BlockItem && !getType(sourceSlot).equalsIgnoreCase("block")) {
                            Block block = ((BlockItem)itemStack.getItem()).getBlock();
                            if (block instanceof AnvilBlock || block instanceof EnchantmentTableBlock || block instanceof FurnaceBlock || block instanceof CraftingTableBlock || block == Blocks.COBWEB || block == Blocks.HOPPER || block instanceof ChestBlock || InventoryUtils.isBlocked(block))
                                continue;
                            return new SortCallback(sourceSlot, slotStack == null || !(slotStack.getItem() instanceof BlockItem));
                        }
                }
                break;
            }
            case "water": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() instanceof BucketItem && ((BucketItem) itemStack.getItem()).getFluid() == Fluids.WATER && !getType(sourceSlot).equalsIgnoreCase("water"))
                        return new SortCallback(sourceSlot, slotStack == null || slotStack.isEmpty() || !(slotStack.getItem() instanceof BucketItem && ((BucketItem) slotStack.getItem()).getFluid() == Fluids.WATER));
                }
                break;
            }
            case "lavabucket": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    Fluid fluid = null;
                    if (itemStack != null && itemStack.getItem() instanceof BucketItem) {
                        fluid = ((BucketItem) itemStack.getItem()).getFluid();
                    }
                    if (fluid == Fluids.LAVA && !getType(sourceSlot).equalsIgnoreCase("lavabucket")) {
                        boolean shouldReplace = slotStack == null || slotStack.isEmpty() ||
                                !(slotStack.getItem() instanceof BucketItem && ((BucketItem) slotStack.getItem()).getFluid() == Fluids.LAVA);
                        return new SortCallback(sourceSlot, shouldReplace);
                    }
                }
                break;
            }
            case "pearl": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() instanceof EnderpearlItem && !getType(sourceSlot).equalsIgnoreCase("pearl"))
                        return new SortCallback(sourceSlot, slotStack == null || !(slotStack.getItem() instanceof EnderpearlItem));
                }
                break;
            }
            case "slimeball": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() == Items.SLIME_BALL && !getType(sourceSlot).equalsIgnoreCase(targetType)) {
                        return new SortCallback(sourceSlot, true);
                    }
                }
                return null;
            }
            case "gapple": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() == Items.GOLDEN_APPLE && !getType(sourceSlot).equalsIgnoreCase("gapple"))
                        return new SortCallback(sourceSlot, slotStack == null || slotStack.getItem() != Items.GOLDEN_APPLE);
                }
                break;
            }
            case "fireball": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() == Items.FIRE_CHARGE && !getType(sourceSlot).equalsIgnoreCase("fireball"))
                        return new SortCallback(sourceSlot, slotStack == null || slotStack.getItem() != Items.FIRE_CHARGE);
                }
                break;
            }
            case "endcrystal": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() == Items.END_CRYSTAL && !getType(sourceSlot).equalsIgnoreCase("endcrystal"))
                        return new SortCallback(sourceSlot, slotStack == null || slotStack.getItem() != Items.END_CRYSTAL);
                }
                break;
            }
            case "tnt": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();
                    if (itemStack != null && itemStack.getItem() == Items.TNT && !getType(sourceSlot).equalsIgnoreCase("tnt"))
                        return new SortCallback(sourceSlot, slotStack == null || slotStack.getItem() != Items.TNT);
                }
                break;
            }
            case "throwable": {
                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int sourceSlot = entry.getKey();
                    ItemStack itemStack = entry.getValue();

                    if (itemStack != null && (itemStack.getItem() == Items.SNOWBALL || itemStack.getItem() == Items.EGG)) {
                        if (sourceSlot != targetSlot && !getType(sourceSlot).equalsIgnoreCase("throwable")) {
                            // slotStack is the item currently in the targetSlot (e.g., offhand)
                            boolean shouldReplace = slotStack.isEmpty() ||
                                    !(slotStack.getItem() == Items.SNOWBALL || slotStack.getItem() == Items.EGG);
                            return new SortCallback(sourceSlot, shouldReplace);
                        }
                    }
                }
                break;
            }
            case "powerbow": {
                int bestPowerBow = -1;
                int bestPowerLevel = -1;

                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int slot = entry.getKey();
                    ItemStack stack = entry.getValue();

                    if (stack.getItem() instanceof BowItem) {
                        int powerLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, stack);
                        if (powerLevel > 0 && (bestPowerBow == -1 || powerLevel > bestPowerLevel)) {
                            bestPowerBow = slot;
                            bestPowerLevel = powerLevel;
                        }
                    }
                }

                if (bestPowerBow != -1 && bestPowerBow != targetSlot) {
                    boolean shouldReplace = slotStack.isEmpty() ||
                            !(slotStack.getItem() instanceof BowItem) ||
                            EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, slotStack) < bestPowerLevel;

                    return new SortCallback(bestPowerBow, shouldReplace);
                }
                return null;
            }
            case "punchbow": {
                int bestPunchBow = -1;
                int bestPunchLevel = -1;

                for (Map.Entry<Integer, ItemStack> entry : getInventoryItemsWithOffhand().entrySet()) {
                    int slot = entry.getKey();
                    ItemStack stack = entry.getValue();

                    if (stack.getItem() instanceof BowItem) {
                        int punchLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, stack);
                        if (punchLevel > 0 && (bestPunchBow == -1 || punchLevel > bestPunchLevel)) {
                            bestPunchBow = slot;
                            bestPunchLevel = punchLevel;
                        }
                    }
                }

                if (bestPunchBow != -1 && bestPunchBow != targetSlot) {
                    boolean shouldReplace = slotStack.isEmpty() ||
                            !(slotStack.getItem() instanceof BowItem) ||
                            EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, slotStack) < bestPunchLevel;

                    return new SortCallback(bestPunchBow, shouldReplace);
                }
                return null;
            }
        }

        return null;
    }

    private Map<Integer, ItemStack> getInventoryItemsWithOffhand() {
        final Map<Integer, ItemStack> itemsMap = new HashMap<>();

        for (int i = 0; i < mc.player.getInventory().items.size(); i++) {
            ItemStack itemStack = mc.player.getInventory().getItem(i);
            if (!itemStack.isEmpty()) {
                itemsMap.put(i, itemStack);
            }
        }

        ItemStack offhandItem = mc.player.getInventory().offhand.get(0);
        if (!offhandItem.isEmpty()) {
            itemsMap.put(45, offhandItem);
        }
        return itemsMap;
    }

    public List<RankedItemStack> getAllItemsOfType(Class<? extends Item> itemType, boolean useEnchantScore) {
        List<RankedItemStack> foundItems = new ArrayList<>();
        Map<Integer, ItemStack> inventory = getInventoryItemsWithOffhand();

        for (Map.Entry<Integer, ItemStack> entry : inventory.entrySet()) {
            ItemStack stack = entry.getValue();
            int slot = entry.getKey();
            if (itemType.isInstance(stack.getItem())) {
                foundItems.add(new RankedItemStack(stack, slot, getToolScore(stack)));
            }
        }
        return foundItems;
    }

    private Map<Integer, ItemStack> getItems(final int start, final int end) {
        final Map<Integer, ItemStack> itemsMap = new HashMap<>();

        for (int i = start; i < end; i++) {
            final ItemStack itemStack = mc.player.getInventory().getItem(i);
            if (i >= 0 && i <= 8 && getType(i).equalsIgnoreCase("ignore"))
                continue;

            if (!itemStack.isEmpty()) {
                itemsMap.put(i, itemStack);
            }
        }

        ItemStack offhandItem = mc.player.getInventory().offhand.get(0);
        if (!offhandItem.isEmpty() && getType(45).equalsIgnoreCase("ignore")) {
            itemsMap.put(45, offhandItem);
        }

        return itemsMap;
    }

    public class SortCallback {
        private final int itemSlot;
        private final boolean replaceCurrentItem;

        public SortCallback(final int itemSlot, final boolean replaceCurrentItem) {
            this.itemSlot = itemSlot;
            this.replaceCurrentItem = replaceCurrentItem;
        }

        public int getItemSlot() {
            return itemSlot;
        }

        public boolean isReplaceCurrentItem() {
            return replaceCurrentItem;
        }
    }

    private float getBestItem(ItemStack itemStack) {
        float prot = 1f;

        if (itemStack.getDescriptionId().contains("wooden")) {
            prot = 1f;
        } else if (itemStack.getDescriptionId().contains("golden")) {
            prot = 2f;
        } else if (itemStack.getDescriptionId().contains("stone")) {
            prot = 3f;
        } else if (itemStack.getDescriptionId().contains("iron")) {
            prot = 4f;
        } else if (itemStack.getDescriptionId().contains("diamond")) {
            prot = 5f;
        } else if (itemStack.getDescriptionId().contains("netherite")) {
            prot = 6f;
        }

        return prot;
    }

    private float getBestCrossBow(ItemStack itemStack) {
        return EnchantmentHelper.getItemEnchantmentLevel(Enchantments.QUICK_CHARGE, itemStack) + EnchantmentHelper.getItemEnchantmentLevel(Enchantments.MULTISHOT, itemStack) + EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PIERCING, itemStack);
    }

    public float getToolScore(ItemStack itemStack) {
        Item item = itemStack.getItem();
        float score = 0;

        if (item instanceof SwordItem sword) {
            score = sword.getDamage();
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.SHARPNESS, itemStack) * 1.25f;
        } else if (item instanceof AxeItem axe) {
            score = axe.getAttackDamage();
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.SHARPNESS, itemStack) * 1.25f;
        } else if (item instanceof PickaxeItem pickaxe) {
            score = getMaterialTierValue(itemStack);
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_EFFICIENCY, itemStack) * 0.5f;
        } else if (item instanceof ShovelItem) {
            score = getMaterialTierValue(itemStack);
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_EFFICIENCY, itemStack) * 0.5f;
        } else if (item instanceof HoeItem) {
            score = getMaterialTierValue(itemStack);
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_EFFICIENCY, itemStack) * 0.5f;
        } else if (item instanceof CrossbowItem) {
            score = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.QUICK_CHARGE, itemStack) * 1.0f;
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.MULTISHOT, itemStack) * 1.0f;
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PIERCING, itemStack) * 1.0f;
        } else if (item instanceof BowItem) {
            // 综合评估弓的得分，同时考虑力量和冲击附魔
            int powerLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.POWER_ARROWS, itemStack);
            int punchLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.PUNCH_ARROWS, itemStack);
            int flameLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.FLAMING_ARROWS, itemStack);
            int infinityLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.INFINITY_ARROWS, itemStack);

            // 力量作为主要评分因素
            score = powerLevel * 2.0f;
            // 冲击作为次要评分因素
            score += punchLevel * 1.5f;
            // 火矢和无限作为额外加分
            score += flameLevel * 0.5f;
            score += infinityLevel * 1.0f;
        } else if (item instanceof ArmorItem armor) {
            score = armor.getDefense();
            score += EnchantmentHelper.getItemEnchantmentLevel(Enchantments.ALL_DAMAGE_PROTECTION, itemStack) * 1.5f;
        } else {
            score = getMaterialTierValue(itemStack);
        }
        return score;
    }

    private float getMaterialTierValue(ItemStack itemStack) {
        float prot = 0f;
        if (itemStack.getDescriptionId().contains("wooden")) {
            prot = 1f;
        } else if (itemStack.getDescriptionId().contains("leather")) {
            prot = 1.5f;
        } else if (itemStack.getDescriptionId().contains("golden")) {
            prot = 2f;
        } else if (itemStack.getDescriptionId().contains("stone")) {
            prot = 3f;
        } else if (itemStack.getDescriptionId().contains("chainmail")) {
            prot = 3.5f;
        } else if (itemStack.getDescriptionId().contains("iron")) {
            prot = 4f;
        } else if (itemStack.getDescriptionId().contains("diamond")) {
            prot = 5f;
        } else if (itemStack.getDescriptionId().contains("netherite")) {
            prot = 6f;
        }
        return prot;
    }

    private void calculateSlotsToDiscardForBlockLimit() {
        List<SlotAndStack> goodBlocks = new ArrayList<>();
        int totalGoodBlockQuantity = 0;

        Map<Integer, ItemStack> currentInventory = getInventoryItemsWithOffhand();

        for (Map.Entry<Integer, ItemStack> entry : currentInventory.entrySet()) {
            int slot = entry.getKey();
            ItemStack stack = entry.getValue();
            if (stack.getItem() instanceof BlockItem) {
                Block block = ((BlockItem) stack.getItem()).getBlock();

                boolean isBadBlock = block instanceof AnvilBlock || block instanceof EnchantmentTableBlock ||
                        block instanceof FurnaceBlock || block instanceof CraftingTableBlock ||
                        block == Blocks.COBWEB || block == Blocks.HOPPER || block instanceof ChestBlock ||
                        block == Blocks.TNT ||
                        InventoryUtils.isBlocked(block);

                if (!isBadBlock) {
                    goodBlocks.add(new SlotAndStack(slot, stack));
                    totalGoodBlockQuantity += stack.getCount();
                }
            }
        }

        if (totalGoodBlockQuantity > blockLimit.getValue().intValue()) {
            int amountToDiscard = totalGoodBlockQuantity - blockLimit.getValue().intValue();
            goodBlocks.sort(Comparator.comparingInt(sb -> sb.stack.getCount()));
            for (SlotAndStack sb : goodBlocks) {
                if (amountToDiscard <= 0) break;

                int discardFromThisStack = Math.min(amountToDiscard, sb.stack.getCount());
                slotsToDiscardForBlockLimit.add(sb.slot);
                amountToDiscard -= sb.stack.getCount();
            }
        }
    }

    /**
     * 判断一个方块是否不计入方块上限
     * @param block 要检查的方块
     * @return 如果方块不计入上限则返回true
     */
    public boolean isBlockExcludedFromLimit(Block block) {
        return block instanceof AnvilBlock ||
                block instanceof EnchantmentTableBlock ||
                block instanceof FurnaceBlock ||
                block instanceof CraftingTableBlock ||
                block == Blocks.COBWEB ||
                block == Blocks.HOPPER ||
                block instanceof ChestBlock ||
                block == Blocks.TNT ||
                InventoryUtils.isBlocked(block);
    }

    /**
     * 计算当前背包中的方块总数（不包括特殊方块）
     * @return 方块总数
     */
    public int calculateTotalBlockCount() {
        int totalBlockCount = 0;

        for (int i = 0; i < mc.player.getInventory().getContainerSize(); i++) {
            ItemStack stack = mc.player.getInventory().getItem(i);
            if (stack.isEmpty() || !(stack.getItem() instanceof BlockItem)) continue;

            Block block = ((BlockItem) stack.getItem()).getBlock();
            if (!isBlockExcludedFromLimit(block)) {
                totalBlockCount += stack.getCount();
            }
        }

        return totalBlockCount;
    }

    /**
     * 判断如果添加指定数量的方块，是否会超过方块上限
     * @param additionalBlockCount 要添加的方块数量
     * @return 如果会超过上限返回true，否则返回false
     */
    public boolean wouldExceedBlockLimit(int additionalBlockCount) {
        int currentCount = calculateTotalBlockCount();
        return currentCount + additionalBlockCount > blockLimit.getValue().intValue();
    }

    /**
     * 判断物品是否是方块且会计入方块上限
     * @param itemStack 物品堆
     * @return 如果是计入上限的方块则返回true
     */
    public boolean isCountedBlock(ItemStack itemStack) {
        if (itemStack.isEmpty() || !(itemStack.getItem() instanceof BlockItem)) {
            return false;
        }

        Block block = ((BlockItem) itemStack.getItem()).getBlock();
        return !isBlockExcludedFromLimit(block);
    }

    public static class RankedItemStack {
        final ItemStack itemStack;
        final int slot;
        final float score;

        RankedItemStack(ItemStack itemStack, int slot, float score) {
            this.itemStack = itemStack;
            this.slot = slot;
            this.score = score;
        }

        public ItemStack getItemStack() {
            return itemStack;
        }

        public int getSlot() {
            return slot;
        }

        public float getScore() {
            return score;
        }
    }

    private static class SlotAndStack {
        final int slot;
        final ItemStack stack;

        SlotAndStack(int slot, ItemStack stack) {
            this.slot = slot;
            this.stack = stack;
        }
    }

    private int getConfiguredSlotCountForItemType(String itemTypeName) {
        int count = 0;
        String lowerItemTypeName = itemTypeName.toLowerCase();
        if (sortSlot1Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot2Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot3Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot4Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot5Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot6Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot7Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot8Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlot9Value.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        if (sortSlotOFFValue.getValue().toLowerCase().equals(lowerItemTypeName)) count++;
        return count;
    }
}