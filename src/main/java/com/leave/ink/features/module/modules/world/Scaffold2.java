package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.hud.dynamicIsland.impl.ProcessDynamic;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.player.InventoryUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.PlaceInfo;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.utils.timer.TimeUtils;
import com.mojang.blaze3d.platform.InputConstants;
import net.minecraft.client.KeyMapping;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.protocol.game.ServerboundSwingPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.level.block.AirBlock;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

import org.lwjgl.glfw.GLFW;
import java.awt.*;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Scaffold2", language = Language.English),
        @Text(label = "搭路2", language = Language.Chinese)
}, category = Category.World)
public class Scaffold2 extends Module {
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delay = new NumberSetting(0, 0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "OffGroundTick", language = Language.English),
            @Text(label = "空中时刻", language = Language.Chinese)
    })
    private final NumberSetting offGroundTick = new NumberSetting(0.0, 0.0, 10.0, "#");
    @SettingInfo(name = {
            @Text(label = "AutoBlock", language = Language.English),
            @Text(label = "自动方块", language = Language.Chinese)
    })
    public final BooleanSetting autoBlockValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Sprint", language = Language.English),
            @Text(label = "疾跑", language = Language.Chinese)
    })
    private final ModeSetting sprint = new ModeSetting("OnGround", Arrays.asList("Off", "OnGround", "Full"));
    @SettingInfo(name = {
            @Text(label = "KeepRotation", language = Language.English),
            @Text(label = "保持转头", language = Language.Chinese)
    })
    private final BooleanSetting keepRotationValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "MoveFix", language = Language.English),
            @Text(label = "移动修复", language = Language.Chinese)
    })
    private final BooleanSetting moveFixValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Swing", language = Language.English),
            @Text(label = "挥手", language = Language.Chinese)
    })
    private final BooleanSetting swingValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "RayCastExact", language = Language.English),
            @Text(label = "光线对准精准大小", language = Language.Chinese)
    })
    private final NumberSetting rayCastExact = new NumberSetting(0.9, 0.1, 1, "#.00");

    @SettingInfo(name = {
            @Text(label = "RotationSpeed", language = Language.English),
            @Text(label = "RotationSpeed", language = Language.Chinese)
    })
    private final NumberSetting rotationSpeed = new NumberSetting(10, 1, 10, "#");
    @SettingInfo(name = {
            @Text(label = "RayCast", language = Language.English),
            @Text(label = "光线对准", language = Language.Chinese)
    })
    private final BooleanSetting rayCast = new BooleanSetting(false, new SettingAttribute<>(rayCastExact, true));
    @SettingInfo(name = {
            @Text(label = "SameY", language = Language.English),
            @Text(label = "同Y轴", language = Language.Chinese)
    })
    private final BooleanSetting sameYValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "UpTelly", language = Language.English),
            @Text(label = "高搭切换", language = Language.Chinese)
    })
    private final BooleanSetting upTelly = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "AutoJump", language = Language.English),
            @Text(label = "自动跳跃", language = Language.Chinese)
    })
    private final BooleanSetting autoJump = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "TestMode", language = Language.English),
            @Text(label = "TestMode", language = Language.Chinese)
    })
    private final BooleanSetting test = new BooleanSetting(true);


    @SettingInfo(name = {
            @Text(label = "PlaceMode", language = Language.English),
            @Text(label = "PlaceMode", language = Language.Chinese)
    })
    private final ModeSetting placeMode = new ModeSetting("Pre", Arrays.asList("Pre", "Post"));
    @SettingInfo(name = {
            @Text(label = "MarkColor", language = Language.English),
            @Text(label = "圆圈颜色", language = Language.Chinese)
    })
    private final ColorSetting markColor = new ColorSetting(new Color(255, 255, 255, 255));
    @SettingInfo(name = {
            @Text(label = "Mark", language = Language.English),
            @Text(label = "标记", language = Language.Chinese)
    })

    private final BooleanSetting mark = new BooleanSetting(true, new SettingAttribute<>(markColor, true));
    private ProcessDynamic processDynamic = null;
    private final TimeUtils timer = new TimeUtils();
    private PlaceInfo targetPlace = null;
    private double keepY = 0.0;
    private int ticksOnAir = 0;
    public int fakeItemSlot = -1;

    @Override
    protected boolean showDynamic() {
        return false;
    }

    public Scaffold2() {
        registerSetting(placeMode, test, delay, offGroundTick, autoBlockValue, sprint, keepRotationValue,rotationSpeed, moveFixValue, swingValue, rayCast, sameYValue, upTelly, autoJump, mark);
    }

    @Override
    public void onEnable() {
        if (mc.player == null) return;

        try {
            if (mc.options.keyAttack.isDown())
                KeyMapping.set(mc.options.keyAttack.getKey(), false);

            if (processDynamic == null) {
                processDynamic = new ProcessDynamic("", "", 10, 100);
                processDynamic.sticky = true;
                processDynamic.setKeepAlive(true);
                HudManager.dynamicIsland.addTask(processDynamic);
            }

            int cnt = Math.max(BlockUtils.getBlocksCountInv(), 0);
            processDynamic.current = cnt;
            processDynamic.max = cnt;

            fakeItemSlot = mc.player.getInventory().selected;
        } catch (Exception e) {
            ChatUtils.displayAlert(e.getMessage());
        }
    }

    @Override
    public void onDisable() {
        if (mc.player == null) return;

        processDynamic.setKeepAlive(false);
        processDynamic.sticky = false;
        processDynamic = null;

        mc.options.keyJump.setDown(GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) == GLFW.GLFW_PRESS);
        targetPlace = null;

        if (fakeItemSlot != -1) {
            if (autoBlockValue.getValue()) {
                mc.player.getInventory().selected = fakeItemSlot;
            }
            fakeItemSlot = -1;
        }
    }

    @EventTarget
    public void onMouseWheel(EventScroll event) {
        if (Utils.isNull()) return;

        if (event.getGetScrollDelta() > 0) {
            fakeItemSlot--;
        } else if (event.getGetScrollDelta() < 0) {
            fakeItemSlot++;
        }
    }

    private KillAura aura = null;

    @EventTarget
    public void onPlaceEvent(EventPlaceBlock eventPlaceBlock) {

        if(placeMode.getValue().equals("Pre")) {
            if(eventPlaceBlock.getEventType() != EventType.PRE) return;
        }

        if(placeMode.getValue().equals("Post")) {
            if(eventPlaceBlock.getEventType() != EventType.POST) return;
        }

        if(test.getValue()) update();

        if (targetPlace == null) return;

        doPlace();
    }

    private void doPlace() {
        if (aura == null) aura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");

        if (aura.target != null || aura.currentTarget != null) {
            return;
        }
        int slot = InventoryUtils.findAutoBlockBlock();

        if (slot < 0)
            return;

        if (mc.player == null) return;

        place();
    }

    @EventTarget
    public void onStrafe(EventStrafe event) {
        if (mc.player == null) return;

        if (autoJump.getValue()) {
            mc.options.keyJump.setDown((mc.player.onGround() && MovementUtils.isMoving()) || InputConstants.isKeyDown(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()));
        }
    }

    @EventTarget
    public void onUpdate(EventUpdate e) {
        if (mc.player == null) return;

        double xDif = mc.player.getX() - mc.player.xOld;
        double zDif = mc.player.getZ() - mc.player.zOld;
        double lastDist = Math.sqrt(xDif * xDif + zDif * zDif) * 20.0;
        int cnt = Math.max(BlockUtils.getBlocksCountInv(), 0);

        processDynamic.rightText = String.format("%.2f", lastDist) + " b/s";
        processDynamic.leftText = (cnt > 0 ? (cnt + "") : "No") +  " §fBlocks";
        processDynamic.current = cnt;

        if (mc.player.onGround()) {
            keepY = Math.floor(mc.player.getY() - 1.0);
        }

        if (upTelly.getValue()) {
            if (sameYValue.getValue() && GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) == GLFW.GLFW_PRESS) {
                sameYValue.setValue(false);
            } else if (!sameYValue.getValue()) {
                sameYValue.setValue(true);
            }
        }
    }

    @EventTarget
    public void onUpdate(EventTick e) {
        if (e.getEventType() != EventType.MIDDLE) return;

        if(!test.getValue()) update();
    }

    public void update() {
        if (mc.player == null) return;

        try {
            if (aura == null) aura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
            targetPlace = null;
            if (aura.target != null || aura.currentTarget != null) {
                return;
            }

            if (fakeItemSlot > 8) fakeItemSlot = 0;
            if (fakeItemSlot < 0) fakeItemSlot = 8;

            if (autoBlockValue.getValue()) {
                for (int i = 0; i < 9; ++i) {
                    if (mc.options.keyHotbarSlots[i].isDown()) {
                        fakeItemSlot = i;
                    }
                }

                if (InventoryUtils.findAutoBlockBlock() != -1) {
                    mc.player.getInventory().selected = InventoryUtils.findAutoBlockBlock();
                }
            }

            if (!mc.player.onGround()) {
                ticksOnAir++;
            } else {
                ticksOnAir = 0;
            }

            int c = InventoryUtils.findAutoBlockBlock();
            if (c != -1) {
                if (sprint.getValue().equals("Full")) {
                    if (Main.INSTANCE.moduleManager.getModule("Sprint").isEnable()) {
                        mc.options.keySprint.setDown(true);
                        mc.player.setSprinting(true);
                    }
                }
                if (sprint.getValue().equals("OnGround") && mc.player.onGround()) {
                    if (Main.INSTANCE.moduleManager.getModule("Sprint").isEnable()) {
                        mc.options.keySprint.setDown(true);
                        mc.player.setSprinting(true);
                    }
                } else if (sprint.getValue().equals("Off")) {
                    mc.options.keySprint.setDown(false);
                    mc.player.setSprinting(false);
                }
            } else return;
            targetPlace = BlockUtils.getBlockPlace(BlockPos.containing(mc.player.getX(), getYLevel(), mc.player.getZ()));
            if (targetPlace != null) {
                Rotation rotation = RotationUtils.getBestRotation(targetPlace.getBlockPos(), targetPlace.getEnumFacing());
                if (ticksOnAir >= offGroundTick.getValue().intValue() || (GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) == GLFW.GLFW_PRESS && ticksOnAir >= 1)) {
                    RotationUtils.setRotation(rotation, rotationSpeed.getValue().intValue(), moveFixValue.getValue() ? MovementFix.NORMAL : MovementFix.OFF);
                }

                if (ticksOnAir == 0 && !autoJump.getValue() && GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) != GLFW.GLFW_PRESS) {
                    if (keepRotationValue.getValue()) {
                        RotationUtils.setRotation(rotation, rotationSpeed.getValue().intValue(), moveFixValue.getValue() ? MovementFix.NORMAL : MovementFix.OFF);
                    }
                }
            }
        } catch (Exception e) {
            ChatUtils.displayAlert(e.getMessage());
        }
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (mc.player == null) return;

        Direction facing = mc.player.getDirection();

        double x = mc.player.getX() + (facing == Direction.WEST ? -0.5 : facing == Direction.EAST ? 0.5 : 0);
        double y = getYLevel();
        double z = mc.player.getZ() + (facing == Direction.NORTH ? -0.5 : facing == Direction.SOUTH ? 0.5 : 0);

        BlockPos blockPos = BlockPos.containing(x, y, z);

        if (mark.getValue() && PlaceInfo.get(blockPos) != null) {
            RenderUtils.drawLineBox(event.getPoseStack(), blockPos, markColor.getValue());
        }
    }

    private double getYLevel() {
        if (mc.player == null) return 0.0;

        if (!sameYValue.getValue()) {
            return mc.player.getY() - 1.0;
        }

        return !MovementUtils.isMoving() ? mc.player.getY() - 1.0 : keepY;
    }

    private void place() {
        try {
            if (timer.hasTimeElapsed(delay.getValue().longValue())) {
                if (mc.player == null || targetPlace == null || mc.gameMode == null) return;

                if (BlockUtils.block(mc.player.getX(), getYLevel(), mc.player.getZ()) instanceof AirBlock) {
                    Vec3 vec3 = BlockUtils.getVec3(targetPlace.getBlockPos(), targetPlace.getEnumFacing(), 0.08f);
                    BlockHitResult target = new BlockHitResult(vec3, targetPlace.getEnumFacing(), targetPlace.getBlockPos(), false);

                    if (!rayCast.getValue() || RotationUtils.isLookingAtBlock(RotationUtils.rotations, target.getBlockPos(), 4 + 1, rayCastExact.getValue().floatValue())) {
                        if (mc.gameMode.useItemOn(mc.player, InteractionHand.MAIN_HAND, target).consumesAction()) {
                            if (swingValue.getValue()) {
                                mc.player.swing(InteractionHand.MAIN_HAND);
                            } else {
                                PacketUtils.sendPacket(new ServerboundSwingPacket(InteractionHand.MAIN_HAND));
                            }
                        }
                    }
                    timer.reset();
                }
            }
        } catch (Exception e) {
            ChatUtils.displayAlert(e.getMessage());
        }
    }
}