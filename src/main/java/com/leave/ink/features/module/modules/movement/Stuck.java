package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.hud.dynamicIsland.impl.ProcessDynamic;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.KeyMapping;
import net.minecraft.core.BlockPos;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.block.state.BlockState;

import java.util.concurrent.LinkedBlockingQueue;

@ModuleInfo(name = {
        @Text(label = "Stuck", language = Language.English),
        @Text(label = "卡空", language = Language.Chinese)
}, category = Category.Movement)
public class Stuck extends Module {
    @SettingInfo(name = {
            @Text(label = "StuckTick", language = Language.English),
            @Text(label = "卡空时刻", language = Language.Chinese)
    })
    public final NumberSetting tick = new NumberSetting(20, 1, 200, "#");
    @SettingInfo(name = {
            @Text(label = "AutoDisable", language = Language.English),
            @Text(label = "自动关闭", language = Language.Chinese)
    })
    public final BooleanSetting autoDisable = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Lag Check", language = Language.English),
            @Text(label = "Lag Check", language = Language.Chinese)
    })
    public final BooleanSetting lagCheck = new BooleanSetting(true);

    private boolean useEnderPearl;

    private final LinkedBlockingQueue<Packet<ClientGamePacketListener>> packets = new LinkedBlockingQueue<>();
    public static final TimeUtils targetTimer = new TimeUtils();
    public static boolean shouldCancel = false;
    private boolean disableLogger;
    private int stuckTick;
    public Stuck() {
        registerSetting(tick, autoDisable, lagCheck);
    }
    private ProcessDynamic processDynamic = new ProcessDynamic("Stuck...","",10,100);

    @Override
    protected void onDisable() {
        if(Utils.isNull()) return;
        processDynamic.sticky = false;
        processDynamic.setKeepAlive(false);
        processDynamic = null;
        progressStartTime = -1;
        useEnderPearl = false;
        reset();
    }

    @Override
    protected void onEnable() {
        if(Utils.isNull()) return;

        if (processDynamic == null) {
            processDynamic = new ProcessDynamic("Stuck...", "", 10, 100);
            processDynamic.sticky = true;
            processDynamic.setKeepAlive(true);
            HudManager.dynamicIsland.addTask(processDynamic);
        }


        targetTimer.reset();

        mc.options.keySprint.setDown(mc.options.keySprint.isDown());
    }
    public BlockPos getFirstMineableBlockBelow(LivingEntity player, double reach) {
        BlockPos start = BlockPos.containing(player.position());
        for (int i = 0; i <= reach; i++) {
            BlockPos pos = start.below(i);
            BlockState state = mc.level.getBlockState(pos);
            if (!state.isAir() && state.getDestroySpeed(mc.level, pos) >= 0) {
                return pos;
            }
        }
        return null;
    }
    @EventTarget
    public void onUpdate(EventPlayerTick eventPlayerTick) {

        if (!mc.player.isAlive() || mc.player.onGround() || (autoDisable.getValue() && onGround())) {
            reset();
            toggle();
        }
        stuckTick++;

    }

    private boolean onGround() {
        return getFirstMineableBlockBelow(mc.player, 3.0) != null;
    }
    @EventTarget
    public void onPacket(EventPacket event) {

        if (event.getPacketType() == EventPacket.PacketType.Server) {
            if (lagCheck.getValue() && event.getPacket() instanceof ClientboundPlayerPositionPacket) {
                toggle();
            }
        }
        if (disableLogger || event.getPacketType() == EventPacket.PacketType.Client || onGround() || mc.player.isSpectator()) return;
        if (event.getPacket() instanceof ClientboundPlayerPositionPacket) {
            toggle();
        }

    }
    @EventTarget
    public void onMoveInput(EventMoveInput event) {

            event.setForward(0);
            event.setStrafe(0);

    }
    @EventTarget
    public void onWorld(EventWorld eventWorld) {
        toggle();
    }

    private void reset() {

        try {
            disableLogger = true;
            while (!packets.isEmpty()) {
                packets.take().handle(mc.player.connection);
            }
            stuckTick = 0;
        } catch (final Exception e) {
            e.printStackTrace();
        } finally {
            disableLogger = false;
        }
    }
    @EventTarget
    private void onMathEvent(EventMoveMath event) {

        if (!Utils.isNull() && !mc.player.onGround()) {

            if (!mc.player.isSpectator()) {
                if (stuckTick == tick.getValue().intValue()) {
                    RotationUtils.setRotation(new Rotation(mc.player.getYRot() + (float) (Math.random() - 0.5), mc.player.getXRot()),  10,MovementFix.NORMAL);
                    event.setCancelled(false);
                    stuckTick = 0;
                } else {
                    KeyMapping.set(mc.options.keySprint.getKey(), false);
                    event.setCancelled(true);
                }
            } else if (!autoDisable.getValue() || onGround() || mc.player.isSpectator()) {
                stuckTick = 0;
            }

//            if (stuckTick == 20) {
//                RotationUtils.setRotation(
//                        new Rotation(mc.player.getYRot() + (float) (Math.random() - 0.5), mc.player.getXRot()),
//                        10,MovementFix.NORMAL
//                );
//                event.setCancelled(false);
//                stuckTick = 0;
//            } else {
//                KeyMapping.set(mc.options.keySprint.getKey(), false);
//                event.setCancelled(true);
//            }
        } else {
            stuckTick = 0;
        }

    }

    @Override
    protected boolean showDynamic() {
        return false;
    }

    @EventTarget
    public void onRender2D(EventRender2D event) {

        processDynamic.current = stuckTick;
        processDynamic.rightText = "Tick " + stuckTick + "/" +  tick.getValue().intValue();
        processDynamic.max = tick.getValue().intValue();
//            PoseStack poseStack = event.getPoseStack();
//            poseStack.pushPose();
//            double width = 120;
//            double x = mc.getWindow().getGuiScaledWidth() / 2f - width / 2f;
//            double y = mc.getWindow().getGuiScaledHeight() / 2f + 35;
//            poseStack.translate(x, y, 0);
//            RenderSystem.enableBlend();
//            RenderSystem.setShader(GameRenderer::getPositionColorShader);
//            RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
//            RenderUtils.drawRect(poseStack, 0, 0, 122, 5, new Color(0, 0, 0, 160).getRGB());
//            RenderUtils.drawRect(poseStack, 2, 2, 120 * (stuckTick / tick.getValue().doubleValue()), 3, Color.RED.getRGB());
//            FontRenderers.sf_bold20.drawString(poseStack, "Stuck", 45, -10, Color.WHITE.getRGB());
//            poseStack.popPose();

    }
    private long progressStartTime = -1;


}