package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventAttack;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.timer.MinecraftTimer;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.network.protocol.game.ClientboundAnimatePacket;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import net.minecraft.world.entity.LivingEntity;

import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Criticals", language = Language.English),
        @Text(label = "刀爆", language = Language.Chinese)
}, category = Category.Combat)
public class Criticals extends Module {
    @SettingInfo(name = {
            @Text(label = "TimerSpeed", language = Language.English),
            @Text(label = "变速齿轮速度", language = Language.Chinese)
    })
    private final NumberSetting timerSpeedValue = new NumberSetting(0.4f, 0.1f, 2f, "#.00");
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting modeValue = new ModeSetting("Packet", Arrays.asList("Packet", "AutoJump", "Jump", "LowJump", "Hop", "Visual", "Timer"), new SettingAttribute<>(timerSpeedValue, "Timer"));
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delayValue = new NumberSetting(0d, 0d, 1000d, "#");
    @SettingInfo(name = {
            @Text(label = "HurtTime", language = Language.English),
            @Text(label = "伤害时间", language = Language.Chinese)
    })
    private final NumberSetting hurtTimeValue = new NumberSetting(10d, 0d, 10d, "#");
    @SettingInfo(name = {
            @Text(label = "DeBug", language = Language.English),
            @Text(label = "输出日记", language = Language.Chinese)
    })
    private final BooleanSetting deBugValue = new BooleanSetting(true);

    public Criticals() {
        registerSetting(modeValue, delayValue, hurtTimeValue, deBugValue);
    }

    int target = 0;
    TimeUtils timer = new TimeUtils();
    LivingEntity entity;

    @Override
    public void onEnable() {
        if (Utils.isNull())
            return;

        target = 0;
    }

    @EventTarget(4)
    public void onAttack(EventAttack event) {
        if (event.getTargetEntity() instanceof LivingEntity livingEntity) {
            target = livingEntity.getId();
            entity = livingEntity;
        }
    }

    @EventTarget(4)
    public void onUpdate(EventUpdate event) {
        MinecraftTimer.setTimerSpeed(1f);

        if (entity != null) {
            if ((!mc.player.onGround() && !modeValue.getValue().equals("Timer")) || mc.player.onClimbable() || mc.player.isInWater() ||
                    mc.player.isInLava() || mc.player.isPassenger() || entity.hurtTime > hurtTimeValue.getValue().intValue()
                    || !timer.hasTimeElapsed(delayValue.getValue().longValue()))
                return;

            double x = mc.player.getX();
            double y = mc.player.getY();
            double z = mc.player.getZ();

            switch (modeValue.getValue().toLowerCase()) {
                case "autojump": {
                    mc.player.jumpFromGround();
                    break;
                }
                case "packet": {
                    mc.getConnection().send(new ServerboundMovePlayerPacket.Pos(x, y + 0.0625, z, true));
                    mc.getConnection().send(new ServerboundMovePlayerPacket.Pos(x, y, z, false));
                    mc.getConnection().send(new ServerboundMovePlayerPacket.Pos(x, y + 1.1E-5, z, false));
                    mc.getConnection().send(new ServerboundMovePlayerPacket.Pos(x, y, z, false));
                    break;
                }
                case "hop": {
                    mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, 0.1, mc.player.getDeltaMovement().z);
                    mc.player.fallDistance = 0.1f;
                    mc.player.setOnGround(false);
                    break;
                }
                case "jump": {
                    mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, 0.42, mc.player.getDeltaMovement().z);
                    break;
                }
                case "lowjump": {
                    mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, 0.3425, mc.player.getDeltaMovement().z);
                    break;
                }
                case "visual": {
                    mc.player.magicCrit(entity);
                    break;
                }
                case "timer": {
                    MinecraftTimer.setTimerSpeed(timerSpeedValue.getValue().floatValue());
                    break;
                }
            }
            timer.reset();
            entity = null;
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacket() instanceof ClientboundAnimatePacket animatePacket && deBugValue.getValue()) {
            if (animatePacket.getAction() == ClientboundAnimatePacket.CRITICAL_HIT && animatePacket.getId() == target) {
                ChatUtils.displayAlert("触发§c暴击§b(§6玩家§f:" + entity.getName().getString() + "§b)");
            }
        }
    }

    @Override
    public String getTag() {
        return modeValue.getValue();
    }
}
