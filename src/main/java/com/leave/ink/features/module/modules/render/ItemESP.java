package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.systems.RenderSystem;

import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.phys.AABB;

import java.awt.Color;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import org.lwjgl.opengl.GL11;

@ModuleInfo(name = {
        @Text(label = "ItemESP", language = Language.English),
        @Text(label = "掉落物透视", language = Language.Chinese)
}, category = Category.Render)
public class ItemESP extends Module {

    @SettingInfo(name = {
            @Text(label = "Box", language = Language.English),
            @Text(label = "方框", language = Language.Chinese)
    })
    public final BooleanSetting box = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Name", language = Language.English),
            @Text(label = "显示名称", language = Language.Chinese)
    })
    public final BooleanSetting showName = new BooleanSetting(true);



    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "范围", language = Language.Chinese)
    })
    public final NumberSetting range = new NumberSetting(64, 8, 128, "#");

    @SettingInfo(name = {
            @Text(label = "MaxItems", language = Language.English),
            @Text(label = "最大物品数", language = Language.Chinese)
    })
    public final NumberSetting maxItems = new NumberSetting(15, 5, 50, "#");

    @SettingInfo(name = {
            @Text(label = "UpdateFrequency", language = Language.English),
            @Text(label = "更新频率", language = Language.Chinese)
    })
    public final NumberSetting updateFrequency = new NumberSetting(10, 1, 20, "#");

    @SettingInfo(name = {
            @Text(label = "BoxColor", language = Language.English),
            @Text(label = "方框颜色", language = Language.Chinese)
    })
    public final ColorSetting boxColor = new ColorSetting(new Color(255, 255, 0, 255));

    @SettingInfo(name = {
            @Text(label = "NameColor", language = Language.English),
            @Text(label = "名称颜色", language = Language.Chinese)
    })
    public final ColorSetting nameColor = new ColorSetting(new Color(255, 255, 0, 255));

    private final List<ItemEntityData> itemCache = new CopyOnWriteArrayList<>();
    private int updateCounter = 0;

    public ItemESP() {
        registerSetting(box, showName, range, maxItems, updateFrequency, boxColor, nameColor);
    }

    @Override
    protected void onDisable() {
        super.onDisable();
        itemCache.clear();
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (mc.level == null || mc.player == null) return;
        updateCounter++;
        if (updateCounter >= updateFrequency.getValue().intValue() || itemCache.isEmpty()) {
            updateCounter = 0;
            updateItemCache();
        }
        if (box.getValue()) {
            renderBoxes(event);
        }

        if (showName.getValue()) {
            renderNames(event);
        }
    }

    private void updateItemCache() {
        itemCache.clear();

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof ItemEntity itemEntity && isInRange(entity)) {
                double distance = mc.player.distanceTo(itemEntity);
                itemCache.add(new ItemEntityData(itemEntity, distance));
            }
        }

        if (itemCache.size() > 1) {
            itemCache.sort(Comparator.comparingDouble(ItemEntityData::getDistance));
        }

        int limit = maxItems.getValue().intValue();
        if (itemCache.size() > limit) {
            itemCache.subList(limit, itemCache.size()).clear();
        }
    }

    private void renderBoxes(EventRender3D event) {
        boolean depthEnabled = GL11.glIsEnabled(GL11.GL_DEPTH_TEST);
        int depthFunc = GL11.glGetInteger(GL11.GL_DEPTH_FUNC);

        try {
            PoseStack poseStack = event.getPoseStack();

                GL11.glDisable(GL11.GL_DEPTH_TEST);
                RenderSystem.depthMask(false);

            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            for (ItemEntityData data : itemCache) {
                ItemEntity itemEntity = data.getEntity();
                if (itemEntity == null || !itemEntity.isAlive() || itemEntity.isRemoved()) {
                    continue;
                }

                renderBox(poseStack, itemEntity);
            }
            RenderSystem.disableBlend();

                RenderSystem.depthMask(true);
                if (depthEnabled) {
                    GL11.glEnable(GL11.GL_DEPTH_TEST);
                    GL11.glDepthFunc(depthFunc);
                }

        } catch (Exception ignored) {
            RenderSystem.disableBlend();

                RenderSystem.depthMask(true);
                if (depthEnabled) {
                    GL11.glEnable(GL11.GL_DEPTH_TEST);
                    GL11.glDepthFunc(depthFunc);
                }

        }
    }

    private void renderNames(EventRender3D event) {
        boolean depthEnabled = GL11.glIsEnabled(GL11.GL_DEPTH_TEST);
        int depthFunc = GL11.glGetInteger(GL11.GL_DEPTH_FUNC);

        try {

                GL11.glDisable(GL11.GL_DEPTH_TEST);
                RenderSystem.depthMask(false);


            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();

            for (ItemEntityData data : itemCache) {
                ItemEntity itemEntity = data.getEntity();
                if (itemEntity == null || !itemEntity.isAlive() || itemEntity.isRemoved()) {
                    continue;
                }

                renderItemName(event, itemEntity);
            }

            RenderSystem.disableBlend();

                RenderSystem.depthMask(true);
                if (depthEnabled) {
                    GL11.glEnable(GL11.GL_DEPTH_TEST);
                    GL11.glDepthFunc(depthFunc);
                }

        } catch (Exception ignored) {
            RenderSystem.disableBlend();
            RenderSystem.depthMask(true);
            if (depthEnabled) {
                GL11.glEnable(GL11.GL_DEPTH_TEST);
                GL11.glDepthFunc(depthFunc);
            }
        }
    }

    private void renderBox(PoseStack poseStack, ItemEntity itemEntity) {
        try {
            AABB boundingBox = itemEntity.getBoundingBox()
                    .inflate(0.05)
                    .move(-itemEntity.getX(), -itemEntity.getY(), -itemEntity.getZ());

            poseStack.pushPose();
            poseStack.translate(itemEntity.getX(), itemEntity.getY(), itemEntity.getZ());
            RenderUtils.drawLineBox(poseStack, boundingBox, boxColor.getValue());
            poseStack.popPose();
        } catch (Exception ignored) {
        }
    }

    private void renderItemName(EventRender3D event, ItemEntity itemEntity) {
        try {
            Component nameComponent = itemEntity.getItem().getHoverName();

            double x = itemEntity.getX();
            double y = itemEntity.getY() + itemEntity.getBbHeight() + 0.5;
            double z = itemEntity.getZ();

            float distanceSq = (float) mc.player.distanceToSqr(x, y, z);
            float maxDistance = range.getValue().floatValue();

            if (distanceSq <= maxDistance * maxDistance) {
                float opacity = mc.options.getBackgroundOpacity(0.5F);
                int visibleColor = nameColor.getValue().getRGB();

                PoseStack matrixStack = event.getPoseStack();
                MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();

                matrixStack.pushPose();
                matrixStack.translate(x - mc.gameRenderer.getMainCamera().getPosition().x,
                        y - mc.gameRenderer.getMainCamera().getPosition().y,
                        z - mc.gameRenderer.getMainCamera().getPosition().z);
                matrixStack.mulPose(mc.gameRenderer.getMainCamera().rotation());

                float scale = 0.025F;
                if (distanceSq > 100.0f) {
                    scale = 0.025F * (float)(Math.sqrt(distanceSq * 0.01F));
                }
                matrixStack.scale(-scale, -scale, scale);

                float backgroundOpacity = 0.5F * opacity;
                int backgroundColor = (int)(backgroundOpacity * 255.0F) << 24;
                float nameX = -mc.font.width(nameComponent) / 2.0f;
                float nameY = 0;
                mc.font.drawInBatch(nameComponent, nameX, nameY, visibleColor, false,
                        matrixStack.last().pose(), bufferSource,
                        net.minecraft.client.gui.Font.DisplayMode.SEE_THROUGH,
                        backgroundColor, 15728880);

                bufferSource.endBatch();
                matrixStack.popPose();
            }
        } catch (Exception ignored) {
        }
    }

    private boolean isInRange(Entity entity) {
        if (mc.player == null) return false;
        return mc.player.distanceTo(entity) <= range.getValue().floatValue();
    }

    /**
     * 物品实体数据类，用于缓存物品信息和距离
     */
    private static class ItemEntityData {
        private final ItemEntity entity;
        private final double distance;

        public ItemEntityData(ItemEntity entity, double distance) {
            this.entity = entity;
            this.distance = distance;
        }

        public ItemEntity getEntity() {
            return entity;
        }

        public double getDistance() {
            return distance;
        }
    }
} 