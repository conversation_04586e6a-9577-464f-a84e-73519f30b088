package com.leave.ink.features.module.annotation;

import com.leave.ink.features.module.Category;
import com.leave.ink.language.Text;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ModuleInfo {
    Text[] name();

    Category category();

    int key() default 0;

    boolean enable() default false;
}
