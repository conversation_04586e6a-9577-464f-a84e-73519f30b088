package com.leave.ink.features.module.modules.movement;


import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.*;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.network.PacketUtils;
import lombok.Getter;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;
import net.minecraft.sounds.SoundEvents;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 禁止抄袭，发现死全家
 * <AUTHOR>
 */
@ModuleInfo(name = {@Text(label = "LongJump", language = Language.English), @Text(label = "跳远冠军", language = Language.Chinese)}, category = Category.Movement)
public class LongJump extends Module {
    @SettingInfo(name = {@Text(label = "PacketDelay", language = Language.English), @Text(label = "延迟包", language = Language.Chinese)})
    private final NumberSetting packetDelay = new NumberSetting(50, 10, 200, "#.0");

    @SettingInfo(name = {@Text(label = "AutoJump", language = Language.English), @Text(label = "自动跳跃", language = Language.Chinese)})
    private final BooleanSetting autoJump = new BooleanSetting(true);
    private final Queue<PacketSnapshot> delayedPackets = new ConcurrentLinkedQueue<>();

    @Getter
    private boolean flying = false;
    private boolean isJump = false;
    private double lastY = 0;
    public static LongJump instance;
    public LongJump() {
        instance = this;
        registerSetting(packetDelay, autoJump);
    }

    public void debugMessage(String message) {

    }

    @Override
    public void onEnable() {
        if (mc.player == null) return;


        flying = false;
        isJump = false;
        lastY = mc.player.getY();
    }

    @Override
    public void onDisable() {
        if (mc.player == null) return;

        releasePackets();

        flying = false;
        isJump = false;
        lastY = 0;
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        releasePackets();
    }

    @EventTarget
    public void onStrafe(EventStrafe event) {
        if (autoJump.getValue() && mc.player.onGround() && isJump) {
            mc.player.jumpFromGround();
            isJump = true;
        }
    }

    @EventTarget
    public void onMotion(EventMotion event) {
        if (event.getEventType() == EventType.PRE) return;

        if (mc.player.isDeadOrDying() || mc.player.getHealth() <= 0) {
            releasePackets();
            this.setEnable(false);
            return;
        }

        if (!flying && mc.player.getY() > lastY + 3) {
            flying = true;
            debugMessage("Flying!");
        }

        if (delayedPackets.isEmpty()) {
            debugMessage("All packets released!");
        }

        processDelayedPackets();
    }

    @EventTarget
    public void onSyncHandleReceivePacketEvent(EventSyncHandleReceivePacket event) {

        if (event.isCancelled()) {
            return;
        }

        Packet<?> packet = event.getPacket();

        if (packet instanceof ServerboundChatPacket
                || packet instanceof ClientboundSystemChatPacket
                || packet instanceof ServerboundCommandSuggestionPacket) {
            return;
        }

        if (packet instanceof ClientboundPlayerPositionPacket
                || packet instanceof ClientboundDisconnectPacket) {
            releasePackets();
            this.setEnable(false);
            return;
        }

        if (packet instanceof ClientboundSoundPacket soundPacket) {
            if (soundPacket.getSound().get() == SoundEvents.PLAYER_HURT) {
                releasePackets();
                return;
            }
        }

        if (packet instanceof ClientboundSetHealthPacket healthPacket) {
            if (healthPacket.getHealth() <= 0) {
                releasePackets();
                this.setEnable(false);
                return;
            }
        }

        event.setCancelled(true);
        delayedPackets.offer(new PacketSnapshot(packet, System.currentTimeMillis()));
    }

    private void processDelayedPackets() {
        long currentTime = System.currentTimeMillis();
        long delay = packetDelay.getValue().longValue() * 50;

        delayedPackets.removeIf(snapshot -> {
            if (currentTime - snapshot.timestamp() >= delay) {
                try {
                    PacketUtils.handlePacket((Packet<ClientGamePacketListener>) snapshot.packet());
                    debugMessage("Processed packet: " + snapshot.packet().getClass().getSimpleName());
                    return true;
                } catch (Exception e) {
                    debugMessage("Failed to handle packet: " + e.getMessage());
                    return true;
                }
            }
            return false;
        });
    }

    private void releasePackets() {
        debugMessage("Starting to release " + delayedPackets.size() + " packets");
        int count = 0;

        while (!delayedPackets.isEmpty()) {
            PacketSnapshot snapshot = delayedPackets.poll();
            if (snapshot != null) {
                try {
                    PacketUtils.handlePacket((Packet<ClientGamePacketListener>) snapshot.packet());
                    count++;
                    if (count % 10 == 0) {
                        debugMessage("Released " + count + " packets so far");
                    }
                } catch (Exception e) {
                    debugMessage("Error handling packet: " + e.getMessage());
                }
            }
        }
        debugMessage("Released a total of " + count + " packets");
    }

    private record PacketSnapshot(Packet<?> packet, long timestamp) {}
}