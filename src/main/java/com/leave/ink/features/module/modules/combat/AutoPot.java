package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import net.minecraft.network.protocol.game.ServerboundSetCarriedItemPacket;
import net.minecraft.network.protocol.game.ServerboundUseItemPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.inventory.ClickType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.alchemy.PotionUtils;
import net.minecraft.world.phys.Vec3;

import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "AutoPot", language = Language.English),
        @Text(label = "自动药水", language = Language.Chinese)
}, category = Category.Combat)
public class AutoPot extends Module {
    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量", language = Language.Chinese)
    })
    private final NumberSetting healthValue = new NumberSetting( 3.5d, 1.0d, 20d, "#.00");
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delayValue = new NumberSetting(150d, 0d, 500d, "#.00");
    @SettingInfo(name = {
            @Text(label = "OpenInv", language = Language.English),
            @Text(label = "打开背包", language = Language.Chinese)
    })
    private final BooleanSetting openInventoryValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "NoAir", language = Language.English),
            @Text(label = "不在空中", language = Language.Chinese)
    })
    private final BooleanSetting noAirValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting modeValue = new ModeSetting("Normal", Arrays.asList("Normal", "Jump", "Port"));

    public AutoPot() {
        registerSetting(healthValue, delayValue, openInventoryValue, noAirValue, modeValue);
    }

    private final TimeUtils msTimer = new TimeUtils();

    @Override
    public String getTag() {
        return modeValue.getValue();
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (!msTimer.hasTimeElapsed(delayValue.getValue().longValue()) || mc.player.isCreative())
            return;

        if (noAirValue.getValue() && !mc.player.onGround())
            return;

        final int potionInHotbar = findPotion(0, 9);

        if (mc.player.getHealth() <= healthValue.getValue().floatValue() && potionInHotbar != -1) {
            if (mc.player.onGround()) {
                switch (modeValue.getValue().toLowerCase()) {
                    case "jump" -> mc.player.jumpFromGround();
                    case "port" -> mc.player.move(MoverType.SELF, new Vec3(0, 0.42D, 0));
                }
            }

            throwPot(potionInHotbar);
            msTimer.reset();
            return;
        }

        final int potionInInventory = findPotion(9, 36);

        if (potionInInventory != -1 && hasSpaceHotbar()) {
            if (openInventoryValue.getValue() && !(mc.screen instanceof InventoryScreen))
                return;

            mc.gameMode.handleInventoryMouseClick(0, potionInInventory, 0, ClickType.QUICK_MOVE, mc.player);
            msTimer.reset();
        }
    }

    private void throwPot(final int slot) {
        mc.getProfiler().startTick();
        mc.getConnection().send(new ServerboundMovePlayerPacket.PosRot(mc.player.getX(), mc.player.getY(), mc.player.getZ(), mc.player.getYRot(), 90F, mc.player.onGround()));
        mc.getConnection().send(new ServerboundSetCarriedItemPacket(slot));
        mc.getConnection().send(new ServerboundUseItemPacket(InteractionHand.MAIN_HAND,0));
        mc.getConnection().send(new ServerboundSetCarriedItemPacket(mc.player.getInventory().selected));
        mc.getConnection().send(new ServerboundMovePlayerPacket.PosRot(mc.player.getX(), mc.player.getY(), mc.player.getZ(), mc.player.getYRot(), mc.player.getXRot(), mc.player.onGround()));
    }

    private boolean hasSpaceHotbar() {
        Inventory inventory = mc.player.getInventory();
        for (int i = 0; i < 9; i++) {
            ItemStack itemStack = inventory.getItem(i);
            if (itemStack.isEmpty())
                return true;
        }
        return false;
    }

    private int findPotion(final int startSlot, final int endSlot) {
        Inventory inventory = mc.player.getInventory();
        for (int i = startSlot; i < endSlot; i++) {
            ItemStack stack = inventory.getItem(i);
            if (stack.isEmpty() || stack.getItem() != Items.SPLASH_POTION)
                continue;

            if (PotionUtils.getMobEffects(stack).stream().anyMatch(effect -> effect.getEffect() == MobEffects.HEAL || effect.getEffect() == MobEffects.REGENERATION))
                return i;
        }
        return -1;
    }
}
