package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRenderHand;
import com.leave.ink.events.EventRenderLiving;
import com.leave.ink.events.EventType;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.BlockAnimationUtils;
import com.mojang.math.Axis;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.client.renderer.entity.player.PlayerRenderer;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.item.ItemDisplayContext;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.UseAnim;


import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Animations", language = Language.English),
        @Text(label = "防砍动画", language = Language.Chinese)
}, category = Category.Render)
public class Animations extends Module {
    @SettingInfo(name = {
            @Text(label = "XP", language = Language.English),
            @Text(label = "旋转X", language = Language.Chinese)
    })
    private final NumberSetting xP = new NumberSetting(0.0d, -100.0d, 100.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "YP", language = Language.English),
            @Text(label = "旋转Y", language = Language.Chinese)
    })
    private final NumberSetting yP = new NumberSetting(0.0d, -100.0d, 100.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "ZP", language = Language.English),
            @Text(label = "旋转Z", language = Language.Chinese)
    })
    private final NumberSetting zP = new NumberSetting(0.0d, -100.0d, 100.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("1.7", Arrays.asList("None","Custom", "Rotation","1.7", "MineCraft", "New", "Push", "Smooth", "Sigma", "Flux", "ETB", "SigmaOld", "Tap", "Zoom", "Jello")
    , new SettingAttribute<>(xP, "Custom"), new SettingAttribute<>(yP, "Custom"), new SettingAttribute<>(zP, "Custom"));
    @SettingInfo(name = {
            @Text(label = "ItemX", language = Language.English),
            @Text(label = "物品X位置", language = Language.Chinese)
    })
    private final NumberSetting x = new NumberSetting(0.0d, -2.0d, 2.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "ItemY", language = Language.English),
            @Text(label = "物品Y位置", language = Language.Chinese)
    })
    private final NumberSetting y = new NumberSetting(0.0d, -2.0d, 2.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "ItemZ", language = Language.English),
            @Text(label = "物品Z位置", language = Language.Chinese)
    })
    private final NumberSetting z = new NumberSetting(0.0d, -2.0d, 2.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "Scale", language = Language.English),
            @Text(label = "物品大小", language = Language.Chinese)
    })
    private final NumberSetting scale = new NumberSetting(0.8d, 0.1d, 1.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "Bobbing", language = Language.English),
            @Text(label = "物品抖动", language = Language.Chinese)
    })
    private final NumberSetting bobbing = new NumberSetting(3d, 0.3d, 10.0d, "#.00");
    @SettingInfo(name = {
            @Text(label = "EquipProgress", language = Language.English)
    })
    private final BooleanSetting equipProgress = new BooleanSetting(false);
    public Animations() {
        registerSetting(mode, x, y, z, scale, bobbing, equipProgress);
    }

    @EventTarget
    public void onRenderLiving(EventRenderLiving evt) {
        if (mc.player == null)
            return;
        if(evt.getEventType() != EventType.PRE) return;

        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");

        if (killAura.isEnable() && killAura.autoBlockValue.getValue().equals("Fake") && !mc.options.keyUse.isDown())
            return;

        if (evt.getEntity() instanceof LocalPlayer player) {

            if (player.isUsingItem() && BlockAnimationUtils.getCanStackBlock(player.getUseItem()) && (mc.options.keyUse.isDown() && !mc.player.getOffhandItem().is(Items.GOLDEN_APPLE))) {

                PlayerRenderer model = (PlayerRenderer) evt.getRenderer();

                boolean left1 = player.getUsedItemHand() == InteractionHand.OFF_HAND && player.getMainArm() == HumanoidArm.RIGHT;
                boolean left2 = player.getUsedItemHand() == InteractionHand.MAIN_HAND && player.getMainArm() == HumanoidArm.LEFT;
                if (left1 || left2) {
                    if (model.getModel().leftArmPose == HumanoidModel.ArmPose.ITEM) {
                        model.getModel().leftArmPose = HumanoidModel.ArmPose.BLOCK;
                    }
                } else if (model.getModel().rightArmPose == HumanoidModel.ArmPose.ITEM) {
                    model.getModel().rightArmPose = HumanoidModel.ArmPose.BLOCK;

                }
            }
        }
    }

    @EventTarget
    public void onRenderHand(EventRenderHand evt) {
        if (mc.player == null)
            return;

        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
        boolean isBlocking = killAura.isEnable() && killAura.autoBlockValue.getValue().equals("Fake");
        ItemStack stack = evt.getStack();
        boolean flag = evt.getHand() == InteractionHand.MAIN_HAND;
        HumanoidArm handSide = flag ? mc.player.getMainArm() : mc.player.getMainArm().getOpposite();

        evt.getPoseStack().scale(bobbing.getValue().floatValue(), bobbing.getValue().floatValue(), bobbing.getValue().floatValue());
        if (!(mc.player.getMainHandItem().is(Items.AIR))) {
            evt.getPoseStack().translate(x.getValue().floatValue(), y.getValue().floatValue(), z.getValue().floatValue());
            evt.getPoseStack().scale(scale.getValue().floatValue(), scale.getValue().floatValue(), scale.getValue().floatValue());
            if (BlockAnimationUtils.getCanStackBlock(evt.getStack())) {
                UseAnim action = mc.player.getMainHandItem().getUseAnimation();
//                System.out.println(&& !mc.player.canEat(false) );
                if (action == UseAnim.NONE && ((isBlocking && killAura.blockingStatus) || (mc.options.keyUse.isDown() && !mc.player.getOffhandItem().is(Items.GOLDEN_APPLE)))) {
                    evt.getPoseStack().pushPose();

                    boolean rightHanded = (evt.getHand() == InteractionHand.MAIN_HAND ? mc.player.getMainArm() : mc.player.getMainArm().getOpposite()) == HumanoidArm.RIGHT;
                    switch (mode.getValue().toLowerCase()) {
                        case "none" -> {
                            BlockAnimationUtils.transformSideFirstPerson(handSide, evt.getPoseStack(), evt.getEquipProgress());
                        }
                        case "rotation" -> {
                            BlockAnimationUtils.Rotation360(evt.getPoseStack(), mc.player.getAttackAnim(evt.getPartialTick()));
                        }
                        case "minecraft" -> {
                            BlockAnimationUtils.transformSideFirstPersonBlock(handSide, evt.getPoseStack(), equipProgress.getValue() ? -0.1F + evt.getEquipProgress() : 0, evt.getSwingProgress());
                        }
                        case "1.7" -> {
                            BlockAnimationUtils.transformSideFirstPersonBlock_1_7( evt.getPoseStack(),handSide, equipProgress.getValue() ? -0.1F + evt.getEquipProgress() : 0, evt.getSwingProgress());

                        }
                        case "new" -> {
                            BlockAnimationUtils.New(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "push" -> {
                            BlockAnimationUtils.Push(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "smooth" -> {
                            BlockAnimationUtils.SmoothBlock(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "flux" -> {
                            BlockAnimationUtils.Flux(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "sigma" -> {
                            BlockAnimationUtils.New2(handSide, evt.getPoseStack(), evt.getEquipProgress(), mc.player.getAttackAnim(evt.getPartialTick()));
                        }
                        case "etb" -> {
                            BlockAnimationUtils.ETB(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "sigmaold" -> {
                            BlockAnimationUtils.sigmaold(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "tap" -> {
                            BlockAnimationUtils.tap(evt.getPoseStack(), equipProgress.getValue() ? evt.getEquipProgress() : 0, evt.getSwingProgress());
                        }
                        case "custom" -> {
                            BlockAnimationUtils.custom(evt.getPoseStack(), equipProgress.getValue() ? evt.getEquipProgress() : 0, evt.getSwingProgress()
                           , xP.getValue().floatValue(), yP.getValue().floatValue(), zP.getValue().floatValue());
                        }
                        case "zoom" -> {
                            BlockAnimationUtils.Zoom(evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                        }
                        case "jello" -> {
                            BlockAnimationUtils.jello(handSide, evt.getPoseStack(), evt.getSwingProgress());
                        }
                        case "sigmanew" -> {
                            BlockAnimationUtils.transformSideFirstPersonBlock(handSide, evt.getPoseStack(), evt.getEquipProgress(), evt.getSwingProgress());
                            evt.getPoseStack().mulPose(Axis.YP.rotationDegrees(45.0F));
                            float var12 = Mth.sin(Mth.sqrt(evt.getSwingProgress()) * 3.1415927F);
                            evt.getPoseStack().mulPose(Axis.XP.rotationDegrees(var12 * -5.0F));
                            evt.getPoseStack().mulPose(Axis.ZP.rotationDegrees(var12 * 0.0F));
                            evt.getPoseStack().mulPose(Axis.YP.rotationDegrees(var12 * 25.0F));
                        }
                    }

                    mc.getEntityRenderDispatcher().getItemInHandRenderer().renderItem(mc.player, stack, rightHanded ? ItemDisplayContext.FIRST_PERSON_RIGHT_HAND : ItemDisplayContext.FIRST_PERSON_LEFT_HAND, !rightHanded, evt.getPoseStack(), evt.getBufferSource(), evt.getPackedLight());
                    evt.getPoseStack().popPose();
                    evt.setCancelled(true);

                }
            }
        }
    }

//    @SubscribeEvent(priority = EventPriority.HIGHEST)
//    public void onItemUseStart(LivingEntityUseItemEvent.Start evt) {
//        if (mc.player == null)
//            return;
//
//        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
//
//        if (killAura.isEnable() && killAura.autoBlockValue.getValue().equals("Fake") && !mc.options.keyUse.isDown())
//            return;
//
//        if (mc.player.getUseItem().getUseAnimation() != UseAnim.EAT) {
//            if (evt.getEntity() instanceof Player && AnimationUtils.getCanStackBlock(evt.getItem())) {
//                evt.setDuration(72000);
//            }
//        }
//    }


    @Override
    public String getTag() {
        return mode.getValue();
    }
}
