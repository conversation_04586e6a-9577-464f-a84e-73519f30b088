package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.world.inventory.ClickType;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;

@ModuleInfo(name = {
        @Text(label = "AutoArmor", language = Language.English),
        @Text(label = "自动穿装备", language = Language.Chinese)
}, category = Category.Combat)
@SuppressWarnings("all")

public class AutoArmor extends Module {
    @SettingInfo(name = {
            @Text(label = "MinDelay", language = Language.English),
            @Text(label = "最小延迟", language = Language.Chinese)
    })
    private final NumberSetting minDelay = new NumberSetting(200.0d, 0.0D, 2000.0d, "#");
    @SettingInfo(name = {
            @Text(label = "MaxDelay", language = Language.English),
            @Text(label = "最大延迟", language = Language.Chinese)
    })
    private final NumberSetting maxDelay = new NumberSetting(500.0d, 0.0D, 2000.0d, "#");
    @SettingInfo(name = {
            @Text(label = "OnlyINV", language = Language.English),
            @Text(label = "只在背包界面", language = Language.Chinese)
    })
    private final BooleanSetting onlyINV = new BooleanSetting(false);
    private final TimeUtils timer = new TimeUtils();


    private enum ArmorAction {
        WAITING,
        REMOVING,
        EQUIPPING
    }
    private ArmorAction currentAction = ArmorAction.WAITING;

    public AutoArmor() {
        registerSetting(maxDelay, minDelay, onlyINV);
    }

    public TimeUtils getTimer() {
        return timer;
    }

    private boolean done = false;
    public int[] slot;
    @Override
    public void onEnable() {
        slotIndex = 0;
        timer.reset();
        lastArmorAction = 0;
        done = false;
        currentAction = ArmorAction.WAITING;
    }

    private int slotIndex = 0;
    private long lastArmorAction = 0;
    private static final long MIN_INTERACTION_DELAY = 100;
    private final TimeUtils interactionTimer = new TimeUtils();

    @Override
    public String getDescription() {
        return "自动装备最好的盔甲";
    }
    private boolean opened = false;
    @EventTarget
    public void onClientTick(EventUpdate event) {
        done = false;
        if (mc.screen instanceof InventoryScreen) {
            if (!opened) {
                opened = true;
                mc.player.setSprinting(false);
                mc.options.keySprint.setDown(false);
                timer.reset();
            }
        } else {
            opened = false;
        }
        if (onlyINV.getValue() && !(mc.screen instanceof InventoryScreen)) return;
        if(!interactionTimer.hasTimeElapsed(MIN_INTERACTION_DELAY)) return;

        if(!mc.player.isAlive() || mc.player.isDeadOrDying() || mc.player.isSpectator()) return;
        KillAura aura = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if(aura.target != null) return;

        if (onlyINV.getValue() && !(mc.screen instanceof InventoryScreen)) return;
        long delay = TimeUtils.randomDelay(minDelay.getValue().intValue(), maxDelay.getValue().intValue());
        delay += (int) (Math.random() * 60);
        if(!timer.hasTimeElapsed(delay)) {
            return;
        }

        updateArmorStatus();
        if (done) {
            return;
        }

        // 获取最佳装备槽位
        if (currentAction == ArmorAction.WAITING) {
            slot = getBestArmor();
            if(slot[slotIndex] > 0) {
                if (mc.player.inventoryMenu.getSlot(slotIndex + 5).hasItem()) {
                    clickSlot(slotIndex + 5, 1, ClickType.THROW);
                    currentAction = ArmorAction.REMOVING;
                    timer.reset();
                    interactionTimer.reset();
                } else {
                    currentAction = ArmorAction.EQUIPPING;
                    timer.reset();
                }
            } else {
                slotIndex++;
                if(slotIndex == 4) slotIndex = 0;
            }
        } else if (currentAction == ArmorAction.REMOVING) {
            if (timer.hasTimeElapsed(delay)) {
                currentAction = ArmorAction.EQUIPPING;
                timer.reset();
            }
        } else if (currentAction == ArmorAction.EQUIPPING) {
            if (timer.hasTimeElapsed(delay)) {
                clickSlot(slot[slotIndex], 0, ClickType.QUICK_MOVE);
                timer.reset();
                interactionTimer.reset();
                slotIndex++;
                if(slotIndex == 4) slotIndex = 0;
                currentAction = ArmorAction.WAITING;
            }
        }

        updateArmorStatus();
    }

    private void updateArmorStatus() {
        slot = getBestArmor();
        int sum = 0;
        for (int i : slot) {
            if(i < 0) sum++;
        }
        done = sum == 4;
    }

    public boolean isDone() {
        return done;
    }
    /**
     * @return slot [1,4,2,5]
     */
    public static int[] getBestArmor() {
        int[] index = new int[4];
        for(int s = 1; s <= 4; s++) {
            index[s - 1] = -1;
            String strType = getStrType(s);
            float invLevel = -1;
            for (int i = 0; i < 45; i++) {
                if (mc.player.inventoryMenu.getSlot(i).hasItem()) {
                    ItemStack is = mc.player.inventoryMenu.getSlot(i).getItem();
                    if (!(is.getItem() instanceof ArmorItem)) continue;
                    if (!is.getDescriptionId().contains(strType)) continue;
                    float t = getBestQuality(is) + getEnchantment(is);
                    if (t > invLevel) {
                        invLevel = t;
                        if(i <= 8 && i >= 5)//如果最好的已经在身上
                            index[s - 1] = -i;
                        else index[s - 1] = i;
                    }
                }
            }
        }
        return index;
    }
    public static void clickSlot(int slotId, int mouseButton, ClickType clickType) {
        mc.gameMode.handleInventoryMouseClick(mc.player.containerMenu.containerId, slotId, mouseButton, clickType, mc.player);
    }

    public static String getStrType(int tmp) {
        switch (tmp) {
            case 1:
                return "helmet";
            case 2:
                return "chestplate";
            case 3:
                return "leggings";
            case 4:
                return "boots";
            default:
                return "";
        }
    }
    public static float getEnchantment(ItemStack stack) {
        float prot = 0;
        if (stack.getItem() instanceof ArmorItem armor) {
            prot += (float) armor.getDefense() + (100 - armor.getDefense()) * EnchantmentHelper.getItemEnchantmentLevel(Enchantments.ALL_DAMAGE_PROTECTION, stack) * 0.0075F;
            prot += (float) (EnchantmentHelper.getItemEnchantmentLevel(Enchantments.UNBREAKING, stack) / 50.0);
        }
        return prot;
    }

    public static float getBestQuality(ItemStack stack) {
        float prot = 1f;
        if (stack.getItem() instanceof ArmorItem) {
            if (stack.getDescriptionId().contains("leather")) {
                prot = 0f;
            } else if (stack.getDescriptionId().contains("golden")) {
                prot *= 1.5f;
            } else if (stack.getDescriptionId().contains("chainmail")) {
                prot *= 3.0f;
            } else if (stack.getDescriptionId().contains("iron")) {
                prot *= 7f;
            } else if (stack.getDescriptionId().contains("diamond")) {
                prot *= 10f;
            } else if (stack.getDescriptionId().contains("netherite")) {
                prot *= 20f;
            }
        }
        return prot;
    }
}
