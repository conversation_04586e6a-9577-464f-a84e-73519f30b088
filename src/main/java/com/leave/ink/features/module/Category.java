package com.leave.ink.features.module;


import com.leave.ink.features.module.annotation.CategoryInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum Category {
    @CategoryInfo(text = {
            @Text(label = "Combat", language = Language.English),
            @Text(label = "战斗类", language = Language.Chinese)
    })
    Combat(false),
    @CategoryInfo(text = {
            @Text(label = "Movement", language = Language.English),
            @Text(label = "移动类", language = Language.Chinese)
    })
    Movement(false),
    @CategoryInfo(text = {
            @Text(label = "Client", language = Language.English),
            @Text(label = "客户端", language = Language.Chinese)
    })
    Client(false),
    @CategoryInfo(text = {
            @Text(label = "Render", language = Language.English),
            @Text(label = "视觉类", language = Language.Chinese)
    })
    Render(false),
    @CategoryInfo(text = {
            @Text(label = "Other", language = Language.English),
            @Text(label = "其他类", language = Language.Chinese)
    })
    Other(false),
    @CategoryInfo(text = {
            @Text(label = "World", language = Language.English),
            @Text(label = "世界类", language = Language.Chinese)
    })
    World(false),
    @CategoryInfo(text = {
            @Text(label = "Settings", language = Language.English),
            @Text(label = "设置", language = Language.Chinese)
    })
    Settings(false),
    @CategoryInfo(text = {
            @Text(label = "Config", language = Language.English),
            @Text(label = "配置", language = Language.Chinese)
    })
    Config(false),
    @CategoryInfo(text = {
            @Text(label = "Hud", language = Language.English),
            @Text(label = "Hud", language = Language.Chinese)
    })
    Hud(false);
    private Text[] texts;

    private boolean show;
    public Animation animation = new DecelerateAnimation(150, 1, Direction.BACKWARDS);

    Category(boolean show) {
        this.show = show;
        try {
            CategoryInfo categoryInfo;
            categoryInfo = this.getClass().getField(name()).getAnnotation(CategoryInfo.class);
            if (categoryInfo != null) {
                texts = categoryInfo.text();
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }


    }

    public Text[] getTexts() {
        return texts;
    }

    public String getName(Language language) {
        return Language.getLabel(getTexts(), language);
    }

    public String getName() {
        return Language.getLabel(getTexts(), Language.getDefaultLanguage());
    }

    public String getNameKey() {
        return Language.getLabel(getTexts(), Language.getDefaultLanguage());
    }

    public static List<Category> getCategories(Category... blackList) {
        return Arrays.stream(Category.values()).filter(it ->
                        !Arrays.stream(blackList).toList().contains(it)).
                collect(Collectors.toList());
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public boolean isShow() {
        return show;
    }
}
