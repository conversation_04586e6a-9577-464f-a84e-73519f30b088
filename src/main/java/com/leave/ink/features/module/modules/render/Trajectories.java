package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.Render3DEngine;
import net.minecraft.core.BlockPos;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.item.*;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;

import java.awt.*;

@ModuleInfo(name = {
        @Text(label = "Trajectories", language = Language.English),
        @Text(label = "抛物线", language = Language.Chinese)
}, category = Category.Render)
public class Trajectories extends Module {
    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    private final ColorSetting color = new ColorSetting(new Color(7, 158, 255, 126));

    public Trajectories() {
        registerSetting(color);
    }

    private boolean isThrowable(Item item) {
        return item instanceof EnderpearlItem || item instanceof TridentItem || item instanceof ExperienceBottleItem || item instanceof SnowballItem || item instanceof EggItem || item instanceof SplashPotionItem || item instanceof LingeringPotionItem;
    }

    private float getDistance(Item item) {
        return item instanceof BowItem ? 1.0f : 0.4f;
    }

    private float getThrowVelocity(Item item) {
        if (item instanceof SplashPotionItem || item instanceof LingeringPotionItem) return 0.5f;
        if (item instanceof ExperienceBottleItem) return 0.59f;
        if (item instanceof TridentItem) return 2f;
        return 1.5f;
    }

    private int getThrowPitch(Item item) {
        if (item instanceof SplashPotionItem || item instanceof LingeringPotionItem || item instanceof ExperienceBottleItem)
            return 20;
        return 0;
    }
    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (mc.options.hideGui || !mc.options.getCameraType().isFirstPerson())
            return;

        ItemStack mainHand = mc.player.getMainHandItem();
        ItemStack offHand = mc.player.getOffhandItem();
        InteractionHand hand;

        if (mainHand.getItem() instanceof BowItem || mainHand.getItem() instanceof CrossbowItem || isThrowable(mainHand.getItem())) {
            hand = InteractionHand.MAIN_HAND;
        } else if (offHand.getItem() instanceof BowItem || offHand.getItem() instanceof CrossbowItem || isThrowable(offHand.getItem())) {
            hand = InteractionHand.OFF_HAND;
        } else return;

        boolean prevBob = mc.options.bobView().get();
        mc.options.bobView().set(false);

        if ((offHand.getItem() instanceof CrossbowItem && EnchantmentHelper.getItemEnchantmentLevel(Enchantments.MULTISHOT, offHand) != 0) ||
                (mainHand.getItem() instanceof CrossbowItem && EnchantmentHelper.getItemEnchantmentLevel(Enchantments.MULTISHOT, mainHand) != 0)) {

            calcTrajectory(hand == InteractionHand.OFF_HAND ? offHand.getItem() : mainHand.getItem(), mc.player.getYRot() - 10);
            calcTrajectory(hand == InteractionHand.OFF_HAND ? offHand.getItem() : mainHand.getItem(), mc.player.getYRot());
            calcTrajectory(hand == InteractionHand.OFF_HAND ? offHand.getItem() : mainHand.getItem(), mc.player.getYRot() + 10);

        } else {
            calcTrajectory(hand == InteractionHand.OFF_HAND ? offHand.getItem() : mainHand.getItem(), mc.player.getYRot());
        }

        mc.options.bobView().set(prevBob);
    }

    private void calcTrajectory(Item item, float yaw) {
        try {
            double x = RenderUtils.interpolate(mc.player.xOld, mc.player.getX(), mc.getFrameTime());
            double y = RenderUtils.interpolate(mc.player.yOld, mc.player.getY(), mc.getFrameTime());
            double z = RenderUtils.interpolate(mc.player.zOld, mc.player.getZ(), mc.getFrameTime());

            y = y + mc.player.getEyeHeight(mc.player.getPose()) - 0.1000000014901161;

            if (item == mc.player.getMainHandItem().getItem()) {
                x = x - Mth.cos(yaw / 180.0f * 3.1415927f) * 0.16f;
                z = z - Mth.sin(yaw / 180.0f * 3.1415927f) * 0.16f;
            } else {
                x = x + Mth.cos(yaw / 180.0f * 3.1415927f) * 0.16f;
                z = z + Mth.sin(yaw / 180.0f * 3.1415927f) * 0.16f;
            }

            final float maxDist = getDistance(item);
            double motionX = -Mth.sin(yaw / 180.0f * 3.1415927f) * Mth.cos(mc.player.getXRot() / 180.0f * 3.1415927f) * maxDist;
            double motionY = -Mth.sin((mc.player.getXRot() - getThrowPitch(item)) / 180.0f * 3.141593f) * maxDist;
            double motionZ = Mth.cos(yaw / 180.0f * 3.1415927f) * Mth.cos(mc.player.getXRot() / 180.0f * 3.1415927f) * maxDist;
            float power = mc.player.getUseItemRemainingTicks() / 20.0f;
            power = (power * power + power * 2.0f) / 3.0f;
            if (power > 1.0f) {
                power = 1.0f;
            }
            final float distance = Mth.sqrt((float) (motionX * motionX + motionY * motionY + motionZ * motionZ));
            motionX /= distance;
            motionY /= distance;
            motionZ /= distance;

            final float pow = (item instanceof BowItem ? (power * 2.0f) : item instanceof CrossbowItem ? (2.2f) : 1.0f) * getThrowVelocity(item);

            motionX *= pow;
            motionY *= pow;
            motionZ *= pow;

            if (!mc.player.onGround())
                motionY += mc.player.getDeltaMovement().y;

            Vec3 lastPos;
            for (int i = 0; i < 300; i++) {
                lastPos = new Vec3(x, y, z);
                x += motionX;
                y += motionY;
                z += motionZ;
                if (mc.level.getBlockState(new BlockPos((int) x, (int) y, (int) z)).getBlock() == Blocks.WATER) {
                    motionX *= 0.8;
                    motionY *= 0.8;
                    motionZ *= 0.8;
                } else {
                    motionX *= 0.99;
                    motionY *= 0.99;
                    motionZ *= 0.99;
                }

                if (item instanceof BowItem) motionY -= 0.05000000074505806;
                else if (mc.player.getMainHandItem().getItem() instanceof CrossbowItem) motionY -= 0.05000000074505806;
                else motionY -= 0.03f;

                Vec3 pos = new Vec3(x, y, z);

                for (Entity ent : mc.level.entitiesForRendering()) {
                    if (ent instanceof AbstractArrow || ent.equals(mc.player)) continue;
                    if (ent.getBoundingBox().intersects(new AABB(x - 0.3, y - 0.3, z - 0.3, x + 0.3, y + 0.3, z + 0.3))) {
                        Render3DEngine.OUTLINE_QUEUE.add(new Render3DEngine.OutlineAction(
                                ent.getBoundingBox(),
                                color.getValue(),
                                2f));
                        Render3DEngine.FILLED_QUEUE.add(new Render3DEngine.FillAction(
                                ent.getBoundingBox(), color.getValue()
                        ));
                        break;
                    }
                }

                BlockHitResult bhr = mc.level.clip(new ClipContext(lastPos, pos, ClipContext.Block.OUTLINE, ClipContext.Fluid.NONE, mc.player));
                if (bhr != null && bhr.getType() == HitResult.Type.BLOCK) {
                    Render3DEngine.OUTLINE_SIDE_QUEUE.add(new Render3DEngine.OutlineSideAction(
                            new AABB(bhr.getBlockPos()), color.getValue(), 2f, bhr.getDirection()
                    ));
                    Render3DEngine.FILLED_SIDE_QUEUE.add(new Render3DEngine.FillSideAction(
                            new AABB(bhr.getBlockPos()), color.getValue(), bhr.getDirection()
                    ));

                    break;
                }

                if (y <= -65) break;
                if (motionX == 0 && motionY == 0 && motionZ == 0) continue;

                Render3DEngine.drawLineDebug(lastPos, pos, color.getValue());
            }
        } catch (Exception ignored) {
        }
    }

}
