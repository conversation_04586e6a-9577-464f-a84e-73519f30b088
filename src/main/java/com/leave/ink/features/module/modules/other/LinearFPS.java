package com.leave.ink.features.module.modules.other;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "LinearFPS", language = Language.English),
        @Text(label = "线性插帧", language = Language.Chinese)
}, category = Category.Other)
public class LinearFPS extends Module {
}
