package com.leave.ink.features.module.modules.movement;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "TestModule", language = Language.English),
        @Text(label = "TestModule", language = Language.Chinese)
}, category = Category.Movement)
public class TestModule extends Module {
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delay = new NumberSetting(500.0d, 0.0D, 2000.0d, "#");

    public TestModule() {
        registerSetting(delay);
    }
}
