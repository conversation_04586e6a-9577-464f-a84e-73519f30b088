package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import net.minecraft.core.BlockPos;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.block.WebBlock;
import net.minecraft.world.phys.*;

@ModuleInfo(name = {
        @Text(label = "CobWebPlace", language = Language.English),
        @Text(label = "蜘蛛网放置(手动)", language = Language.Chinese)
}, category = Category.World)
public class CobWebPlace extends Module {
    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "范围", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(5.0, 0.0, 5.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Debug", language = Language.English),
            @Text(label = "输出", language = Language.Chinese)
    })
    private final BooleanSetting Debug = new BooleanSetting(false);

    private Rotation rotation = null;
    private Entity targetEntity = null;
    private BlockPos placePos = null;

    public CobWebPlace() {
         registerSetting(range, Debug);
    }

    @Override
    public String getDescription() {
        return "需要手动对准目标右键即可在拿蜘蛛网的情况下";
    }
    @Override
    public void onEnable() {
        reset();
    }

    @Override
    public void onDisable() {
        reset();
    }

    private void reset() {
        rotation = null;
        targetEntity = null;
        placePos = null;
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (Utils.isNull()) return;
        if (!mc.options.keyUse.isDown()) return;
        ItemStack heldItem = mc.player.getMainHandItem();
        if (heldItem.isEmpty() || heldItem.getItem() != Items.COBWEB) return;
        HitResult hitResult = mc.hitResult;
        if (hitResult == null) return;

        if (hitResult.getType() == HitResult.Type.ENTITY) {
            EntityHitResult entityHit = (EntityHitResult) hitResult;
            Entity entity = entityHit.getEntity();

            if (entity instanceof LivingEntity livingEntity) {
                targetEntity = livingEntity;
                placeWebInEntity(livingEntity);
            }
        } else {
            Vec3 eyePos = mc.player.getEyePosition();
            Vec3 lookVec = mc.player.getLookAngle().multiply(range.getValue().doubleValue(), range.getValue().doubleValue(), range.getValue().doubleValue());
            Vec3 endPos = eyePos.add(lookVec);

            EntityHitResult entityHit = rayTraceEntities(eyePos, endPos);
            if (entityHit != null && entityHit.getEntity() instanceof LivingEntity livingEntity) {
                targetEntity = livingEntity;
                placeWebInEntity(livingEntity);
            }
        }
    }

    private void placeWebInEntity(LivingEntity entity) {
        if (entity == null) return;
        BlockPos entityPos = BlockPos.containing(entity.getX(), entity.getY(), entity.getZ());
        if (mc.level.getBlockState(entityPos).getBlock() instanceof WebBlock) {
            if (Debug.getValue()) {
                mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("目标位置已有蜘蛛网"));
            }
            return;
        }
        placePos = entityPos;
        rotation = RotationUtils.getAngles(entity);
        placeWeb();
    }

    private void placeWeb() {
        if (placePos == null) return;
        RotationUtils.setRotation(rotation, 10, MovementFix.NORMAL);
        BlockHitResult placeResult = new BlockHitResult(
                Vec3.atCenterOf(placePos),
                mc.player.getDirection().getOpposite(),
                placePos,
                false
        );

        mc.gameMode.useItemOn(mc.player, InteractionHand.MAIN_HAND, placeResult);

        if (Debug.getValue()) {
            mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("在 " + placePos.toShortString() + " 放置，蜘蛛网成功"));
        }
        placePos = null;
    }

    private EntityHitResult rayTraceEntities(Vec3 start, Vec3 end) {
        Entity closestEntity = null;
        Vec3 closestVec = null;
        double closestDistance = Double.MAX_VALUE;

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (!(entity instanceof LivingEntity) || entity == mc.player) continue;
            if (mc.player.distanceTo(entity) > range.getValue().doubleValue()) continue;

            AABB boundingBox = entity.getBoundingBox().inflate(0.1);
            Vec3 hitVec = boundingBox.clip(start, end).orElse(null);

            if (hitVec != null) {
                double distance = start.distanceTo(hitVec);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestEntity = entity;
                    closestVec = hitVec;
                }
            }
        }

        return closestEntity != null ? new EntityHitResult(closestEntity, closestVec) : null;
    }

}
