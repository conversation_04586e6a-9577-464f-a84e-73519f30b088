package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.AutoArmor;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.ClickType;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.item.PickaxeItem;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.ShovelItem;
import net.minecraft.world.item.HoeItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.Comparator;

@ModuleInfo(name = {
        @Text(label = "ChestStealer", language = Language.English),
        @Text(label = "箱子小偷", language = Language.Chinese)
}, category = Category.World)
public class ChestStealer extends Module {
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting maxDelay = new NumberSetting(200.0, 0.0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "MinDelay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting minDelay = new NumberSetting(200.0, 0.0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "AutoClose", language = Language.English),
            @Text(label = "自动关闭", language = Language.Chinese)
    })
    private final BooleanSetting autoClose = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Filter", language = Language.English),
            @Text(label = "过滤", language = Language.Chinese)
    })
    private final BooleanSetting filter = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Random Click", language = Language.English),
            @Text(label = "随机点击", language = Language.Chinese)
    })
    private final BooleanSetting randomClick = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "IgnoreMouseInput", language = Language.English),
            @Text(label = "忽略鼠标输入", language = Language.Chinese)
    })
    public final BooleanSetting disableClick = new BooleanSetting(true);
    private final TimeUtils timer = new TimeUtils();

    public ChestStealer() {
        registerSetting(maxDelay,minDelay, autoClose, filter,randomClick,disableClick);
    }
    private boolean opened = false;
    public boolean hasItems = false;
    @EventTarget
    public void onUpdate(EventUpdate event) {
        if(Utils.isNull()) return;
        if (!disableClick.getValue() && (mc.options.keyAttack.isDown() || mc.options.keyUse.isDown())) {
            return;
        }
        hasItems = false;
        if (mc.player.containerMenu instanceof ChestMenu) {
            if (!opened) {
                opened = true;
                mc.player.setSprinting(false);
                mc.options.keySprint.setDown(false);
                timer.reset();
            }
        } else {
            opened = false;
        }

        if(!mc.player.isAlive() || mc.player.isDeadOrDying() || mc.player.isSpectator()) return;
        KillAura aura = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if(aura.target != null) return;
        InvClear invClear = ((InvClear) Main.INSTANCE.moduleManager.getModule("InvClear"));


        long delay = TimeUtils.randomDelay(minDelay.getValue().intValue(), maxDelay.getValue().intValue());
        delay += (int) (Math.random() * 100);
        if (timer.hasTimeElapsed(delay)) {
            if (mc.player.containerMenu instanceof ChestMenu chestMenu) {
                boolean playerAlreadyHasLavaBucket = false;
                boolean playerAlreadyHasWaterBucket = false;
                for (int k = 0; k < mc.player.getInventory().getContainerSize(); ++k) {
                    ItemStack itemStack = mc.player.getInventory().getItem(k);
                    if (!itemStack.isEmpty()) {
                        if (itemStack.getItem() == Items.LAVA_BUCKET) {
                            playerAlreadyHasLavaBucket = true;
                        }
                        if (itemStack.getItem() == Items.WATER_BUCKET) {
                            playerAlreadyHasWaterBucket = true;
                        }
                    }
                    if (playerAlreadyHasLavaBucket && playerAlreadyHasWaterBucket) {
                        break;
                    }
                }

                List<Slot> normalItems = new ArrayList<>();
                List<Slot> blockItems = new ArrayList<>();
                int chestInventorySize = chestMenu.getRowCount() * 9;

                for (int i = 0; i < chestInventorySize; i++) {
                    Slot slot = chestMenu.getSlot(i);

                    if (slot.hasItem()) {
                        ItemStack itemInSlot = slot.getItem();
                        Item item = itemInSlot.getItem();
                        boolean useful = false;

                        boolean isBlock = invClear.isCountedBlock(itemInSlot);

                        if (filter.getValue()) {
                            if (item instanceof ArmorItem) {
                                int[] bestArmorInventory = AutoArmor.getBestArmor();
                                int s = 0;
                                String descriptionId = item.getDescriptionId();
                                if (descriptionId.contains("helmet")) s = 1;
                                else if (descriptionId.contains("chestplate")) s = 2;
                                else if (descriptionId.contains("leggings")) s = 3;
                                else if (descriptionId.contains("boots")) s = 4;

                                if (s > 0) {
                                    int bestArmorSlotIndex = bestArmorInventory[s - 1];
                                    if (bestArmorSlotIndex < 0) bestArmorSlotIndex = -bestArmorSlotIndex;
                                    ItemStack currentBestArmorStack = mc.player.inventoryMenu.getSlot(bestArmorSlotIndex).getItem();
                                    float bestLevel = AutoArmor.getBestQuality(currentBestArmorStack) + AutoArmor.getEnchantment(currentBestArmorStack);
                                    float currentLevel = AutoArmor.getBestQuality(itemInSlot) + AutoArmor.getEnchantment(itemInSlot);
                                    useful = currentLevel > bestLevel;
                                }
                            } else if (item instanceof SwordItem || item instanceof AxeItem || item instanceof PickaxeItem || item instanceof ShovelItem || item instanceof HoeItem) {
                                Class<? extends Item> toolClass = (Class<? extends Item>) item.getClass();
                                float newItemScore = invClear.getToolScore(itemInSlot);
                                List<InvClear.RankedItemStack> existingItems = invClear.getAllItemsOfType(toolClass, true);

                                if (existingItems.isEmpty()) {
                                    useful = true;
                                } else {
                                    existingItems.sort(Comparator.comparingDouble(InvClear.RankedItemStack::getScore).reversed());
                                    useful = newItemScore > existingItems.get(0).getScore();
                                }
                            } else {
                                useful = invClear.isUseful(itemInSlot, -1);
                            }
                        } else {
                            useful = true;
                        }

                        if (useful) {
                            if (item == Items.LAVA_BUCKET && playerAlreadyHasLavaBucket) {
                                useful = false;
                            }
                            if (item == Items.WATER_BUCKET && playerAlreadyHasWaterBucket) {
                                useful = false;
                            }
                        }

                        if(useful) {
                            if (isBlock) {
                                blockItems.add(slot);
                            } else {
                                normalItems.add(slot);
                            }
                        }
                    }
                }

                hasItems = false;
                Slot slotToTake = null;

                if (!normalItems.isEmpty()) {
                    Random random = new Random();
                    int slot = randomClick.getValue() ? random.nextInt(normalItems.size()) : 0;
                    slotToTake = normalItems.get(slot);
                }

                else if (!blockItems.isEmpty()) {
                    Random random = new Random();
                    int slot = randomClick.getValue() ? random.nextInt(blockItems.size()) : 0;
                    Slot blockSlot = blockItems.get(slot);
                    ItemStack blockItem = blockSlot.getItem();
                    if (!invClear.wouldExceedBlockLimit(blockItem.getCount())) {
                        slotToTake = blockSlot;
                    } else if (autoClose.getValue()) {
                        mc.player.closeContainer();
                        return;
                    }
                }

                if (slotToTake != null) {
                    mc.gameMode.handleInventoryMouseClick(chestMenu.containerId, slotToTake.index, 0, ClickType.QUICK_MOVE, mc.player);
                    hasItems = true;
                    timer.reset();
                }

                if (autoClose.getValue() && !hasItems) {
                    mc.player.closeContainer();
                }
            }

        }
    }
}
