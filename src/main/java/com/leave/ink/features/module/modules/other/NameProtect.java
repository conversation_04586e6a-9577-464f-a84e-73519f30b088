package com.leave.ink.features.module.modules.other;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventText;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.network.chat.Component;
import net.minecraft.util.FormattedCharSequence;

@ModuleInfo(name = {
        @Text(label = "NameProtect", language = Language.English),
        @Text(label = "名字保护", language = Language.Chinese)
}, category = Category.Other)
public class NameProtect extends Module {
    @EventTarget
    public void onText(EventText event) {
        if (Utils.isNull())
            return;

        if (event.getText() != null) {
            if (event.getText().trim().contains(mc.player.getName().getString())) {
                event.setText(event.getText().replace(mc.player.getName().getString(), "Hide"));
            }
        }

        String formattedChar = event.getStringFromFormattedCharSequence();
        if (formattedChar.contains(mc.player.getName().getString())) {
            FormattedCharSequence formattedCharSequence = Component.literal(formattedChar.replace(mc.player.getName().getString(), "Hide")).getVisualOrderText();
            event.setFormattedCharSequence(formattedCharSequence);
        }
    }

    @Override
    public String getSpaceName() {
        return "Name Protect";
    }
}
