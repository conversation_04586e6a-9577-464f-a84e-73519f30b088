package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.client.KeyMapping;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

import java.util.Arrays;@ModuleInfo(name = {
        @Text(label = "KeepRange", language = Language.English),
        @Text(label = "保持距离", language = Language.Chinese)
}, category = Category.Movement)
public class KeepRange extends Module {
    public static KeepRange instance;

    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(3.0, 0.5, 7.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Scan Radius", language = Language.English),
            @Text(label = "扫描半径", language = Language.Chinese)
    })
    private final NumberSetting scanRadius = new NumberSetting(6.0, 1.0, 15.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Buffer", language = Language.English),
            @Text(label = "缓冲", language = Language.Chinese)
    })
    private final NumberSetting buffer = new NumberSetting(15.0, 1.0, 20.0, "#.0");
    
    @SettingInfo(name = {
            @Text(label = "Target Mode", language = Language.English),
            @Text(label = "目标模式", language = Language.Chinese)
    })
    private final ModeSetting targetMode = new ModeSetting("KillAura", Arrays.asList("KillAura", "Normal"));

    @SettingInfo(name = {
            @Text(label = "Safety Check", language = Language.English),
            @Text(label = "安全检测", language = Language.Chinese)
    })
    private final BooleanSetting safetyCheck = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Display Warning", language = Language.English),
            @Text(label = "显示警告", language = Language.Chinese)
    })
    private final BooleanSetting displayWarning = new BooleanSetting(true);
    private LivingEntity target = null;
    private boolean isPressingSKey = false;
    private boolean notification = false;
    private long lastWarningTime = 0;

    public KeepRange() {
        instance = this;
        registerSetting(range, scanRadius, buffer, targetMode, safetyCheck, displayWarning);
    }

    @Override
    public String getDescription() {
        return "自动与目标保持距离，同时避免掉入虚空或危险地形";
    }

    @Override
    protected void onEnable() {
        target = null;
        reset();
        notification = false;
        lastWarningTime = 0;
    }

    @Override
    protected void onDisable() {
        target = null;
        reset();
        notification = false;
        lastWarningTime = 0;
    }    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (mc.player == null || mc.level == null) {
            target = null;
            reset();
            return;
        }

        LivingEntity oldTarget = target;
        
        // 获取目标
        if (targetMode.getValue().equals("KillAura")) {
            // 直接从KillAura获取目标
            KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
            if (killAura != null && killAura.isEnable()) {
                target = killAura.target;
            } else {
                target = null;
            }
        } else {
            // 普通模式下查找目标
            findIndependentTarget();
        }

        // 目标变化时重置S键状态
        if (oldTarget != target && isPressingSKey) {
            reset();
        }

        // 没有目标或目标无效时重置
        if (target == null || !target.isAlive() || target.isDeadOrDying()) {
            if (isPressingSKey) {
                reset();
            }
            if (notification) {
                notification = false;
            }
            return;
        }        // 计算与目标的距离和应保持的距离
        double distance = mc.player.distanceTo(target);
        double rangeValue = this.range.getValue().doubleValue();
        double bufferValue = this.buffer.getValue().doubleValue() / 100.0;

        if (distance < (rangeValue - bufferValue)) { // 需要后退
            if (safetyCheck.getValue()) {
                boolean safe = safeWalk();
                
                if (safe) {
                    if (!isPressingSKey) {
                        KeyMapping.set(mc.options.keyDown.getKey(), true);
                        isPressingSKey = true;
                    }
                    notification = false;
                } else {
                    if (isPressingSKey) {
                        KeyMapping.set(mc.options.keyDown.getKey(), false);
                        isPressingSKey = false;
                    }
                    
                    if (displayWarning.getValue() && !notification && System.currentTimeMillis() - lastWarningTime > 2000) {
                        mc.player.displayClientMessage(Component.literal("§c警告: 检测到危险地形，无法后退!"), true);
                        lastWarningTime = System.currentTimeMillis();
                        notification = true;
                    }
                }
            } else {
                if (!isPressingSKey) {
                    KeyMapping.set(mc.options.keyDown.getKey(), true);
                    isPressingSKey = true;
                }
            }
        } else { // 不需要后退
            if (isPressingSKey) {
                KeyMapping.set(mc.options.keyDown.getKey(), false);
                isPressingSKey = false;
            }
            
            if (notification) {
                notification = false;
            }
        }
    }    
    /**
     * 检查玩家是否可以安全后退
     */
    private boolean safeWalk() {
        if (mc.player == null || mc.level == null) {
            return false;
        }

        // 获取玩家位置和朝向
        Vec3 playerPos = mc.player.position();
        float yaw = mc.player.getYRot();
        double radians = Math.toRadians(yaw);
        
        // 计算玩家身后的位置（0.8格）- 使用与Cherish项目一致的计算方式
        BlockPos centralPointBehind = new BlockPos(
                (int)Math.floor(playerPos.x + Math.sin(radians) * 0.8),
                (int)Math.floor(playerPos.y),
                (int)Math.floor(playerPos.z + -Math.cos(radians) * 0.8)
        );
        
        // 检查身后是否是岩浆
        if (mc.level.getBlockState(centralPointBehind).is(Blocks.LAVA)) {
            return false;
        }
        
        // 检查身后下方的方块
        BlockPos pos = centralPointBehind.below();
        BlockState state = mc.level.getBlockState(pos);
        
        // 检查下方是否是岩浆
        if (state.is(Blocks.LAVA)) {
            return false;
        }
        
        // 检查是否是空气（可能是虚空）
        if (state.isAir()) {
            // 如果低于世界最低高度，不安全
            if (pos.getY() < mc.level.getMinBuildHeight()) {
                return false;
            }
            
            // 检查再下一格是否也是空气
            if (mc.level.getBlockState(pos.below()).isAir()) {
                // 如果再下一格也是空气，检查再下两格是否有支撑
                return pos.below().getY() >= mc.level.getMinBuildHeight() 
                        && !mc.level.getBlockState(pos.below().below()).isAir();
            }
        }
        
        return true;
    }    private void findIndependentTarget() {
        if (mc.player == null || mc.level == null) {
            target = null;
            return;
        }

        target = null;
        float scanRadiusValue = scanRadius.getValue().floatValue();
        
        // 查找范围内最近的有效目标
        double minDistance = Double.MAX_VALUE;
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity livingEntity 
                    && isValidTarget(livingEntity) 
                    && mc.player.distanceTo(livingEntity) <= scanRadiusValue 
                    && mc.player.hasLineOfSight(livingEntity)) {
                
                double distance = mc.player.distanceTo(livingEntity);
                if (distance < minDistance) {
                    minDistance = distance;
                    target = livingEntity;
                }
            }
        }
    }    private boolean isValidTarget(LivingEntity entity) {
        if (entity == mc.player) return false;
        if (Targets.isTeam(entity)) return false;
        return Utils.isValidEntity(entity);
    }

    private void reset() {
        if (isPressingSKey) {
            KeyMapping.set(mc.options.keyDown.getKey(), false);
        }
        isPressingSKey = false;
    }
}