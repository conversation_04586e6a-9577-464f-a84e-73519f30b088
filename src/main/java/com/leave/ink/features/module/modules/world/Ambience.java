package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundGameEventPacket;
import net.minecraft.network.protocol.game.ClientboundSetTimePacket;

import java.util.Arrays;
@ModuleInfo(name = {
        @Text(label = "Ambience", language = Language.English),
        @Text(label = "世界变化", language = Language.Chinese)
}, category = Category.World)
public class Ambience extends Module {
    @SettingInfo(name = {
            @Text(label = "TimeMode", language = Language.English),
            @Text(label = "时间模式", language = Language.Chinese)
    })
    private final ModeSetting timeModeValue = new ModeSetting("Normal", Arrays.asList("None", "Normal", "Custom"));
    @SettingInfo(name = {
            @Text(label = "WeatherMode", language = Language.English),
            @Text(label = "天气模式", language = Language.Chinese)
    })
    private final ModeSetting weatherModeValue = new ModeSetting("None", Arrays.asList("None", "Sunny", "Rainy", "Thunder"));
    @SettingInfo(name = {
            @Text(label = "CustomTime", language = Language.English),
            @Text(label = "自定义模式时间", language = Language.Chinese)
    })
    private final NumberSetting customWorldTimeValue = new NumberSetting( 1000, 0, 24000, "#");
    @SettingInfo(name = {
            @Text(label = "ChangeWorldTimeSpeed", language = Language.English),
            @Text(label = "更改世界时间速度", language = Language.Chinese)
    })
    private final NumberSetting changeWorldTimeSpeedValue = new NumberSetting(150, 10, 500, "#");
    @SettingInfo(name = {
            @Text(label = "WeatherStrength", language = Language.English),
            @Text(label = "天气强度", language = Language.Chinese)
    })
    private final NumberSetting weatherStrengthValue = new NumberSetting(1, 0f, 1, "#.00");

    private long i = 0L;

    public Ambience() {
        registerSetting(timeModeValue, weatherModeValue, customWorldTimeValue, changeWorldTimeSpeedValue, weatherStrengthValue);
    }

    @Override
    public String getDescription() {
        return "改变世界的天气";
    }

    @Override
    public void onDisable() {
        if (Utils.isNull())
            return;

        i = 0;
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (Utils.isNull()) return;

        switch (timeModeValue.getValue().toLowerCase()) {
            case "normal" -> {
                if (i < 24000) {
                    i += changeWorldTimeSpeedValue.getValue().longValue();
                } else {
                    i = 0;
                }
                mc.level.setDayTime(i);
            }
            case "custom" -> mc.level.setDayTime(customWorldTimeValue.getValue().longValue());
        }

        switch (weatherModeValue.getValue().toLowerCase()) {
            case "sunny" -> {
                mc.level.setRainLevel(0f);
                mc.level.setThunderLevel(0f);
            }
            case "rainy" -> {
                mc.level.setRainLevel(weatherStrengthValue.getValue().floatValue());
                mc.level.setThunderLevel(0f);
            }
            case "thunder" -> {
                mc.level.setRainLevel(weatherStrengthValue.getValue().floatValue());
                mc.level.setThunderLevel(weatherStrengthValue.getValue().floatValue());
            }
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        final Packet<?> packet = event.getPacket();
        if (!timeModeValue.getValue().equals("None") && packet instanceof ClientboundSetTimePacket) {
            event.setCancelled(true);
        }

        if (!weatherModeValue.getValue().equals("None") && packet instanceof ClientboundGameEventPacket) {
            if (((ClientboundGameEventPacket) packet).getEvent() == ClientboundGameEventPacket.STOP_RAINING ||
                    ((ClientboundGameEventPacket) packet).getEvent() == ClientboundGameEventPacket.START_RAINING) {
                event.setCancelled(true);
            }
        }
    }

    @Override
    public String getTag() {
        return timeModeValue.getValue();
    }
}
