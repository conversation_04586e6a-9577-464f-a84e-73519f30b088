package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

@ModuleInfo(name = {
        @Text(label = "FullBright", language = Language.English),
        @Text(label = "夜视", language = Language.Chinese)
}, category = Category.Render)
public class FullBright extends Module {
    @Override
    public void onDisable() {
        mc.player.removeEffect(MobEffects.NIGHT_VISION);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        mc.player.addEffect(new MobEffectInstance(MobEffects.NIGHT_VISION, 1337, 0, false, false));
    }
}
