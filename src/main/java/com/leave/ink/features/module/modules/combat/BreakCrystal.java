package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.rotation.*;
import lombok.Getter;
import net.minecraft.network.protocol.game.*;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.boss.enderdragon.EndCrystal;
import net.minecraft.world.phys.*;

@ModuleInfo(name = {
        @Text(label = "BreakCrystal", language = Language.English),
        @Text(label = "自动水晶", language = Language.Chinese)
}, category = Category.Combat)
public class BreakCrystal extends Module {
    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "范围", language = Language.Chinese)
    })
    private final NumberSetting targetRange = new NumberSetting(3, 1, 5, "#.0");

    @SettingInfo(name = {
            @Text(label = "Move Fix", language = Language.English),
            @Text(label = "移动修复", language = Language.Chinese)
    })
    private final BooleanSetting movefixValue = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "SilentRotation", language = Language.English),
            @Text(label = "静态转头", language = Language.Chinese)
    })
    private final BooleanSetting silentRotationValue = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "SmoothRotation", language = Language.English),
            @Text(label = "平滑转头", language = Language.Chinese)
    })
    private final BooleanSetting smoothRotationValue = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Smooth Rotation Speed", language = Language.English),
            @Text(label = "平滑转头速度", language = Language.Chinese)
    })
    private final NumberSetting smoothRotationSpeed = new NumberSetting(180, 0, 180, "#");

    @SettingInfo(name = {
            @Text(label = "Wall Check", language = Language.English),
            @Text(label = "墙壁检测", language = Language.Chinese)
    })
    private final BooleanSetting wallCheck = new BooleanSetting(true);

    public BreakCrystal() {
        registerSetting(targetRange, movefixValue, silentRotationValue, smoothRotationValue, smoothRotationSpeed, wallCheck);
    }




    @Getter
    private Rotation rotation = null;

    private boolean isEntityVisible(Entity entity) {
        if (!wallCheck.getValue()) return true;
        Vec3 eyePos = new Vec3(mc.player.getX(),
                mc.player.getY() + mc.player.getEyeHeight(),
                mc.player.getZ());

        Vec3 entityCenter = entity.getBoundingBox().getCenter();

        HitResult result = mc.level.clip(new net.minecraft.world.level.ClipContext(
                eyePos,
                entityCenter,
                net.minecraft.world.level.ClipContext.Block.COLLIDER,
                net.minecraft.world.level.ClipContext.Fluid.NONE,
                mc.player));

        if (result.getType() == HitResult.Type.BLOCK) {
            return false;
        }

        double maxDistance = mc.player.distanceTo(entity);
        Vec3 lookVec = mc.player.getViewVector(1.0F).scale(maxDistance);
        Vec3 targetVec = eyePos.add(lookVec);

        EntityHitResult entityResult = rayTraceEntities(eyePos, targetVec, maxDistance, entity);
        return entityResult != null && entityResult.getEntity() == entity;
    }


    private EntityHitResult rayTraceEntities(Vec3 start, Vec3 end, double maxDistance, Entity targetEntity) {
        Entity result = null;
        double distance = maxDistance;
        Vec3 hitVec = null;

        Vec3 direction = end.subtract(start).normalize().scale(0.1);
        Vec3 pos = start.add(0, 0, 0);

        for (int i = 0; i < maxDistance * 10; i++) {
            if (targetEntity.getBoundingBox().contains(pos)) {
                return new EntityHitResult(targetEntity, pos);
            }
            pos = pos.add(direction);
        }

        return null;
    }

    @EventTarget
    public void onPreMotion(EventUpdate event) {
        if (Utils.isNull() || mc.gameMode == null || mc.getConnection() == null) return;

        Entity entity = null;
        for (Entity ent : mc.level.entitiesForRendering()) {
            if (mc.player.distanceTo(ent) < targetRange.getValue().doubleValue()) {
                if (ent instanceof EndCrystal && isEntityVisible(ent)) {
                    entity = ent;
                    break;
                }
            }
        }

        if (entity != null) {
            rotation = RotationUtils.getAngles(entity);

            if (silentRotationValue.getValue()) {
                double speed = smoothRotationValue.getValue() ?
                        smoothRotationSpeed.getValue().doubleValue() :
                        180.0;

                MovementFix movementFix = movefixValue.getValue() ?
                        MovementFix.NORMAL :
                        MovementFix.OFF;

                RotationUtils.setRotation(rotation, speed, movementFix);
            } else {
                rotation.toPlayer(mc.player);
            }

            mc.getConnection().send(ServerboundInteractPacket.createAttackPacket(entity, mc.player.isShiftKeyDown()));
            mc.player.attack(entity);
            mc.player.swing(InteractionHand.MAIN_HAND);
        }
    }

    @Override
    protected void onEnable() {
        rotation = null;
    }
}