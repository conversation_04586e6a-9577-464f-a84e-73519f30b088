package com.leave.ink.features.module.modules.other;

import com.darkmagician6.eventapi.EventTarget;
import com.darkmagician6.eventapi.types.Priority;
import com.leave.ink.events.EventBlink;
import com.leave.ink.events.EventPacket;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.wrapper.WrapperUtils;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.gui.screens.DeathScreen;
import net.minecraft.client.gui.screens.ProgressScreen;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;

import java.util.Random;

@ModuleInfo(name = {
        @Text(label = "Disabler", language = Language.English),
        @Text(label = "禁用器", language = Language.Chinese)
}, category = Category.Other)
public class Disabler extends Module {
    public static Disabler instance;

    @SettingInfo(name = {
            @Text(label = "GrimBadPacketsA", language = Language.English),
            @Text(label = "Grim坏包A绕过", language = Language.Chinese)
    })
    private final BooleanSetting badPackets_A = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "DuplicateRotPlace", language = Language.English),
            @Text(label = "重复旋转放置", language = Language.Chinese)
    })
    private final BooleanSetting duplicateRotPlace = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "AimDuplicateLook", language = Language.English),
            @Text(label = "重复视角", language = Language.Chinese)
    })
    private final BooleanSetting aimDuplicateLook = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Aim360", language = Language.English),
            @Text(label = "重复视角", language = Language.Chinese)
    })
    public final BooleanSetting aim360 = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Blink_Grim", language = Language.English),
            @Text(label = "瞬移", language = Language.Chinese)
    })
    private final BooleanSetting blink_grim = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Grim", language = Language.English),
            @Text(label = "Grim绕过", language = Language.Chinese)
    })
    public final BooleanSetting grim = new BooleanSetting(true,
            new SettingAttribute<>(badPackets_A, true),
            new SettingAttribute<>(duplicateRotPlace, true),
            new SettingAttribute<>(aimDuplicateLook, true),
            new SettingAttribute<>(aim360,true),
            new SettingAttribute<>(blink_grim,true)
    );

    @SettingInfo(name = {
            @Text(label = "FastSwitch", language = Language.English),
            @Text(label = "快速切换", language = Language.Chinese)
    })
    private final BooleanSetting fastSwitch = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Inventory Frequency", language = Language.English),
            @Text(label = "背包频率", language = Language.Chinese)
    })
    private final BooleanSetting invFrequency = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "AimStep", language = Language.English),
            @Text(label = "瞄准", language = Language.Chinese)
    })
    private final BooleanSetting aimStep = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Rotation", language = Language.English),
            @Text(label = "转头", language = Language.Chinese)
    })
    private final BooleanSetting perfectRot = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Aca", language = Language.English),
            @Text(label = "Aca绕过", language = Language.Chinese)
    })
    private final BooleanSetting aca = new BooleanSetting(true,
            new SettingAttribute<>(fastSwitch, true),
            new SettingAttribute<>(invFrequency, true),
            new SettingAttribute<>(aimStep, true),
            new SettingAttribute<>(perfectRot,true)
    );

    @SettingInfo(name = {
            @Text(label = "Blink_Themis", language = Language.English),
            @Text(label = "瞬移", language = Language.Chinese)
    })
    private final BooleanSetting blink_themis = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Themis", language = Language.English),
            @Text(label = "Themis绕过", language = Language.Chinese)
    })
    private final BooleanSetting themis = new BooleanSetting(true,
            new SettingAttribute<>(blink_themis, true)
    );

    @SettingInfo(name = {
            @Text(label = "OnlyServer", language = Language.English),
            @Text(label = "仅在服务器生效", language = Language.Chinese)
    })
    private final BooleanSetting onlyServer = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "DeBug", language = Language.English),
            @Text(label = "调试输出", language = Language.Chinese)
    })
    private final BooleanSetting debug = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Other", language = Language.English),
            @Text(label = "其它设置", language = Language.Chinese)
    })
    private final BooleanSetting ohter = new BooleanSetting(true,
            new SettingAttribute<>(onlyServer, true),
            new SettingAttribute<>(debug,true)
    );

    private ServerboundContainerClosePacket storedClosePacket = null;
    private final TimeUtils inventoryCloseTimer = new TimeUtils();
    private long time = System.currentTimeMillis();
    private boolean inventoryJustOpened = false;
    private long lastInventoryOpenTime = 0;
    private long delayTime = 0;
    private int lastSlot = -1;
    private int count = 0;

    private static final double[] PERFECT_PATTERNS = {0.1, 0.25};
    private static final double EPSILON = 0.0000000001;
    private final Random random = new Random();

    private float lastSentPitch = 0;
    private float lastSentYaw = 0;
    private float lastPitch = 0;
    private float lastYaw = 0;

    private float lastPlacedDeltaPitch = 0;
    private float lastPlacedDeltaYaw = 0;
    private float playerPitch = 0;
    private float playerYaw = 0;
    private float deltaPitch = 0;
    private float deltaYaw = 0;
    private boolean rotated = false;

    public Disabler() {
        instance = this;
        registerSetting(grim, aca, themis, ohter);
    }

    @Override
    public void onEnable() {
        reset();
    }

    @Override
    public void onDisable() {
        reset();
    }

    @EventTarget(Priority.HIGHEST)
    public void onBlink(EventBlink event) {
        if (Utils.isNull() || (mc.isSingleplayer() && onlyServer.getValue())) return;

        Packet<?> packet = event.getPacket();

        if (mc.player.isSpectator() || !mc.player.isAlive() || mc.player.isDeadOrDying() || mc.screen instanceof ProgressScreen) {
            reset();
            return;
        }

        if (grim.getValue() && blink_grim.getValue()) {
            if (isNoPushPacket(packet)) {
                if (event.getBlinkTicks() % 10 == 0) {
                    event.setCancelled(false);
                    debugMessage("Grim-Blink: Cancelled no-push packet at tick " + event.getBlinkTicks() +
                            ", packet type: " + packet.getClass().getSimpleName() +
                            ", interval: 10 ticks");
                }
            }
        }
    }

    public boolean isNoPushPacket(Packet<?> packet) {
        return packet instanceof ServerboundMovePlayerPacket;
    }

    @EventTarget(Priority.HIGHEST)
    public void onPacket(EventPacket event) {
        if (Utils.isNull() || (mc.isSingleplayer() && onlyServer.getValue())) return;

        Packet<?> packet = event.getPacket();

        if (packet instanceof ClientboundLoginPacket) {
            reset();
        }

        if (mc.player.isSpectator() || !mc.player.isAlive() || mc.player.isDeadOrDying() || mc.screen instanceof ProgressScreen || mc.screen instanceof DeathScreen) {
            reset();
            return;
        }

        if (storedClosePacket != null && inventoryCloseTimer.hasTimeElapsed(delayTime)) {
            PacketUtils.sendPacketNoEvent(storedClosePacket);
            debugMessage("InventoryFrequency: Released stored close packet");
            storedClosePacket = null;
        }

        if (packet instanceof ClientboundOpenScreenPacket) {
            lastInventoryOpenTime = System.currentTimeMillis();
            inventoryJustOpened = true;
            debugMessage("Inventory opened at: " + lastInventoryOpenTime);
        }

        if (packet instanceof ServerboundSetCarriedItemPacket wrapper) {
            int targetSlot = wrapper.getSlot();

            if (grim.getValue() && badPackets_A.getValue() && targetSlot == lastSlot && targetSlot != -1) {
                event.setCancelled(true);
                debugMessage("BadPacketsA: Cancelled duplicate slot packet: " + targetSlot);
                return;
            }

            if (aca.getValue() && fastSwitch.getValue() && lastSlot != -1 && targetSlot != lastSlot) {
                generateIntermediateSlots(lastSlot, targetSlot);
            }

            lastSlot = targetSlot;
            debugMessage("Processed slot switch: " + lastSlot + " -> " + targetSlot);
        }

        if (aca.getValue() && invFrequency.getValue()) {
            if (packet instanceof ServerboundContainerClosePacket closePacket && inventoryJustOpened) {
                long currentTime = System.currentTimeMillis();
                long timeSinceOpen = currentTime - lastInventoryOpenTime;

                if (timeSinceOpen <= 150) {
                    event.setCancelled(true);
                    storedClosePacket = closePacket;

                    delayTime = 151 - timeSinceOpen;
                    inventoryCloseTimer.reset();
                    debugMessage("InventoryFrequency: Storing close packet, will send after " + delayTime + "ms");

                    inventoryJustOpened = false;
                    return;
                }

                inventoryJustOpened = false;
                debugMessage("InventoryFrequency: Allowed close packet after " + timeSinceOpen + "ms");
            }
        }

        if (themis.getValue() && blink_themis.getValue()) {
            if (System.currentTimeMillis() - time > 200) {
                if (count == 0) {
                    PacketUtils.sendPacketNoEvent(new ServerboundPongPacket(0));
                    debugMessage("ValidC0F, Themis Blink: Send valid c0f successful.");
                }
                time = System.currentTimeMillis();
                count = 0;
            }
            if (packet instanceof ServerboundMovePlayerPacket.StatusOnly || packet instanceof ServerboundPongPacket) {
                count++;
            }
        }

        if (grim.getValue() && aimDuplicateLook.getValue()) {
            if (packet instanceof ServerboundMovePlayerPacket movePacket && movePacket.hasRotation()) {
                float yaw = WrapperUtils.getPacketYRot(movePacket);
                float pitch = WrapperUtils.getPacketXRot(movePacket);

                if (yaw == lastSentYaw && pitch == lastSentPitch) {
                    float baseRange = isPlayerMoving() ? 0.0003f : 0.00015f;
                    float yawRange = baseRange * (0.8f + random.nextFloat() * 0.4f);
                    float pitchRange = baseRange * (0.8f + random.nextFloat() * 0.4f);

                    float yawOffset = (float) (random.nextGaussian() * yawRange * 0.3f);
                    float pitchOffset = (float) (random.nextGaussian() * pitchRange * 0.3f);

                    yawOffset = MathUtils.clamp_float(yawOffset, -0.0005f, 0.0005f);
                    pitchOffset = MathUtils.clamp_float(pitchOffset, -0.0003f, 0.0003f);

                    float newYaw = yaw + yawOffset;
                    float newPitch = MathUtils.clampPitch_To90(pitch + pitchOffset);

                    WrapperUtils.setPacketYRot(movePacket, newYaw);
                    WrapperUtils.setPacketXRot(movePacket, newPitch);

                    debugMessage("AimDuplicateLook: Dynamic offset Yaw:" + yaw + "->" + newYaw + " Pitch:" + pitch + "->" + newPitch);
                }

                lastSentYaw = WrapperUtils.getPacketYRot(movePacket);
                lastSentPitch = WrapperUtils.getPacketXRot(movePacket);
            }
        }

        if (grim.getValue() && duplicateRotPlace.getValue()) {
            if (packet instanceof ServerboundMovePlayerPacket movePacket) {
                if (movePacket.hasRotation()) {
                    float lastPlayerYaw = playerYaw;
                    float lastPlayerPitch = playerPitch;

                    playerYaw = WrapperUtils.getPacketYRot(movePacket);
                    playerPitch = WrapperUtils.getPacketXRot(movePacket);

                    deltaYaw = Math.abs(playerYaw - lastPlayerYaw);
                    deltaPitch = Math.abs(playerPitch - lastPlayerPitch);
                    rotated = true;

                    if (deltaYaw > 2) {
                        float yawDiff = Math.abs(deltaYaw - lastPlacedDeltaYaw);
                        if (yawDiff < 0.0001) {
                            float randomOffset = (float) (0.001 + Math.random() * 0.009);
                            float newYaw = playerYaw - randomOffset;
                            WrapperUtils.setPacketYRot(movePacket, newYaw);
                            debugMessage("DuplicateRotPlace: Modified yaw from " + playerYaw + " to " + newYaw + " (yawDiff: " + yawDiff + ")");
                        }
                    }

                    if (deltaPitch > 2) {
                        float pitchDiff = Math.abs(deltaPitch - lastPlacedDeltaPitch);
                        if (pitchDiff < 0.0001) {
                            float randomOffset = (float) (0.001 + Math.random() * 0.009);
                            float newPitch = MathUtils.clampPitch_To90(playerPitch - randomOffset);
                            WrapperUtils.setPacketXRot(movePacket, newPitch);
                            debugMessage("DuplicateRotPlace: Modified pitch from " + playerPitch + " to " + newPitch + " (pitchDiff: " + pitchDiff + ")");
                        }
                    }
                }
            } else if (packet instanceof ServerboundUseItemOnPacket) {
                if (rotated) {
                    lastPlacedDeltaYaw = deltaYaw;
                    lastPlacedDeltaPitch = deltaPitch;
                    rotated = false;
                    debugMessage("DuplicateRotPlace: Recorded place deltaYaw: " + lastPlacedDeltaYaw + ", deltaPitch: " + lastPlacedDeltaPitch);
                }
            }
        }

        if (aca.getValue() && (aimStep.getValue() || perfectRot.getValue())) {
            if (packet instanceof ServerboundMovePlayerPacket movePacket) {
                float currentYaw = WrapperUtils.getPacketYRot(movePacket);
                float currentPitch = WrapperUtils.getPacketXRot(movePacket);

                boolean modified = false;

                if (aimStep.getValue() && shouldModifyRotation(currentYaw, currentPitch)) {
                    float[] modifiedRotation = getModifiedRotation(currentYaw, currentPitch);
                    currentYaw = modifiedRotation[0];
                    currentPitch = modifiedRotation[1];
                    modified = true;
                    debugMessage("AimStep: Modified rotation");
                }

                if (perfectRot.getValue()) {
                    float[] antiPerfectRotation = getAntiPerfectRotation(currentYaw, currentPitch);
                    if (antiPerfectRotation[0] != currentYaw || antiPerfectRotation[1] != currentPitch) {
                        currentYaw = antiPerfectRotation[0];
                        currentPitch = antiPerfectRotation[1];
                        modified = true;
                        debugMessage("PerfectRotation: Modified rotation");
                    }
                }

                if (modified) {
                    WrapperUtils.setPacketYRot(movePacket, currentYaw);
                    WrapperUtils.setPacketXRot(movePacket, MathUtils.clampPitch_To90(currentPitch));
                }

                lastYaw = WrapperUtils.getPacketYRot(movePacket);
                lastPitch = WrapperUtils.getPacketXRot(movePacket);
            }
        }
    }

    private boolean isPlayerMoving() {
        return mc.player != null && mc.player.getDeltaMovement().lengthSqr() > 0.001;
    }

    private boolean shouldModifyRotation(float currentYaw, float currentPitch) {
        if (lastYaw == 0 && lastPitch == 0) return false;

        double yawDelta = Math.abs(MathUtils.wrapAngle_To180(currentYaw - lastYaw));
        double pitchDelta = Math.abs(currentPitch - lastPitch);

        boolean isStepYaw = yawDelta < 0.00001 && pitchDelta > 1.0;
        boolean isStepPitch = pitchDelta < 0.00001 && yawDelta > 1.0;

        return isStepYaw || isStepPitch;
    }

    private float[] getModifiedRotation(float yaw, float pitch) {
        double yawDelta = Math.abs(MathUtils.wrapAngle_To180(yaw - lastYaw));
        double pitchDelta = Math.abs(pitch - lastPitch);

        float newYaw = yaw;
        float newPitch = pitch;

        if (yawDelta < 0.00001 && pitchDelta > 1.0) {
            newYaw = lastYaw + (float)(random.nextGaussian() * 0.001);
        }

        if (pitchDelta < 0.00001 && yawDelta > 1.0) {
            newPitch = lastPitch + (float)(random.nextGaussian() * 0.001);
        }

        return new float[]{newYaw, newPitch};
    }

    private float[] getAntiPerfectRotation(float yaw, float pitch) {
        if (lastYaw == 0 && lastPitch == 0) return new float[]{yaw, pitch};

        double yawDelta = Math.abs(MathUtils.wrapAngle_To180(yaw - lastYaw));
        double pitchDelta = Math.abs(pitch - lastPitch);

        float newYaw = yaw;
        float newPitch = pitch;

        if (!isNoRotation(yawDelta) && isPerfectPattern(yawDelta)) {
            double jitter = (random.nextGaussian() * 0.005);
            newYaw = yaw + (float)jitter;
        }

        if (!isNoRotation(pitchDelta) && isPerfectPattern(pitchDelta)) {
            double jitter = (random.nextGaussian() * 0.005);
            newPitch = pitch + (float)jitter;
        }

        return new float[]{newYaw, newPitch};
    }

    private boolean isNoRotation(double rotation) {
        return Math.abs(rotation) <= EPSILON || isIntegerMultiple(360, rotation);
    }

    private boolean isPerfectPattern(double rotation) {
        if (Double.isInfinite(rotation) || Double.isNaN(rotation)) return false;

        for (double pattern : PERFECT_PATTERNS) {
            if (isIntegerMultiple(pattern, rotation)) {
                return true;
            }
        }
        return false;
    }

    private boolean isIntegerMultiple(double reference, double value) {
        if (reference == 0) return Math.abs(value) <= EPSILON;
        double multiple = value / reference;
        return Math.abs(multiple - Math.round(multiple)) <= EPSILON;
    }

    private void generateIntermediateSlots(int from, int to) {
        int diff = Math.abs(from - to);

        if (diff > 1 && !isSpecialJump(from, to)) {
            int direction = from > to ? -1 : 1;

            for (int middle = from + direction; middle != to; middle += direction) {
                if (middle >= 0 && middle <= 8) {
                    PacketUtils.sendPacketNoEvent(new ServerboundSetCarriedItemPacket(middle));
                    debugMessage("Sent intermediate slot: " + middle);
                }
            }
        }
    }

    private boolean isSpecialJump(int from, int to) {
        return (from == 0 && to == 8) || (from == 8 && to == 0);
    }

    public void reset() {
        lastSlot = -1;
        lastInventoryOpenTime = 0;
        inventoryJustOpened = false;
        storedClosePacket = null;
        delayTime = 0;
        lastYaw = 0;
        lastPitch = 0;
        lastSentYaw = 0;
        lastSentPitch = 0;
        playerYaw = 0;
        playerPitch = 0;
        deltaYaw = 0;
        deltaPitch = 0;
        lastPlacedDeltaYaw = 0;
        lastPlacedDeltaPitch = 0;
        rotated = false;
    }

    public void debugMessage(String message) {
        if (debug.getValue()) {
            mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("[Disabler] -> " + message));
        }
    }
}

