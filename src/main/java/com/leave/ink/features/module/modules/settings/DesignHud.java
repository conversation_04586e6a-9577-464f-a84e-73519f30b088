package com.leave.ink.features.module.modules.settings;

import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "DesignHud", language = Language.English),
        @Text(label = "编辑HUD", language = Language.Chinese)
}, category = Category.Settings)
public class DesignHud extends Module {
    @Override
    protected void onEnable() {
        setEnable(false);
        HudManager.displayGui();

    }
}
