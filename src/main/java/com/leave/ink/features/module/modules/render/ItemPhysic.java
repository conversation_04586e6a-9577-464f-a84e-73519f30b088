package com.leave.ink.features.module.modules.render;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "ItemPhysic", language = Language.English),
        @Text(label = "物理掉落", language = Language.Chinese)
}, category = Category.Render)
public class ItemPhysic extends Module {
    @Override
    public String getSpaceName() {
        return "Item Physic";
    }
}
