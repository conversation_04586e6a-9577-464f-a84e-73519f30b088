package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.manager.BlinkManager;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.features.module.modules.render.esp.ESPUtils;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.wrapper.WrapperUtils;
import net.minecraft.client.player.RemotePlayer;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.ease.EaseBackIn;
import com.mojang.blaze3d.vertex.PoseStack;

import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.entity.TransientEntitySectionManager;

import java.awt.Color;
import java.util.Arrays;

@ModuleInfo(name = {
    @Text(label = "Blink", language = Language.English),
    @Text(label = "瞬移", language = Language.Chinese)
}, category = Category.Movement)
public class Blink extends Module {

    @SettingInfo(name = {
        @Text(label = "Pulse Tick", language = Language.English),
        @Text(label = "脉冲间隔", language = Language.Chinese)
    })
    private final NumberSetting pulseTick = new NumberSetting(20.0, 5.0, 60.0, "#");

    @SettingInfo(name = {
        @Text(label = "Auto Disable", language = Language.English),
        @Text(label = "自动禁用", language = Language.Chinese)
    })
    private final BooleanSetting autoDisable = new BooleanSetting(true);

    @SettingInfo(name = {
        @Text(label = "Max Ticks", language = Language.English),
        @Text(label = "最大刻数", language = Language.Chinese)
    })
    private final NumberSetting maxTicks = new NumberSetting(40.0, 1.0, 100.0, "#");

    @SettingInfo(name = {
        @Text(label = "Slow Release", language = Language.English),
        @Text(label = "慢速释放", language = Language.Chinese)
    })
    private final BooleanSetting slowRelease = new BooleanSetting(false);

    @SettingInfo(name = {
        @Text(label = "Release Ticks", language = Language.English),
        @Text(label = "释放刻数", language = Language.Chinese)
    })
    private final NumberSetting releaseTicks = new NumberSetting(5.0, 1.0, 20.0, "#");

    @SettingInfo(name = {
        @Text(label = "Render Ghost", language = Language.English),
        @Text(label = "显示真实位置", language = Language.Chinese)
    })
    private final BooleanSetting renderRealPosition = new BooleanSetting(true);

    @SettingInfo(name = {
        @Text(label = "Fill Mode", language = Language.English),
        @Text(label = "填充模式", language = Language.Chinese)
    })
    private final BooleanSetting fillMode = new BooleanSetting(true);

    @SettingInfo(name = {
        @Text(label = "Mode", language = Language.English),
        @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting modeSetting = new ModeSetting("Vanilla", Arrays.asList("Vanilla", "Pulse", "Heypixel"),
    new SettingAttribute<>(pulseTick,"Pulse")
    );

    private final Animation animation = new EaseBackIn(500, 1.0, 1.8f);
    private int tickSinceRelease = 1;
    private RemotePlayer fakePlayer;
    private String tag = "";
    
    public Blink() {
        registerSetting(
            modeSetting, autoDisable, maxTicks, slowRelease, releaseTicks, renderRealPosition, fillMode );
    }

    @Override
    public void onEnable() {
        if (Utils.isNull()) return;

        fakePlayer = new RemotePlayer(mc.level, mc.player.getGameProfile());
        fakePlayer.yHeadRot = mc.player.yHeadRot;
        fakePlayer.copyPosition(mc.player);
        fakePlayer.setUUID(mc.player.getUUID());
        tickSinceRelease = 1;

        final TransientEntitySectionManager<Entity> entityTransientEntitySectionManager = WrapperUtils.getEntityStorage();
        if (entityTransientEntitySectionManager == null) {
            this.setEnable(false);
            return;
        }
        fakePlayer.setId(mc.player.getId());

        // Start the blink effect
        BlinkManager.startBlink();
    }

    @Override
    public void onDisable() {
        if (Utils.isNull()) return;

        // If slow release is enabled, release packets gradually
        BlinkManager.stopBlink();

        fakePlayer = null;
    }

    @EventTarget
    public void onRender2D(EventRender2D event) {
        if (Utils.isNull()) return;

        int y = mc.getWindow().getGuiScaledHeight() / 2 + 70;
        this.animation.setDirection(Direction.FORWARDS);

        if (BlinkManager.INSTANCE.delayTicks != 0 && !animation.finished(Direction.BACKWARDS)) {
            final float progress = Math.min(BlinkManager.INSTANCE.delayTicks / maxTicks.getValue().floatValue(), 1);
            event.getPoseStack().pushPose();
            
            // 这里展示瞬移中的信息
            RenderUtils.drawRoundedRect(event.getPoseStack(), mc.getWindow().getGuiScaledWidth() / 2 - 50, y, 100, 20, 5, new Color(0, 0, 0, 120));
            //FontRenderers.misans18.drawString(event.getPoseStack(), "瞬移中... " + BlinkUtils.INSTANCE.delayTicks + "刻", mc.getWindow().getGuiScaledWidth() / 2 - 40, y + 6, -1);
            RenderUtils.drawRoundedRect(event.getPoseStack(), mc.getWindow().getGuiScaledWidth() / 2 - 50, y + 19, progress * 100, 2, 1, new Color(AuraSyncHud.getColor(0, 0)));
            
            event.getPoseStack().popPose();
        }
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (Utils.isNull() || !renderRealPosition.getValue()) return;

        // 在原始位置渲染幽灵实体
        PoseStack poseStack = event.getPoseStack();

        int primaryColor = AuraSyncHud.getColor(0, 0);

        ESPUtils.render3DEntityBoundingBox(
            poseStack,
            fakePlayer,
            primaryColor,
            true,
            fillMode.getValue(),
            true,
            0.3f
        );
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (Utils.isNull()) return;

        // Auto disable when reaching max ticks
        if (autoDisable.getValue() && BlinkManager.INSTANCE.delayTicks >= maxTicks.getValue().intValue()) {
            setEnable(false);
            return;
        }

        // Pulse mode logic
        switch (modeSetting.getValue()) {
            case "Pulse": {
                int pulseTicks = pulseTick.getValue().intValue();

                if (BlinkManager.INSTANCE.delayTicks >= pulseTicks) {
                    // Release packets
                    if (slowRelease.getValue()) {
                        int ticks = releaseTicks.getValue().intValue();
                        BlinkManager.INSTANCE.release(ticks);
                        // Update position tracking after release
                        fakePlayer.setPos(BlinkManager.INSTANCE.lastPosition);
                        fakePlayer.setYRot(BlinkManager.INSTANCE.lastRotation.getYaw());
                        fakePlayer.setXRot(BlinkManager.INSTANCE.lastRotation.getPitch());
                        fakePlayer.xo = BlinkManager.INSTANCE.lastPosition.x;
                        fakePlayer.yo = BlinkManager.INSTANCE.lastPosition.y;
                        fakePlayer.zo = BlinkManager.INSTANCE.lastPosition.z;
                    } else {
                        BlinkManager.startBlink();
                    }
                }

                break;
            }

            case "Heypixel": {
                if (BlinkManager.INSTANCE.delayTicks >= tickSinceRelease * 4) {
                    tickSinceRelease = BlinkManager.INSTANCE.delayTicks;
                    BlinkManager.INSTANCE.release(1);
                }

                break;
            }
        }

    }
    
    @Override
    public String getTag() {
        return "Delay " + BlinkManager.INSTANCE.delayTicks + "ticks";
    }
}
