package com.leave.ink.features.module.modules.combat;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "AutoKnockBack", language = Language.English),
        @Text(label = "自动击退", language = Language.Chinese)
}, category = Category.Combat)
public class AutoKnockBack extends Module {
}
