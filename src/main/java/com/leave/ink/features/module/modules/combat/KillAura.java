package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.AntiBot;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.events.EventAttack;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.CaptureMark;
import com.leave.ink.utils.rotation.*;
import com.leave.ink.utils.timer.TimeUtils;
import lombok.Getter;
import lombok.Setter;
import net.minecraft.network.protocol.game.*;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.phys.*;

import java.awt.*;
import java.util.*;
import java.util.List;

@ModuleInfo(name = {
        @Text(label = "KillAura", language = Language.English),
        @Text(label = "杀戮光环", language = Language.Chinese)
}, category = Category.Combat)
public class KillAura extends Module {
    @SettingInfo(name = {
            @Text(label = "SwitchDelay", language = Language.English),
            @Text(label = "切换延迟", language = Language.Chinese)
    })
    private final NumberSetting switchDelay = new NumberSetting(100.0, 0.0, 1000.0, "#");
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    public final ModeSetting mode = new ModeSetting("Switch", Arrays.asList("Single", "Switch"));
    @SettingInfo(name = {
            @Text(label = "Priority", language = Language.English),
            @Text(label = "优先级", language = Language.Chinese)
    })
    private final ModeSetting priorityValue = new ModeSetting(
            "Distance", Arrays.asList(
            "Health",
            "Distance",
            "LivingTime",
            "Fov",
            "Armor",
            "HurtResistance",
            "HurtTime",
            "RegenAmplifier"
    )
    );
    @SettingInfo(name = {
            @Text(label = "MaxCPS", language = Language.English),
            @Text(label = "最大点击", language = Language.Chinese)
    })
    private final NumberSetting maxCps = new NumberSetting(10.0, 1.0, 20.0, "#");
    @SettingInfo(name = {
            @Text(label = "MinCPS", language = Language.English),
            @Text(label = "最小点击", language = Language.Chinese)
    })
    private final NumberSetting minCps = new NumberSetting(7.0, 1.0, 20.0, "#");
    @SettingInfo(name = {
            @Text(label = "HurtTime", language = Language.English),
            @Text(label = "伤害时间", language = Language.Chinese)
    })
    private final NumberSetting hurtTimeValue = new NumberSetting(10.0, 0.0, 10.0, "#");
    @SettingInfo(name = {
            @Text(label = "PredictReach", language = Language.English),
            @Text(label = "预判距离", language = Language.Chinese)
    })
    private final NumberSetting blockReach = new NumberSetting(3.7, 1.0, 8.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "Reach", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    public final NumberSetting rangeValue = new NumberSetting(3.7, 1.0, 8.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "ThroughWallsRange", language = Language.English),
            @Text(label = "穿墙距离", language = Language.Chinese)
    })
    private final NumberSetting throughWallsRangeValue = new NumberSetting(1.0, 0.0, 8.0, "#.0");
    @SettingInfo(name = {
            @Text(label = "Swing", language = Language.English),
            @Text(label = "挥手", language = Language.Chinese)
    })
    private final BooleanSetting swingValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "KeepSprintTick", language = Language.English),
            @Text(label = "保持疾跑Tick", language = Language.Chinese)
    })
    public final NumberSetting keepSprintTick = new NumberSetting(1.0, 0.0, 10.0, "#");

    @SettingInfo(name = {
            @Text(label = "KeepSprintMode", language = Language.English),
            @Text(label = "保持疾跑模式", language = Language.Chinese)
    })
    public final ModeSetting keepSprintMode = new ModeSetting("None", Arrays.asList("None", "Full", "Simulation"),
            new SettingAttribute<>(keepSprintTick, "Simulation")
    );
    @SettingInfo(name = {
            @Text(label = "AutoBlock", language = Language.English),
            @Text(label = "自动防砍", language = Language.Chinese)
    })
    public final ModeSetting autoBlockValue = new ModeSetting("Off", Arrays.asList("Off", "Options", "Fake"));
    @SettingInfo(name = {
            @Text(label = "RayCast", language = Language.English),
            @Text(label = "光线对准", language = Language.Chinese)
    })
    private final BooleanSetting rayCastValue = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Fov", language = Language.English),
            @Text(label = "视角", language = Language.Chinese)
    })
    private final NumberSetting fovValue = new NumberSetting(180.0, 1.0, 180.0, "#");
    @SettingInfo(name = {
            @Text(label = "SilentRotation", language = Language.English),
            @Text(label = "静态转头", language = Language.Chinese)
    })
    private final BooleanSetting silentRotationValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "MoveFixMode", language = Language.English),
            @Text(label = "移动修复模式", language = Language.Chinese)
    })
    private final ModeSetting moveFixMode = new ModeSetting("Silent", Arrays.asList("OFF", "Silent", "Strict"));
    @SettingInfo(name = {
            @Text(label = "RotationSpeed", language = Language.English),
            @Text(label = "转头速度", language = Language.Chinese)
    })
    private final NumberSetting rotationSpeedValue = new NumberSetting(10, 0.0, 10.0, "#");
    @SettingInfo(name = {
            @Text(label = "NoInvAttack", language = Language.English),
            @Text(label = "背包界面时禁用", language = Language.Chinese)
    })
    private final BooleanSetting noInventoryAttackValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "CircleColor", language = Language.English),
            @Text(label = "圆圈颜色", language = Language.Chinese)
    })
    private final ColorSetting circleColorValue = new ColorSetting(new Color(255, 255, 255, 255));
    @SettingInfo(name = {
            @Text(label = "ESPMode", language = Language.English),
            @Text(label = "标记模式", language = Language.Chinese)
    })
    public final ModeSetting espValue = new ModeSetting("Jello", Arrays.asList("Texture", "Jello", "Box"));
    @SettingInfo(name = {
            @Text(label = "Circle", language = Language.English),
            @Text(label = "圆圈", language = Language.Chinese)
    })
    public final BooleanSetting circleValue = new BooleanSetting(true, new SettingAttribute<>(circleColorValue, true));

    public KillAura() {
        registerSetting(mode, switchDelay, priorityValue, maxCps, minCps, hurtTimeValue,
                blockReach, rangeValue, throughWallsRangeValue, swingValue, keepSprintMode, autoBlockValue,
                rayCastValue, fovValue, silentRotationValue, moveFixMode, rotationSpeedValue, noInventoryAttackValue, espValue, circleValue);
    }

    public LivingEntity target = null;
    public LivingEntity currentTarget = null;

    @Override
    public String getSpaceName() {
        return "Kill Aura";
    }

    private Module scaffold = null;
    private Module antiStuck = null;
    public boolean blockingStatus = false;
    @Getter
    @Setter
    private Rotation rotation = null;

    @Override
    protected void onDisable() {
        super.onDisable();

        if (blockingStatus) {
            mc.options.keyUse.setDown(false);
            blockingStatus = false;
        }

        target = null;
        rotation = null;
        currentTarget = null;
    }

    private boolean shouldAura() {
        if (scaffold == null)
            scaffold = Main.INSTANCE.moduleManager.getModule("Scaffold");
        if (antiStuck == null)
            antiStuck = Main.INSTANCE.moduleManager.getModule("Stuck");
        if (cancelRun()) return false;
        return !scaffold.isEnable() && !antiStuck.isEnable();
    }

    private boolean shouldAttack() {
        if (scaffold == null)
            scaffold = Main.INSTANCE.moduleManager.getModule("Scaffold");
        assert mc.player != null;
        if (mc.screen != null && noInventoryAttackValue.getValue()) return false;
        if (mc.player.isUsingItem()) return false;
        return !scaffold.isEnable();
    }

    private final TimeUtils switchDelayTimer = new TimeUtils();
    private final TimeUtils timer = new TimeUtils();
    private final List<LivingEntity> targets = new ArrayList<>();

    @EventTarget
    public void onUpdate(EventUpdate event) {
        CaptureMark.tick();

        if (!shouldAura())
            return;

        updateEntities();
        unBlock();

        if (targets.isEmpty())
            return;

        if (mode.is("Switch")) {
            int index = 0;
            if (switchDelayTimer.hasTimeElapsed(switchDelay.getValue().longValue())) {
                targets.removeIf(Objects::isNull);
                if (targets.size() > 1) {
                    index = MathUtils.getRandomNumberUsingNextInt(0, targets.size());
                }
                switchDelayTimer.reset();
            }
            target = targets.get(index);
        } else {
            target = targets.get(0);
        }

        if (target == null)
            return;

        ClipRotation clipRotation = ClipRotation.getClipEntity(target, throughWallsRangeValue.getValue().doubleValue());

        if (clipRotation == null)
            return;

        boolean success = clipRotation.isCanSee();

        if (!success) {
            return;
        }

        rotation = clipRotation.getRotation();

        MovementFix movementFix = moveFixMode.getValue().equals("OFF") ? MovementFix.OFF : moveFixMode.getValue().equals("Silent") ? MovementFix.NORMAL : MovementFix.TRADITIONAL;
        if (silentRotationValue.getValue())
            RotationUtils.setRotation(rotation, rotationSpeedValue.getValue().floatValue(), movementFix);
        else {
            rotation.toPlayer(mc.player);
        }

        runAttack();
        doBlock();
    }

    private void doBlock() {
        if (autoBlockValue.is("None")) return;
        blockingStatus = true;
    }

    private void unBlock() {
        if (autoBlockValue.is("None")) return;
        if (blockingStatus) {
            blockingStatus = false;
        }
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (circleValue.getValue()) {
            RenderUtils.drawCircle(event.getPoseStack(), mc.player, blockReach.getValue().floatValue(), event.getPartialTicks(), circleColorValue.getValue(), 2F);
        }
    }

    private void runAttack() {
        boolean canAttack;

        if (!shouldAttack())
            return;

        if (RotationUtils.getDistanceToEntityBox(target) > rangeValue.getValue().doubleValue()) return;
        if (rayCastValue.getValue()) {
            canAttack = canHit(target, RotationUtils.serverRotation);
        } else {
            canAttack = true;
        }
        if (canAttack) {
            currentTarget = target;
            attackEntity(target);
        }
    }

    private boolean canHit(LivingEntity entity, Rotation rotation) {
        HitResult result = RayCastUtil.rayCast(rotation, rangeValue.getValue().doubleValue());

        if (result instanceof BlockHitResult blockHitResult) {
            if (BlockUtils.isAirBlock(blockHitResult.getBlockPos())) {
                if (throughWallsRangeValue.getValue().doubleValue() == 0.0d)
                    return false;

                return RotationUtils.canWallAttack(entity, throughWallsRangeValue.getValue().floatValue());
            }
        } else return result instanceof EntityHitResult;

        return false;
    }

    @SuppressWarnings("all")
    private void attackEntity(LivingEntity entity) {
        if (mc.options.keyUse.isDown()) {
            return;
        }
        Velocity velocity = ((Velocity) Main.INSTANCE.moduleManager.getModule("Velocity"));
        if (velocity.attacked) return;
        if (timer.hasTimeElapsed((long) (700L / getCps()))) {
            unBlock();
            EventManager.call(new EventAttack(entity));
            if (keepSprintMode.getValue().equals("Simulation") || keepSprintMode.getValue().equals("None"))
                mc.gameMode.attack(mc.player, entity);
            else {
                mc.gameMode.tick();
                mc.getConnection().send(ServerboundInteractPacket.createAttackPacket(entity, mc.player.isShiftKeyDown()));
            }

            if (swingValue.getValue())
                mc.player.swing(InteractionHand.MAIN_HAND);
            else
                mc.getConnection().send(new ServerboundSwingPacket(InteractionHand.MAIN_HAND));
            if (keepSprintMode.getValue().equals("Full")) {
                if (mc.player.fallDistance > 0
                        && !mc.player.onGround()
                        && !mc.player.onClimbable()
                        && !mc.player.isInWater()
                        && !mc.player.hasEffect(MobEffects.BLINDNESS)
                        && mc.player.getVehicle() == null) {
                    mc.player.crit(entity);
                }
                if (EnchantmentHelper.getDamageBonus(mc.player.getMainHandItem(), entity.getMobType()) > 0F)
                    mc.player.magicCrit(entity);
            }
            timer.reset();
        }
    }

    public float getCps() {
        return (float) MathUtils.getRandomNumber(minCps.getValue().intValue(), maxCps.getValue().intValue());
    }

    private boolean isAlive() {
        return !mc.player.isDeadOrDying() && mc.player.isAlive() && mc.player.getHealth() > 0;
    }

    private boolean cancelRun() {
        return mc.player.isSpectator() || !isAlive();
    }

    private void updateEntities() {
        targets.clear();
        target = currentTarget = null;

        var hurtTime = hurtTimeValue.getValue().intValue();
        var fov = fovValue.getValue().floatValue();

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity == mc.player) continue;
            if (entity instanceof LivingEntity livingEntity) {
                if (Main.INSTANCE.friendsManager.isFriend(livingEntity.getName().getString())) continue;

                if (livingEntity.isDeadOrDying() || !livingEntity.isAlive() || livingEntity.getHealth() <= 0) {
                    continue;
                }
                if (!Utils.isValidEntity(livingEntity))
                    continue;

                if (Targets.isTeam(livingEntity))
                    continue;

                if (AntiBot.isBot(livingEntity))
                    continue;

                double distance = RotationUtils.getDistanceToEntityBox(livingEntity);
                double entityFov = RotationUtils.getRotationDifference(livingEntity);

                if (distance <= blockReach.getValue().floatValue() && (fov == 180F || entityFov <= fov) && livingEntity.hurtTime <= hurtTime) {
                    targets.add(livingEntity);
                }
            }
        }

        switch (priorityValue.getValue().toLowerCase()) {
            case "distance" -> targets.sort(Comparator.comparingDouble(mc.player::distanceTo));
            case "health" -> targets.sort(Comparator.comparingDouble(e -> e.getHealth() + e.getAbsorptionAmount()));
            case "fov" -> targets.sort(Comparator.comparingDouble(RotationUtils::getRotationDifference));
            case "livingtime" -> targets.sort((e1, e2) -> Integer.compare(e2.tickCount, e1.tickCount));
            case "armor" -> targets.sort(Comparator.comparingInt(LivingEntity::getArmorValue));
            case "hurttime" -> targets.sort(Comparator.comparingInt(e -> e.hurtTime));
            case "hurtresistance" -> targets.sort(Comparator.comparingInt(e -> e.invulnerableTime));
            case "regenamplifier" ->
                    targets.sort(Comparator.comparingInt(e -> e.hasEffect(MobEffects.REGENERATION) ? e.getEffect(MobEffects.REGENERATION).getAmplifier() : -1));
        }
    }

    @Override
    public String getTag() {
        return mode.getValue();
    }
}