package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventMotion;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "Sprint", language = Language.English),
        @Text(label = "自动疾跑", language = Language.Chinese)
}, category = Category.Movement)
public class Sprint extends Module {


    @Override
    protected void onDisable() {
        if (mc.player != null && mc.level != null) {
            mc.options.keySprint.setDown(false);
        }
    }
    private KillAura aura = null;
    @EventTarget
    public void onMotion(EventMotion event) {
//        if (event.getEventType() == EventType.PRE) {
//            if (aura == null)
//                aura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
//            if (aura.isEnable() && (aura.target != null || aura.currentTarget != null) && !aura.keepSprintMode.getValue().equals("Simulation")) {
//                mc.options.keySprint.setDown(aura.target.hurtTime >= aura.keepSprintTick.getMax() - aura.keepSprintTick.getValue().intValue());
//                return;
//            }
//
//            mc.options.keySprint.setDown(true);
//        }
    }
}
