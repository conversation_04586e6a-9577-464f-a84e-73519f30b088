package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventScroll;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;

@ModuleInfo(name = {
        @Text(label = "AutoTool", language = Language.English),
        @Text(label = "自动工具", language = Language.Chinese)
}, category = Category.World)
public class AutoTool extends Module {
    public boolean clicking = false;
    private int current;
    public int fakeItemSlot = -1;

    @Override
    protected void onEnable() {
        if (Utils.isNull())
            return;

        fakeItemSlot = mc.player.getInventory().selected;
    }

    public ItemStack getFakeCurrentItem() {
        return fakeItemSlot >= 0 && fakeItemSlot <= 8 ? mc.player.getInventory().getItem(fakeItemSlot) : ItemStack.EMPTY;
    }

    @EventTarget
    public void onMouseWheel(EventScroll event) {
        if (Utils.isNull())
            return;

        if (event.getGetScrollDelta() > 0) {
            fakeItemSlot--;
        } else if (event.getGetScrollDelta() < 0) {
            fakeItemSlot++;
        }
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (fakeItemSlot > 8) fakeItemSlot = 0;
        if (fakeItemSlot < 0) fakeItemSlot = 8;

        var fucker = (Fucker) Main.INSTANCE.moduleManager.getModule("BlockBreaker");

        if (!clicking) {
            current = mc.player.getInventory().selected;
        }

        for (int i = 0; i < 9; ++i) {
            if (mc.options.keyHotbarSlots[i].isDown()) {
                fakeItemSlot = i;
            }
        }

        if (mc.options.keyAttack.isDown()) {
            if (mc.hitResult != null && mc.hitResult.getType() == HitResult.Type.BLOCK) {
                clicking = true;
                equipBestTool(mc.level.getBlockState(((BlockHitResult) mc.hitResult).getBlockPos()));
            }
        } else if (fucker.isEnable() && fucker.pos != null) {
            clicking = true;
            equipBestTool(mc.level.getBlockState(fucker.pos));
        } else {
            if (clicking) {
                mc.player.getInventory().selected = current;
                clicking = false;
            }
        }
    }

    private void equipBestTool(BlockState blockState) {
        int bestSlot = -1;
        double max = 0.0;
        for (int i = 0; i < 9; ++i) {
            ItemStack stack = mc.player.getInventory().getItem(i);
            if (!stack.isEmpty()) {
                float speed = stack.getDestroySpeed(blockState);
                if (speed > 1.0f) {
                    int eff = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.BLOCK_EFFICIENCY, stack);
                    speed += (eff > 0) ? (Math.pow(eff, 2.0) + 1.0) : 0.0;
                    if (speed > max) {
                        max = speed;
                        bestSlot = i;
                    }
                }
            }
        }

        if (bestSlot != -1) {
            equip(bestSlot);
        }
    }

    private void equip(int slot) {
        mc.player.getInventory().selected = slot;
    }
}
