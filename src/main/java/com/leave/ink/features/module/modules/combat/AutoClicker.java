package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventTick;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.KeyMapping;
import net.minecraft.client.Minecraft;
import net.minecraft.world.phys.HitResult;

@ModuleInfo(name = {
        @Text(label = "AutoClicker", language = Language.English),
        @Text(label = "连点", language = Language.Chinese)
}, category = Category.Combat)
public class AutoClicker extends Module {
    @SettingInfo(name = {
            @Text(label = "MaxCPS", language = Language.English),
            @Text(label = "最大连点", language = Language.Chinese)
    })
    private final NumberSetting maxCPSValue = new NumberSetting(12, 1.0, 20.0, "#");
    @SettingInfo(name = {
            @Text(label = "MinCPS", language = Language.English),
            @Text(label = "最小连点", language = Language.Chinese)
    })
    private final NumberSetting minCPSValue = new NumberSetting(11, 1.0, 20.0, "#");
    @SettingInfo(name = {
            @Text(label = "Left", language = Language.English),
            @Text(label = "左键", language = Language.Chinese)
    })
    private final BooleanSetting leftValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Right", language = Language.English),
            @Text(label = "右键", language = Language.Chinese)
    })
    private final BooleanSetting rightValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "NoBreakBlock", language = Language.English),
            @Text(label = "破坏方块时关闭", language = Language.Chinese)
    })
    private final BooleanSetting noBreakBlockValue = new BooleanSetting(true);

    private long leftDelay = TimeUtils.randomClickDelay(minCPSValue.getValue().intValue(), maxCPSValue.getValue().intValue());
    private long leftLastSwing = 0L;
    private long rightDelay = TimeUtils.randomClickDelay(minCPSValue.getValue().intValue(), maxCPSValue.getValue().intValue());
    private long rightLastSwing = 0L;
    private final TimeUtils breakBlocksTimer = new TimeUtils();

    @Override
    public void onEnable() {
        breakBlocksTimer.reset();
    }

    public AutoClicker() {
        registerSetting(maxCPSValue, minCPSValue, leftValue, rightValue, noBreakBlockValue);
    }

    @EventTarget
    public void onUpdate(EventTick event) {
        if (mc.player == null || mc.level == null)
            return;


        // Left click
        if (mc.options.keyAttack.isDown() && leftValue.getValue() && System.currentTimeMillis() - leftLastSwing >= leftDelay) {
            ObfuscationReflectionHelper.setPrivateValue(Minecraft.class, mc, 0, "missTime");
            HitResult hitResult = mc.hitResult;
            //m_91530_
//            Method method = ObfuscationReflectionHelper.findMethod(MouseHandler.class, "m_91530_", long.class, int.class, int.class, int.class);
            if (noBreakBlockValue.getValue() && hitResult != null && hitResult.getType() == HitResult.Type.BLOCK) {

            } else {
                KeyMapping.click(mc.options.keyAttack.getDefaultKey());
            }
//            try {
//                method.invoke(mc.mouseHandler, 2542151189120L, 0, 1, 0);
//            } catch (IllegalAccessException | InvocationTargetException e) {
//                e.printStackTrace();
//            }

            leftLastSwing = System.currentTimeMillis();
            leftDelay = TimeUtils.randomClickDelay(minCPSValue.getValue().intValue(), maxCPSValue.getValue().intValue());
        }

        // Right click
        if (mc.options.keyUse.isDown() && !mc.player.isUsingItem() && rightValue.getValue() && System.currentTimeMillis() - rightLastSwing >= rightDelay) {
            KeyMapping.click(mc.options.keyUse.getDefaultKey());
            rightLastSwing = System.currentTimeMillis();
            rightDelay = TimeUtils.randomClickDelay(minCPSValue.getValue().intValue(), maxCPSValue.getValue().intValue());
        }
    }
}