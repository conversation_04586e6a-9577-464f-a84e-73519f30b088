package com.leave.ink.features.module;

import com.darkmagician6.eventapi.EventManager;
import com.external.ui.ExternalUI;
import com.leave.ink.Main;
import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.hud.dynamicIsland.impl.ModuleDynamic;
import com.leave.ink.features.hud.main.NotificationHud;
import com.leave.ink.features.module.annotation.CppInfo;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.notification.impl.ModuleNotification;
import com.leave.ink.ui.notification.impl.ModuleNotification2;
import com.leave.ink.ui.notification.impl.NVIDIANotification;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;
import lombok.Getter;
import net.minecraft.ChatFormatting;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.sounds.SoundEvents;

public abstract class Module extends SettingManager implements IMinecraft {
    private final Text[] texts;
    private final Category category;
    private int key;
    @Getter
    private boolean enable;
    public Animation animation = new DecelerateAnimation(250, 1);
    private ModuleDynamic moduleDynamic;

    public Module() {
        ModuleInfo moduleInfo = this.getClass().getAnnotation(ModuleInfo.class);

        if (moduleInfo == null)
            throw new RuntimeException(String.format("未检测到模块信息 %s", getClass().getName()));

        this.texts = moduleInfo.name();
        this.category = moduleInfo.category();
        this.key = moduleInfo.key();
        this.enable = moduleInfo.enable();
    }

    @CppInfo
    public String getDescription() {
        return "None";
    }

    @CppInfo
    protected boolean showDynamic() {
        return true;
    }

    public void registerSetting(Setting<?>... settings) {
        try {
            registerSetting(this, settings);
        } catch (Exception e) {
            throw new RuntimeException(String.format("注冊失敗 %s", getClass().getName()));
        }
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
        mc.getSoundManager().play(SimpleSoundInstance.forUI(SoundEvents.UI_BUTTON_CLICK, 1.0F));

        if (enable) {
            EventManager.register(this);
            onEnable();

            switch (NotificationHud.mode.getValue()) {
                case "Simple" ->
                        Main.INSTANCE.notificationManager.add(new ModuleNotification2(true, getNameKey() + ChatFormatting.GREEN + " Enabled"));
                case "Simple2" ->
                        Main.INSTANCE.notificationManager.add(new ModuleNotification(true, getNameKey() + ChatFormatting.GREEN + " Enabled"));
                case "NVIDIA" ->
                        Main.INSTANCE.notificationManager.add(new NVIDIANotification(getNameKey()  + ChatFormatting.GREEN + " Enabled"));
            }

            if(HudManager.dynamicIsland != null && showDynamic()) {
                moduleDynamic = new ModuleDynamic(getName(), true);
                HudManager.dynamicIsland.addTask(moduleDynamic);
            }
        } else {
            EventManager.unregister(this);
            onDisable();

            switch (NotificationHud.mode.getValue()) {
                case "Simple" ->
                        Main.INSTANCE.notificationManager.add(new ModuleNotification2(false, getNameKey() + ChatFormatting.RED + " Disabled"));
                case "Simple2" ->
                        Main.INSTANCE.notificationManager.add(new ModuleNotification(false, getNameKey() + ChatFormatting.RED + " Disabled"));
                case "NVIDIA" ->
                        Main.INSTANCE.notificationManager.add(new NVIDIANotification(getNameKey() + ChatFormatting.RED + " Disabled"));
            }

            if(HudManager.dynamicIsland != null && showDynamic()) {
                moduleDynamic = new ModuleDynamic(getName(), false);
                HudManager.dynamicIsland.addTask(moduleDynamic);
            }
        }
        ExternalUI.UI_UpdateModulesState(getNameKey(), enable);
    }


    public void toggle() {
        setEnable(!isEnable());
    }

    protected void onEnable() {}

    protected void onDisable() {}

    @CppInfo
    public String getSpaceName() {
        return getNameKey();
    }

    @CppInfo
    public String getName(Language language) {
        return Language.getLabel(getTexts(), language);
    }

    @CppInfo
    public String getCNName() {
        return Language.getLabel(getTexts(), Language.Chinese);
    }

    @CppInfo
    public String getCategoryName() {
        return category.getNameKey();
    }

    @CppInfo
    public String getName() {
        return Language.getLabel(getTexts(), Language.getLanguage());
    }

    @CppInfo
    public String getNameKey() {
        return Language.getLabel(getTexts(), Language.getDefaultLanguage());
    }

    @CppInfo
    public Text[] getTexts() {
        return texts;
    }

    @CppInfo
    public String getTag() {
        return null;
    }

    @CppInfo
    public Category getCategory() {
        return category;
    }

    @CppInfo
    public int getKey() {
        return key;
    }

    @CppInfo
    public void setKey(int key) {
        this.key = key;
    }
}
