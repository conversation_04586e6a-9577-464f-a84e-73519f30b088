package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.rotation.MovementFix;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.gui.screens.ChatScreen;
import net.minecraft.core.BlockPos;
import net.minecraft.network.protocol.game.ServerboundSwingPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@ModuleInfo(name = {
        @Text(label = "ChestAura", language = Language.English),
        @Text(label = "自动开箱子", language = Language.Chinese)
}, category = Category.World)
public class ChestAura extends Module {
    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(5.0, 1.0, 10.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delay = new NumberSetting(200.0, 50.0, 500.0, "#.00");
    @SettingInfo(name = {
            @Text(label = "Swing", language = Language.English),
            @Text(label = "挥手", language = Language.Chinese)
    })
    private final BooleanSetting swing = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "RayCast", language = Language.English),
            @Text(label = "光线对准", language = Language.Chinese)
    })
    private final BooleanSetting rayCast = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "SilentRotation", language = Language.English),
            @Text(label = "静态转头", language = Language.Chinese)
    })
    private final BooleanSetting silentRotation = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Furnace", language = Language.English),
            @Text(label = "熔炉", language = Language.Chinese)
    })
    private final BooleanSetting furnace = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "BlastFurnace", language = Language.English),
            @Text(label = "高炉", language = Language.Chinese)
    })
    private final BooleanSetting blastFurnace = new BooleanSetting(true);

    private BlockPos currentTarget;
    private final TimeUtils timer = new TimeUtils();
    private Rotation rotation;
    private final List<BlockPos> clickedBlocks = new ArrayList<>();

    public ChestAura() {
        registerSetting(range, delay, swing, rayCast, silentRotation, furnace, blastFurnace);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (!timer.hasTimeElapsed(delay.getValue().longValue()))
            return;

        if (!(mc.screen instanceof ChatScreen) && mc.screen != null) {
            timer.reset();
            rotation = null;
            currentTarget = null;
            return;
        }

        double radius = range.getValue().floatValue() + 1.0;

        List<Map.Entry<BlockPos, Block>> chestsToSearch = RotationUtils.searchBlocks((int) radius)
                .entrySet().stream()
                .filter(entry -> (Block.getId(Blocks.CHEST.defaultBlockState()) == Block.getId(entry.getValue().defaultBlockState())
                        || (furnace.getValue() && Block.getId(Blocks.FURNACE.defaultBlockState()) == Block.getId(entry.getValue().defaultBlockState()))
                        || (blastFurnace.getValue() && Block.getId(Blocks.BLAST_FURNACE.defaultBlockState()) == Block.getId(entry.getValue().defaultBlockState())))
                        && !clickedBlocks.contains(entry.getKey())
                        && BlockUtils.getCenterDistance(entry.getKey()) <= range.getValue().floatValue())
                .sorted(Comparator.comparingDouble(entry -> BlockUtils.getCenterDistance(entry.getKey())))
                .toList();

        for (Map.Entry<BlockPos, Block> entry : chestsToSearch) {
            BlockPos chest = entry.getKey();
            rotation = RotationUtils.getRotationBlock(chest);
            currentTarget = chest;
            break;
        }

        if (currentTarget == null || rotation == null) return;

        if (silentRotation.getValue()) {
            RotationUtils.setRotation(rotation, 10, MovementFix.NORMAL);
        } else {
            rotation.toPlayer(mc.player);
        }

        Vec3 vec3 = new Vec3(currentTarget.getX(), currentTarget.getY(), currentTarget.getZ());
        BlockHitResult rayTraceResult = BlockHitResult.miss(vec3, RotationUtils.getViewDirection(currentTarget), currentTarget);
        if (!rayCast.getValue() || RotationUtils.isLookingAtBlock(RotationUtils.serverRotation, currentTarget, range.getValue().floatValue() + 1, 0.9F)) {
            if (mc.gameMode.useItemOn(mc.player, InteractionHand.MAIN_HAND, rayTraceResult).consumesAction()) {
                if (swing.getValue()) {
                    mc.player.swing(InteractionHand.MAIN_HAND);
                } else {
                    mc.getConnection().send(new ServerboundSwingPacket(InteractionHand.MAIN_HAND));
                }

                clickedBlocks.add(currentTarget);
                currentTarget = null;
                rotation = null;
                timer.reset();
            }
        }
    }

    @Override
    public void onDisable() {
        clickedBlocks.clear();
        rotation = null;
    }
}
