package com.leave.ink.features.module.modules.settings;

import com.darkmagician6.eventapi.EventTarget;
import com.external.ui.ExternalUI;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.Utils;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import org.lwjgl.glfw.GLFW;
import java.awt.*;
import java.util.Arrays;
import java.util.regex.Pattern;

@ModuleInfo(name = {
        @Text(label = "ClientSetting", language = Language.English),
        @Text(label = "客户端设置", language = Language.Chinese)
}, category = Category.Settings)
public class ClientSetting extends Module {
    @SettingInfo(name = {
            @Text(label = "GuiColor", language = Language.English),
            @Text(label = "Gui颜色", language = Language.Chinese)
    })
    public static final ColorSetting color = new ColorSetting(Color.RED);
    @SettingInfo(name = {
            @Text(label = "Language", language = Language.English),
            @Text(label = "语言", language = Language.Chinese)
    })
    public static final ModeSetting language = new ModeSetting("English", Arrays.asList("English", "Chinese"));
    @SettingInfo(name = {
            @Text(label = "GuiStyle", language = Language.English),
            @Text(label = "GuiStyle", language = Language.Chinese)
    })
    public static final ModeSetting guiStyle = new ModeSetting("2", Arrays.asList("1", "2"));
    @SettingInfo(name = {
            @Text(label = "EditHud", language = Language.English),
            @Text(label = "编辑Hud", language = Language.Chinese)
    })
    public static final ButtonSetting editHud = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            HudManager.displayGui();

        }
    };
    @SettingInfo(name = {
            @Text(label = "ReviseScoreboard", language = Language.English),
            @Text(label = "修改计分板休息", language = Language.Chinese)
    })
    public final BooleanSetting reviseScoreboard = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "MidClickAddFriend", language = Language.English),
            @Text(label = "中键加好友", language = Language.Chinese)
    })
    public final BooleanSetting midClickAddFriend = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "FriendMode", language = Language.English),
            @Text(label = "好友模式", language = Language.Chinese)
    })
    public final ModeSetting friendMode = new ModeSetting("White", Arrays.asList("White", "Black"));
    @SettingInfo(name = {
            @Text(label = "Gui", language = Language.English),
            @Text(label = "Gui", language = Language.Chinese)
    })
    public static final ModeSetting gui = new ModeSetting("Window", Arrays.asList("Window", "Dropdown"),
            new SettingAttribute<>(guiStyle, "Window")
    ) {
        @Override
        protected void onValueChanged() {
            ExternalUI.ClearGui();
            super.onValueChanged();
        }
    };

    public ClientSetting() {
        registerSetting(color, language, editHud, reviseScoreboard, midClickAddFriend, friendMode);
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        Main.INSTANCE.friendsManager.removeAll();
    }

    @EventTarget
    public void onRender2D(EventRender2D event) {
        if (Utils.isNull()) return;

        if (!midClickAddFriend.getValue())
            return;

        if (!wasDown && GLFW.glfwGetMouseButton(mc.getWindow().getWindow(), GLFW.GLFW_MOUSE_BUTTON_MIDDLE) == GLFW.GLFW_PRESS) {
            HitResult hitResult = mc.hitResult;

            if (hitResult != null && hitResult.getType() == HitResult.Type.ENTITY) {
                EntityHitResult entityHit = (EntityHitResult) hitResult;
                Entity entity = entityHit.getEntity();

                if (entity instanceof Player) {
                    String playerName = stripColor(entity.getName().getString());

                    if (!Main.INSTANCE.friendsManager.friends.contains(playerName)) {
                        Main.INSTANCE.friendsManager.add(playerName);
                        ChatUtils.displayAlert("§a§l" + playerName + "§c 已添加.");
                    } else {
                        Main.INSTANCE.friendsManager.remove(playerName);
                        ChatUtils.displayAlert("§a§l" + playerName + "§c 已删除.");
                    }
                }
            } else {
                ChatUtils.displayAlert("§c§l你需要选择一名玩家.");
            }
        }

        wasDown = GLFW.glfwGetMouseButton(mc.getWindow().getWindow(), GLFW.GLFW_MOUSE_BUTTON_MIDDLE) == GLFW.GLFW_PRESS;
    }

    boolean wasDown = false;

    private final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");
    private String stripColor(String input) {
        return input != null ? COLOR_PATTERN.matcher(input).replaceAll("") : null;
    }
}
