package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.Render3DEngine;
import com.leave.ink.utils.timer.TimeUtils;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.protocol.game.ClientboundSystemChatPacket;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.entity.*;
import net.minecraft.world.phys.AABB;
import org.lwjgl.opengl.GL11;
import java.awt.*;
import java.util.*;

@ModuleInfo(name = {
        @Text(label = "BoxESP", language = Language.English),
        @Text(label = "箱子边框", language = Language.Chinese)
}, category = Category.Render)
public class BoxESP extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Line", Arrays.asList("Line", "Fill"));
    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    private final ColorSetting colorValue = new ColorSetting(new Color(255, 255, 255, 120));
    @SettingInfo(name = {
            @Text(label = "OpenedColor", language = Language.English),
            @Text(label = "打开箱子颜色", language = Language.Chinese)
    })
    private final ColorSetting openedColorValue = new ColorSetting(new Color(0, 255, 0, 120));
    @SettingInfo(name = {
            @Text(label = "Furnace", language = Language.English),
            @Text(label = "熔炉", language = Language.Chinese)
    })
    private final BooleanSetting furnace = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "BlastFurnace", language = Language.English),
            @Text(label = "高炉", language = Language.Chinese)
    })
    private final BooleanSetting blastFurnace = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "NetherQuartz", language = Language.English),
            @Text(label = "下界石英", language = Language.Chinese)
    })
    private final BooleanSetting netherQuartz = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Tag", language = Language.English),
            @Text(label = "标签", language = Language.Chinese)
    })
    private final BooleanSetting tag = new BooleanSetting(false);

    private final Set<BlockEntity> openedChests = new HashSet<>();
    private final TimeUtils searchTimer = new TimeUtils();
    private final Map<BlockPos, Block> posList = new HashMap<>();
    private Thread thread;
    private boolean isAnni;

    public BoxESP() {
        registerSetting(mode, colorValue, openedColorValue, furnace, blastFurnace, netherQuartz, tag);
    }

    @Override
    protected void onDisable() {
        openedChests.clear();
        isAnni = false;
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        openedChests.clear();
        isAnni = false;
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        for (BlockEntity blockEntity : BlockUtils.getBlockEntities()) {
            onBox(event, blockEntity);

            if (blockEntity instanceof EnderChestBlockEntity enderChest) {
                AABB enderChestBox = new AABB(
                        enderChest.getBlockPos().getX() + 0.06,
                        enderChest.getBlockPos().getY(),
                        enderChest.getBlockPos().getZ() + 0.06,
                        (enderChest.getBlockPos().getX() + 0.94),
                        (enderChest.getBlockPos().getY() - 0.125 + 1),
                        (enderChest.getBlockPos().getZ() + 0.94)
                );

                boolean isOpened = enderChest.getOpenNess(event.getPartialTicks()) > 0.0f;
                if (isOpened) {
                    if (mode.getValue().equals("Fill"))
                        Render3DEngine.drawFilledBox(event.getPoseStack(), enderChestBox, openedColorValue.getValue());
                    else
                        RenderUtils.drawLineBox(event.getPoseStack(), enderChestBox, openedColorValue.getValue());
                }

                if (tag.getValue())
                    renderNames(event, blockEntity.getBlockState().getBlock().getName().getString(), blockEntity.getBlockPos());
            }
        }

        if (netherQuartz.getValue()) {
            synchronized (posList) {
                posList.forEach((key, value) -> {
                    if (value == Blocks.NETHER_QUARTZ_ORE) {
                        Color renderColor = isAnni ? Color.red : colorValue.getValue();

                        if (mode.getValue().equals("Fill"))
                            Render3DEngine.drawFilledBox(event.getPoseStack(), new AABB(key), renderColor);
                        else
                            RenderUtils.drawLineBox(event.getPoseStack(), new AABB(key), renderColor);

                        if (tag.getValue())
                            renderNames(event, isAnni ? ChatFormatting.RED + "[危险]传送石" : value.getName().getString(), key);
                    }
                });
            }
        }
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (netherQuartz.getValue()) {
            if (searchTimer.hasTimeElapsed(1000L) && (thread == null || !thread.isAlive())) {
                thread = new Thread(() -> {
                    Map<BlockPos, Block> blocks = new HashMap<>(BlockUtils.searchBlocks2());
                    searchTimer.reset();
                    synchronized (posList) {
                        posList.clear();
                        posList.putAll(blocks);
                    }
                }, "BoxESP-BlockFinder");
                thread.setDaemon(true);
                thread.setPriority(Thread.MIN_PRIORITY);
                thread.start();
            }
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacket() instanceof ClientboundSystemChatPacket chatPacket) {
            if (chatPacket.content().getString().contains("被击败因为"))
                isAnni = true;
        }
    }

    public void onBox(EventRender3D event, BlockEntity blockEntity) {
        if (blockEntity instanceof ChestBlockEntity chestBlock) {
            AABB chestBox = new AABB(
                    chestBlock.getBlockPos().getX() + 0.06,
                    chestBlock.getBlockPos().getY(),
                    chestBlock.getBlockPos().getZ() + 0.06,
                    (chestBlock.getBlockPos().getX() + 0.94),
                    (chestBlock.getBlockPos().getY() - 0.125 + 1),
                    (chestBlock.getBlockPos().getZ() + 0.94)
            );

            boolean isOpened = chestBlock.getOpenNess(event.getPartialTicks()) > 0.0f;
            if (isOpened) {
                openedChests.add(chestBlock);
            }

            Color renderColor = isOpened || openedChests.contains(chestBlock) ? openedColorValue.getValue() : colorValue.getValue();
            if (mode.getValue().equals("Fill"))
                Render3DEngine.drawFilledBox(event.getPoseStack(), chestBox, renderColor);
            else
                RenderUtils.drawLineBox(event.getPoseStack(), chestBox, renderColor);

            if (tag.getValue())
                renderNames(event, blockEntity.getBlockState().getBlock().getName().getString(), blockEntity.getBlockPos());

        } else if ((furnace.getValue() && blockEntity instanceof FurnaceBlockEntity) || (blastFurnace.getValue() && blockEntity instanceof BlastFurnaceBlockEntity)) {
            Color renderColor = colorValue.getValue();

            if (mode.getValue().equals("Fill"))
                Render3DEngine.drawFilledBox(event.getPoseStack(), blockEntity.getRenderBoundingBox(), renderColor);
            else
                RenderUtils.drawLineBox(event.getPoseStack(), blockEntity.getRenderBoundingBox(), renderColor);

            if (tag.getValue())
                renderNames(event, blockEntity.getBlockState().getBlock().getName().getString(), blockEntity.getBlockPos());
        }
    }

    private void renderNames(EventRender3D event, String name, BlockPos blockPos) {
        boolean depthEnabled = GL11.glIsEnabled(GL11.GL_DEPTH_TEST);
        int depthFunc = GL11.glGetInteger(GL11.GL_DEPTH_FUNC);

        try {
            GL11.glDisable(GL11.GL_DEPTH_TEST);
            RenderSystem.depthMask(false);
            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            renderItemName(event, name, blockPos);
            RenderSystem.disableBlend();
            RenderSystem.depthMask(true);
            if (depthEnabled) {
                GL11.glEnable(GL11.GL_DEPTH_TEST);
                GL11.glDepthFunc(depthFunc);
            }
        } catch (Exception ignored) {
            RenderSystem.disableBlend();
            RenderSystem.depthMask(true);
            if (depthEnabled) {
                GL11.glEnable(GL11.GL_DEPTH_TEST);
                GL11.glDepthFunc(depthFunc);
            }
        }
    }

    private void renderItemName(EventRender3D event, String name, BlockPos blockPos) {
        double x = blockPos.getX() + 0.5;
        double y = blockPos.getY() + 1.25;
        double z = blockPos.getZ() + 0.5;
        float distanceSq = (float) mc.player.distanceToSqr(x, y, z);
        PoseStack matrixStack = event.getPoseStack();
        matrixStack.pushPose();
        matrixStack.translate(x - mc.gameRenderer.getMainCamera().getPosition().x,
                y - mc.gameRenderer.getMainCamera().getPosition().y,
                z - mc.gameRenderer.getMainCamera().getPosition().z);
        matrixStack.mulPose(mc.gameRenderer.getMainCamera().rotation());

        float scale = 0.025F;
        if (distanceSq > 100.0f) {
            scale = 0.025F * (float) (Math.sqrt(distanceSq * 0.01F));
        }
        matrixStack.scale(-scale, -scale, scale);
        float nameX = -FontRenderers.harmony18.getStringWidth(name) / 2.0f;
        float nameY = 0;
        FontRenderers.harmony18.drawString(matrixStack, name, nameX, nameY, colorValue.getValue().getRGB());
        matrixStack.popPose();
    }
}
