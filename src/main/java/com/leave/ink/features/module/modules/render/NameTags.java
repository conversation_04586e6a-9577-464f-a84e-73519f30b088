package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.hud.EventSkia2D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.other.ClayGuns;
import com.leave.ink.features.module.modules.other.KillerCheck;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.AntiBot;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import java.util.Arrays;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.SkiaProcess;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.ui.skija.utils.SkiaCompatLayer;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.manager.HeypixelManager;
import com.leave.ink.utils.player.PlayerUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.Projection;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Camera;
import net.minecraft.util.Mth;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.world.phys.Vec3;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;
import org.lwjgl.opengl.GL20;
import java.awt.*;
import java.text.DecimalFormat;

@ModuleInfo(name = {
        @Text(label = "NameTags", language = Language.English),
        @Text(label = "名字标签", language = Language.Chinese)
}, category = Category.Render)
public class NameTags extends Module {

    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量 ", language = Language.Chinese)
    })
    private final BooleanSetting healthValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Distance", language = Language.English),
            @Text(label = "距离 ", language = Language.Chinese)
    })
    private final BooleanSetting distanceValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Armor", language = Language.English),
            @Text(label = "盔甲 ", language = Language.Chinese)
    })
    private final BooleanSetting armorValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Scale", language = Language.English),
            @Text(label = "大小 ", language = Language.Chinese)
    })
    private final NumberSetting scaleValue = new NumberSetting(1.5F, 1F, 4F, "#.00");
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊 ", language = Language.Chinese)
    })
    private final BooleanSetting blurValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Self", language = Language.English),
            @Text(label = "自己 ", language = Language.Chinese)
    })
    private final BooleanSetting self = new BooleanSetting(false);
    
    @SettingInfo(name = {
            @Text(label = "Dis Mc Name", language = Language.English),
            @Text(label = "禁用原版标签", language = Language.Chinese)
    })
    private final BooleanSetting disableVanilla = new BooleanSetting(true);
    
    @SettingInfo(name = {
            @Text(label = "Skia X Offset", language = Language.English),
            @Text(label = "Skia X 偏移", language = Language.Chinese)
    })
    private final NumberSetting skiaXOffset = new NumberSetting(-8F, -50F, 50F, "#.0");
    
    @SettingInfo(name = {
            @Text(label = "Skia Y Offset", language = Language.English),
            @Text(label = "Skia Y 偏移", language = Language.Chinese)
    })
    private final NumberSetting skiaYOffset = new NumberSetting(6F, -50F, 50F, "#.0");

    @SettingInfo(name = {
            @Text(label = "Render Mode", language = Language.English),
            @Text(label = "渲染模式", language = Language.Chinese)
    })
    private final ModeSetting renderMode = new ModeSetting("MC", Arrays.asList("MC", "Skia"),
            new SettingAttribute<>(skiaXOffset, "Skia"),
            new SettingAttribute<>(skiaYOffset, "Skia"),
            new SettingAttribute<>(blurValue, "Skia")
    );

    private static CanvasStack cachedCanvasStack = null;
    private static long lastCanvasUpdate = 0;
    private static final long CANVAS_CACHE_TIME = 100;
    private static class RenderState {
        float x, y, width, height;
        String mainText, subText;
        float healthRate;
        LivingEntity entity;
        boolean isValid = false;

        void update(float x, float y, float width, float height, String mainText, String subText, float healthRate, LivingEntity entity) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.mainText = mainText;
            this.subText = subText;
            this.healthRate = healthRate;
            this.entity = entity;
            this.isValid = true;
        }

        void invalidate() {
            this.isValid = false;
            this.entity = null;
        }
    }

    private final RenderState currentRenderState = new RenderState();

    public NameTags() {
        registerSetting(renderMode, healthValue, distanceValue, armorValue, scaleValue, self, disableVanilla);
    }

    @EventTarget(4)
    public void onRender3D(EventRender3D event) {
        if (mc.player == null)
            return;

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity) {
                if (entity == mc.player) {
                    if (mc.options.getCameraType().isFirstPerson()) continue;
                    if (!self.getValue()) {
                        continue;
                    }
                }

                if (!Utils.isValidEntity((LivingEntity) entity))
                    continue;

                renderNameTag((LivingEntity) entity, Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText()), event.getPoseStack());
            }
        }
    }

    private final DecimalFormat dFormat = new DecimalFormat("0.0");

    private void renderNameTag(LivingEntity entity, String tag, PoseStack poseStack) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");
        boolean bot = AntiBot.isBot(entity);
        boolean killer = KillerCheck.isKiller(entity);
        boolean friend = false;
        boolean team = Targets.isTeam(entity) || (clayGuns.isEnable() && clayGuns.teamName.contains(entity.getName().getString()));
        float a = HeypixelManager.getEntityHealth(entity);

        String nameColor = bot ? "§3" : entity.isInvisible() ? "§6" : entity.isCrouching() ? "§4" : ChatFormatting.WHITE + "";
        String distanceText = distanceValue.getValue() ? " §7" + (int) mc.player.distanceTo(entity) + "m" : "";
        String healthDisplay = dFormat.format(a);
        String healthText = healthValue.getValue() ? "§7§cHP: " + healthDisplay : "";
        String botText = bot ? " §c§lBot " : "";
        String killerText = killer ? " §cKiller " : "";
        String friendText = friend ? " §c§lFriend " : "";
        String teamText = team ? ChatFormatting.AQUA + "[Team] " : "";
        String text = healthText + distanceText;

        String attitudeText = "";
        if (entity instanceof Player player) {
            boolean isGodAxe = PlayerUtils.isHoldingGodAxe(player);
            attitudeText = isGodAxe ? " §4§l[GodAxe]" : "";
        }
        String mainText = teamText + nameColor + tag + botText + killerText + friendText + attitudeText;
        poseStack.pushPose();
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();

        poseStack.translate(
                entity.xOld + (entity.getX() - entity.xOld) * mc.getFrameTime() - renderManager.camera.getPosition().x,
                entity.yOld + (entity.getY() - entity.yOld) * mc.getFrameTime() - renderManager.camera.getPosition().y + entity.getEyeHeight() + 0.55,
                entity.zOld + (entity.getZ() - entity.zOld) * mc.getFrameTime() - renderManager.camera.getPosition().z
        );

        poseStack.mulPose(Axis.YP.rotationDegrees(-renderManager.camera.getYRot()));
        poseStack.mulPose(Axis.XP.rotationDegrees(renderManager.camera.getXRot()));

        float distance = (float) (mc.player.distanceTo(entity) * 0.25);
        if (distance < 1F) distance = 1F;
        float scale = distance / 100f * scaleValue.getValue().floatValue();
        poseStack.scale(-scale, -scale, scale);
        float width = Math.max(FontRenderers.misans18.getStringWidth(text), FontRenderers.misans18.getStringWidth(mainText)) * 0.5f;
        float height = FontRenderers.misans18.getHeight() + 16;
        float rate = HeypixelManager.getHealthRate(entity);
        currentRenderState.update(-width - 2F, -(height), width * 2 + 4F, height, mainText, text, rate, entity);
        if (renderMode.getValue().equals("Skia")) {
            renderWithSkiaInterception(poseStack);
        } else {
            renderWithVanilla(poseStack, mainText, text, entity);
        }
        if (armorValue.getValue() && entity instanceof Player) {
            renderArmor(poseStack, entity);
        }

        currentRenderState.invalidate();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        poseStack.popPose();
    }

    private void renderWithSkiaInterception(PoseStack poseStack) {
        if (!currentRenderState.isValid || currentRenderState.entity == null) return;

        CanvasStack canvasStack = getCachedSkiaCanvas();
        if (canvasStack == null) {
            renderTransparentVanilla(poseStack);
            return;
        }
        Vec3 screenPos = getCorrectScreenPosition(currentRenderState.entity);
        if (screenPos == null || !SkiaCompatLayer.isOnScreen(screenPos)) {
            renderTransparentVanilla(poseStack);
            return;
        }

        canvasStack.push();
        String displayText = buildDisplayText(currentRenderState.entity, currentRenderState.mainText);

        float baseTextWidth = SkiaFontManager.getDefaultFont12().getWidth(displayText);
        float baseTextHeight = SkiaFontManager.getDefaultFont12().getActualHeight();

        float manualScale = scaleValue.getValue().floatValue() / 1.5f;
        float textWidth = baseTextWidth * manualScale;
        float textHeight = baseTextHeight * manualScale;

        float screenX = (float) screenPos.x;
        float screenY = (float) screenPos.y;

        float xOffset = skiaXOffset.getValue().floatValue();
        float yOffset = skiaYOffset.getValue().floatValue();

        float actualTextWidth = SkiaFontManager.getDefaultFont14().getWidth(displayText) * manualScale;
        float actualTextHeight = SkiaFontManager.getDefaultFont14().getActualHeight() * manualScale;
        float textCenterX = screenX + xOffset;
        float textCenterY = screenY + yOffset;
        float textLeft = textCenterX - actualTextWidth / 2f;
        float textTop = textCenterY - actualTextHeight / 2f;
        float padding = 1.4f;
        float bgX = textLeft - padding;
        float bgY = textTop - padding;
        float bgWidth = actualTextWidth + (padding * 2);
        float bgHeight = actualTextHeight + (padding * 2);
        float cornerRadius = 4f;
        if (blurValue.getValue()) {
            float blurRadius = 8f;
            SkiaRender.drawBlurRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, blurRadius);
            int glassColor = new Color(240, 240, 240, 60).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, glassColor);
        } else {
            int backgroundColor = new Color(200, 200, 200, 120).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, backgroundColor);
        }
        canvasStack.push();
        canvasStack.canvas.scale(manualScale, manualScale);
        float scaledTextX = textLeft / manualScale;
        float scaledTextY = textTop / manualScale;

        SkiaFontManager.getDefaultFont14().drawText(canvasStack, displayText,
                scaledTextX, scaledTextY, Color.WHITE.getRGB());

        canvasStack.pop();
        canvasStack.pop();
    }

    private String buildDisplayText(LivingEntity entity, String entityName) {
        StringBuilder text = new StringBuilder();
        text.append(entityName);
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(entity);
            text.append(" [").append(dFormat.format(health)).append("HP]");
        }
        if (distanceValue.getValue()) {
            int distance = (int) mc.player.distanceTo(entity);
            text.append(" [").append(distance).append("m]");
        }

        return text.toString();
    }

    private void renderTransparentVanilla(PoseStack poseStack) {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderUtils.drawRect(poseStack, currentRenderState.x, currentRenderState.y,
                currentRenderState.width, currentRenderState.height,
                new Color(0, 0, 0, 0).getRGB());
    }

    private void renderWithVanilla(PoseStack poseStack, String mainText, String text, LivingEntity entity) {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();

        float mainTextWidth = FontRenderers.misans18.getStringWidth(mainText);
        float subTextWidth = FontRenderers.misans14.getStringWidth(text);
        float width = Math.max(mainTextWidth, subTextWidth) * 0.5f;
        float height = FontRenderers.misans18.getHeight();

        RenderUtils.drawRect(poseStack, -width - 2F, 0, width + 4F, -(height + 16),
                new Color(24, 24, 24, 160).getRGB());

        Color color = getHealthColor(currentRenderState.healthRate);
        float healthBarWidth = (width + 4F - (-width - 2F)) * currentRenderState.healthRate; // 原始计算方式
        RenderUtils.drawRect(poseStack, -width - 2F, -1.5f, healthBarWidth, 1.5f, color.getRGB());

        FontRenderers.misans18.drawString(poseStack, mainText, 1F - width, -20f, Color.WHITE.getRGB());
        FontRenderers.misans14.drawString(poseStack, text, 1F - width, -9F, Color.WHITE.getRGB());
    }

    private CanvasStack getCachedSkiaCanvas() {
        long currentTime = System.currentTimeMillis();
        if (cachedCanvasStack != null && (currentTime - lastCanvasUpdate) < CANVAS_CACHE_TIME) {
            return cachedCanvasStack;
        }
        try {
            if (SkiaProcess.INSTANCE == null ||
                    SkiaProcess.INSTANCE.skiaUtils == null ||
                    SkiaProcess.INSTANCE.skiaUtils.surface == null ||
                    SkiaProcess.INSTANCE.skiaUtils.canvas == null) {
                cachedCanvasStack = null;
                return null;
            }

            cachedCanvasStack = new CanvasStack(SkiaProcess.INSTANCE.skiaUtils);
            lastCanvasUpdate = currentTime;
            return cachedCanvasStack;

        } catch (Exception e) {
            cachedCanvasStack = null;
            return null;
        }
    }

    private Vec3 getCorrectScreenPosition(LivingEntity entity) {
        try {
            if (Main.INSTANCE.projection == null) {
                return null;
            }

            EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
            Camera camera = renderManager.camera;
            float partialTicks = mc.getFrameTime();

            double entityX = entity.xOld + (entity.getX() - entity.xOld) * partialTicks;
            double entityY = entity.yOld + (entity.getY() - entity.yOld) * partialTicks + entity.getEyeHeight() + 0.55;
            double entityZ = entity.zOld + (entity.getZ() - entity.zOld) * partialTicks;

            Vec3 relativePos = new Vec3(
                    entityX - camera.getPosition().x,
                    entityY - camera.getPosition().y,
                    entityZ - camera.getPosition().z
            );

            return Main.INSTANCE.projection.projectToScreen(relativePos);

        } catch (Exception e) {
            return null;
        }
    }

    private void renderArmor(PoseStack poseStack, LivingEntity entity) {
        int[] indices = new int[]{4, 5, 3, 2, 1, 0};

        for (int i = 0; i < indices.length; i++) {
            int index = indices[i];
            ItemStack equipmentInSlot = getEquipmentInSlot(index, entity);
            poseStack.pushPose();
            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            RenderSystem.disableDepthTest();
            RenderSystem.setShader(GameRenderer::getPositionColorShader);
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);

            GL20.glEnable(32823);
            GL20.glPolygonOffset(1.0f, -1000000.0f);
            RenderUtils.renderAndDecorateItem(poseStack, equipmentInSlot, -57 + i * 20, -12);
            GL20.glPolygonOffset(1.0f, 1000000.0f);
            GL20.glDisable(32823);

            RenderSystem.enableDepthTest();
            RenderSystem.disableBlend();
            poseStack.popPose();
        }
    }


    private Color getHealthColor(float rate) {
        if (rate > 0.75) {
            return Color.GREEN;
        } else if (rate <= 0.75f && rate >= 0.5f) {
            return Color.ORANGE;
        } else if (rate <= 0.5f && rate >= 0.25f) {
            return new Color(255, 97, 24);
        } else {
            return Color.RED;
        }
    }

    public ItemStack getEquipmentInSlot(int index, LivingEntity entity) {
        EquipmentSlot equipmentSlot = EquipmentSlot.MAINHAND;
        switch (index) {
            case 0 -> equipmentSlot = EquipmentSlot.FEET;
            case 1 -> equipmentSlot = EquipmentSlot.LEGS;
            case 2 -> equipmentSlot = EquipmentSlot.CHEST;
            case 3 -> equipmentSlot = EquipmentSlot.HEAD;
            case 4 -> {
            }
            case 5 -> equipmentSlot = EquipmentSlot.OFFHAND;
        }

        return entity.getItemBySlot(equipmentSlot);
    }
    
    public boolean isDisableVanilla() {
        return disableVanilla.getValue();
    }
}

