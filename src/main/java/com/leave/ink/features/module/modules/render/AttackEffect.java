package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventAttack;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LightningBolt;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.animal.Squid;
import net.minecraft.world.entity.item.PrimedTnt;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Random;
import java.util.Timer;
import java.util.TimerTask;

@ModuleInfo(name = {
        @Text(label = "AttackEffect", language = Language.English),
        @Text(label = "攻击特效", language = Language.Chinese)
}, category = Category.Render)
public class AttackEffect extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Keep", Arrays.asList("Keep", "Kill"));
    @SettingInfo(name = {
            @Text(label = "RenderMode", language = Language.English),
            @Text(label = "特效模式", language = Language.Chinese)
    })
    private final ModeSetting PaMode = new ModeSetting("Lightning", Arrays.asList("Squid", "TNT", "Lightning", "Heart", "Lava", "Smoke", "Slime", "Flame", "Explode"));

    public AttackEffect() {
        registerSetting(mode, PaMode);
    }

    LivingEntity target = null;
    boolean hitable = false;

    @EventTarget
    public void onAttack(EventAttack event) {
        if (event.getTargetEntity() instanceof LivingEntity) {
            target = (LivingEntity) event.getTargetEntity();
        }
    }

    @Override
    public String getSpaceName() {
        return "Attack Effect";
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");

        if (killAura.isEnable()) {
            if (target == null) {
                target = killAura.target;
            }
            hitable = true;
        }

        if (killAura.isEnable()) {
            if (mode.getValue().equals("Kill")) {
                if (target != null) {
                    if (target.getHealth() <= 0.01 || !target.isAlive()) {
                        effect(target);
                        target = null;
                        hitable = false;
                    }
                }
            }
        }

        if (killAura.isEnable()) {
            if (mode.getValue().equals("Kill")) {
                if (target == null || !hitable)
                    return;
            }
        } else {
            if (target == null)
                return;
        }

        if (killAura.isEnable()) {
            if (mode.getValue().equals("Keep")) {
                if (target != null && hitable) {
                    effect(target);
                    target = null;
                    hitable = false;
                }
            }
        } else {
            if (mode.getValue().equals("Kill")) {
                if (target != null) {
                    if (target.getHealth() <= 0.01 || !target.isAlive()) {
                        effect(target);
                        target = null;
                    }
                }
            }

            if (mode.getValue().equals("Keep")) {
                if (target != null) {
                    effect(target);
                    target = null;
                }
            }
        }
    }

    private void effect(Entity entity) {
        SimpleParticleType em = ParticleTypes.HEART;

        switch (PaMode.getValue().toLowerCase()) {
            case "heart" -> em = ParticleTypes.HEART;
            case "lava" -> em = ParticleTypes.LAVA;
            case "smoke" -> em = ParticleTypes.SMOKE;
            case "slime" -> em = ParticleTypes.ITEM_SLIME;
            case "flame" -> em = ParticleTypes.FLAME;
            case "explode" -> em = ParticleTypes.EXPLOSION;
        }

        switch (PaMode.getValue()) {
            case "Squid" -> {
                Random random = new Random();
                Squid squid = new Squid(EntityType.SQUID, mc.level);
                squid.setXRot(0.0f);
                squid.xRotO = 0.0f;
                squid.setYRot(0.0f);
                squid.yRotO = 0.0f;
                squid.setYBodyRot(90.0f);
                squid.setYHeadRot(90.0f);
                squid.moveTo(entity.getX(), entity.getY(), entity.getZ());
                squid.setDeltaMovement(squid.getDeltaMovement().x, squid.getDeltaMovement().y + 0.5, squid.getDeltaMovement().z);
                addEntity(mc.level, squid);
                Timer timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        for (int i = 0; i < 8; i++) {
                            mc.level.addParticle(ParticleTypes.FLAME,
                                    squid.getX() + (random.nextDouble() - random.nextDouble()) * 4.0D,
                                    squid.getY() + (random.nextDouble() - random.nextDouble()) * 4.0D,
                                    squid.getZ() + (random.nextDouble() - random.nextDouble()) * 4.0D,
                                    1,
                                    1,
                                    1);
                        }
                        squid.kill();
                        squid.onRemovedFromWorld();
                        squid.setRemoved(Entity.RemovalReason.KILLED);
                    }
                }, 1000);
            }
            case "Lightning" -> {
                LightningBolt lightningEntity = new LightningBolt(EntityType.LIGHTNING_BOLT, mc.level);
                lightningEntity.moveTo(entity.getX(), entity.getY(), entity.getZ());
                addEntity(mc.level, lightningEntity);
                mc.level.playLocalSound(entity.getX(), entity.getY(), entity.getZ(), SoundEvents.GENERIC_EXPLODE, mc.player.getSoundSource(), 1.0F, 1.0F, false);
                mc.level.playLocalSound(entity.getX(), entity.getY(), entity.getZ(), SoundEvents.LIGHTNING_BOLT_THUNDER, mc.player.getSoundSource(), 1.0F, 1.0F, false);
            }
            case "TNT" -> {
                Random random = new Random();
                BlockPos pos = BlockPos.containing( entity.getX(),  (entity.getY() + 0.5), entity.getZ());
                PrimedTnt tnt = new PrimedTnt(mc.level, pos.getX() + 0.5, pos.getY(), pos.getZ() + 0.5, mc.player);
                tnt.setFuse(15);
                tnt.setDeltaMovement(tnt.getDeltaMovement().x, tnt.getDeltaMovement().y + 0.5, tnt.getDeltaMovement().z);
                tnt.moveTo(entity.getX(), entity.getY(), entity.getZ());
                addEntity(mc.level, tnt);
                Timer timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        for (int i = 0; i < 20; i++) {
                            mc.level.addParticle(ParticleTypes.EXPLOSION,
                                    tnt.getX() + (random.nextDouble() - random.nextDouble()) * 4.0D,
                                    tnt.getY() + (random.nextDouble() - random.nextDouble()) * 4.0D,
                                    tnt.getZ() + (random.nextDouble() - random.nextDouble()) * 4.0D,
                                    1,
                                    1,
                                    1);
                        }
                        mc.level.playLocalSound(tnt.getX(), tnt.getY(), tnt.getZ(), SoundEvents.GENERIC_EXPLODE, mc.player.getSoundSource(), 1.0F, 1.0F, false);
                        tnt.kill();
                    }
                }, 750);
            }
            default -> mc.level.addParticle(em, entity.getX(), entity.getY(), entity.getZ(), 1.0, 1.0, 1.0);
        }
    }

    private void addEntity(ClientLevel level, Entity entity) {
        try {
            Method method = ObfuscationReflectionHelper.findMethod(ClientLevel.class, "addEntity", int.class, Entity.class);
            method.setAccessible(true);
            method.invoke(level, 0, entity);
        } catch (IllegalAccessException | InvocationTargetException ignored) {
        }
    }

    public double easeInOutCirc(double x) {
        return x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2;
    }

    @Override
    public String getTag() {
        return PaMode.getValue();
    }
}
