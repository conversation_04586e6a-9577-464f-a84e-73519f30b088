package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPlayerTick;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.PingSpoofUtils;

import java.util.Random;

@ModuleInfo(name = {
        @Text(label = "FakeLag", language = Language.English),
        @Text(label = "假延迟", language = Language.Chinese)
}, category = Category.World)
public class FakeLag extends Module {
    @SettingInfo(name = {
            @Text(label = "MinMs", language = Language.English),
            @Text(label = "最小延迟", language = Language.Chinese)
    })
    private final NumberSetting min = new NumberSetting(200,0,5000,"#");
    @SettingInfo(name = {
            @Text(label = "MaxMs", language = Language.English),
            @Text(label = "最大延迟", language = Language.Chinese)
    })
    private final NumberSetting max = new NumberSetting(200,0,5000,"#");
    private final Random random = new Random();

    public FakeLag() {
        registerSetting(min,max);
    }

    private double random(double min, double max) {
        if(min == max) return max;
        return min + (max - min) * random.nextDouble();
    }
    @EventTarget
    public void on(EventPlayerTick eventPlayerTick) {
        int ms = (int) random(min.getValue().floatValue(), max.getValue().floatValue());
        boolean blinkIncoming = true;
        PingSpoofUtils.spoof(ms, blinkIncoming, blinkIncoming, blinkIncoming, blinkIncoming, blinkIncoming, true);

        if (mc.player.hurtTime > 0) {
            PingSpoofUtils.dispatch();
        }
    }
}