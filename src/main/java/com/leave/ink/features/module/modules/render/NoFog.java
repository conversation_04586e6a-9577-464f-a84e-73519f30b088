package com.leave.ink.features.module.modules.render;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "NoFog", language = Language.English),
        @Text(label = "无迷雾", language = Language.Chinese)
}, category = Category.Render)
public class NoFog extends Module {

    @SettingInfo(name = {
            @Text(label = "No End Fog", language = Language.English),
            @Text(label = "禁用末地迷雾", language = Language.Chinese)
    })
    public BooleanSetting noEndFog = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "No Water Fog", language = Language.English),
            @Text(label = "禁用水下迷雾", language = Language.Chinese)
    })
    public BooleanSetting noWaterFog = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "No Lava Fog", language = Language.English),
            @Text(label = "禁用熔岩迷雾", language = Language.Chinese)
    })
    public BooleanSetting noLavaFog = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "No Weather Fog", language = Language.English),
            @Text(label = "禁用天气迷雾", language = Language.Chinese)
    })
    public BooleanSetting noWeatherFog = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "No All Fog", language = Language.English),
            @Text(label = "禁用所有迷雾", language = Language.Chinese)
    })
    public BooleanSetting noAllFog = new BooleanSetting(false);

    public NoFog() {
        registerSetting(noEndFog, noWaterFog, noLavaFog, noWeatherFog, noAllFog);
    }

    public static boolean shouldDisableFog() {
        NoFog noFog = (NoFog) com.leave.ink.Main.INSTANCE.moduleManager.getModule("NoFog");
        return noFog != null && noFog.isEnable() && noFog.noAllFog.getValue();
    }

    public static boolean shouldDisableEndFog() {
        NoFog noFog = (NoFog) com.leave.ink.Main.INSTANCE.moduleManager.getModule("NoFog");
        return noFog != null && noFog.isEnable() && noFog.noEndFog.getValue();
    }

    public static boolean shouldDisableWaterFog() {
        NoFog noFog = (NoFog) com.leave.ink.Main.INSTANCE.moduleManager.getModule("NoFog");
        return noFog != null && noFog.isEnable() && noFog.noWaterFog.getValue();
    }

    public static boolean shouldDisableLavaFog() {
        NoFog noFog = (NoFog) com.leave.ink.Main.INSTANCE.moduleManager.getModule("NoFog");
        return noFog != null && noFog.isEnable() && noFog.noLavaFog.getValue();
    }

    public static boolean shouldDisableWeatherFog() {
        NoFog noFog = (NoFog) com.leave.ink.Main.INSTANCE.moduleManager.getModule("NoFog");
        return noFog != null && noFog.isEnable() && noFog.noWeatherFog.getValue();
    }
} 