package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.google.common.collect.Lists;
import com.leave.ink.Main;
import com.leave.ink.events.EventAttack;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.Timer;
import com.leave.ink.utils.rotation.RotationUtils;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;

@ModuleInfo(name = {
        @Text(label = "HitBubbles", language = Language.English),
        @Text(label = "击打气泡", language = Language.Chinese)
}, category = Category.Render)
public class HitBubbles extends Module {
    @SettingInfo(name = {
            @Text(label = "LifeTime", language = Language.English),
            @Text(label = "存在延迟", language = Language.Chinese)
    })
    private final NumberSetting lifeTime = new NumberSetting(30, 1, 150, "#");

    public HitBubbles() {
        registerSetting(lifeTime);
    }

    private final ArrayList<HitBubble> bubbles = new ArrayList<>();

    @EventTarget
    public void onAttack(EventAttack e) {
        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
        if(RotationUtils.rotations == null) return;
        Vec3 point = RotationUtils.getRtxPoint(RotationUtils.rotations.getYaw(), RotationUtils.rotations.getPitch(), killAura.rangeValue.getValue().floatValue());
        if (point != null)
            bubbles.add(new HitBubble((float) point.x, (float) point.y, (float) point.z, -mc.player.getYRot(), mc.player.getXRot(), 0, new Timer()));
    }

    public void onRender3D(PoseStack matrixStack) {
        RenderSystem.disableDepthTest();
        ArrayList<HitBubble> bubblesCopy = Lists.newArrayList(bubbles);
        bubblesCopy.forEach(b -> {
            matrixStack.pushPose();
            matrixStack.translate(b.x - mc.getEntityRenderDispatcher().camera.getPosition().x(), b.y - mc.getEntityRenderDispatcher().camera.getPosition().y(), b.z - mc.getEntityRenderDispatcher().camera.getPosition().z());
            matrixStack.mulPose(Axis.YP.rotationDegrees(b.yaw));
            matrixStack.mulPose(Axis.XP.rotationDegrees(b.pitch));
            RenderUtils.drawBubble(matrixStack, -b.life.getPassedTimeMs() / 4f, b.life.getPassedTimeMs() / 1500f);
            matrixStack.popPose();
        });
        RenderSystem.enableDepthTest();
        bubbles.removeIf(b -> b.life.passedMs(lifeTime.getValue().intValue() * 50L));
    }

    public record HitBubble(float x, float y, float z, float yaw, float pitch, float eyeHeight, Timer life) {
    }
}
