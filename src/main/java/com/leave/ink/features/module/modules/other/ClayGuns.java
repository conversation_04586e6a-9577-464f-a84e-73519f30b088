package com.leave.ink.features.module.modules.other;

import YeQing.ClassObfuscator;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.misc.ClassUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.rotation.ClipRotation;
import com.leave.ink.utils.rotation.RotationUtils;
import io.netty.buffer.Unpooled;
import net.minecraft.Util;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.protocol.game.*;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.HoeItem;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import org.lwjgl.glfw.GLFW;
import java.awt.*;
import java.util.*;

@ClassObfuscator
@ModuleInfo(name = {
        @Text(label = "CSGuns", language = Language.English),
        @Text(label = "CS枪械", language = Language.Chinese)
}, category = Category.Other)
public class ClayGuns extends Module {
    @SettingInfo(name = {
            @Text(label = "Attack", language = Language.English),
            @Text(label = "攻击", language = Language.Chinese)
    })
    public final ModeSetting attack = new ModeSetting("Auto", Arrays.asList("Auto", "Manual"));
    @SettingInfo(name = {
            @Text(label = "Rotation", language = Language.English),
            @Text(label = "转头", language = Language.Chinese)
    })
    public final ModeSetting rotation = new ModeSetting("Silent", Arrays.asList("Silent", "Player"));
    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(50, 10, 500, "#.0");
    @SettingInfo(name = {
            @Text(label = "Fov", language = Language.English),
            @Text(label = "视角", language = Language.Chinese)
    })
    private final NumberSetting fov = new NumberSetting(180.0, 1.0, 180.0, "#");
    @SettingInfo(name = {
            @Text(label = "Aim", language = Language.English),
            @Text(label = "瞄准", language = Language.Chinese)
    })
    public final BooleanSetting aim = new BooleanSetting(true,
            new SettingAttribute<>(attack, true),
            new SettingAttribute<>(rotation, true),
            new SettingAttribute<>(range, true),
            new SettingAttribute<>(fov, true));
    @SettingInfo(name = {
            @Text(label = "NoRecoil", language = Language.English),
            @Text(label = "无后坐力", language = Language.Chinese)
    })
    private final BooleanSetting noRecoil = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "NoRaiseNG", language = Language.English),
            @Text(label = "无举镜负面效果", language = Language.Chinese)
    })
    public final BooleanSetting noRaiseNG = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "InstantReload", language = Language.English),
            @Text(label = "瞬间换弹", language = Language.Chinese)
    })
    public final BooleanSetting instantRD = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "FastFireTick", language = Language.English),
            @Text(label = "快速开火时刻", language = Language.Chinese)
    })
    private final NumberSetting fastCount = new NumberSetting(2d, 2d, 10d, "#");
    @SettingInfo(name = {
            @Text(label = "FastFire", language = Language.English),
            @Text(label = "快速开火", language = Language.Chinese)
    })
    public final BooleanSetting fastFire = new BooleanSetting(false, new SettingAttribute<>(fastCount, true));
    @SettingInfo(name = {
            @Text(label = "NoChatMessage", language = Language.English),
            @Text(label = "没有聊天信息", language = Language.Chinese)
    })
    public final BooleanSetting noChat = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "TeamGame", language = Language.English),
            @Text(label = "团队游戏", language = Language.Chinese)
    })
    public final BooleanSetting teamGame = new BooleanSetting(false);
    private boolean resetDown = true;
    private LivingEntity currentTarget = null;
    public Set<String> teamName = new HashSet<>();
    public ClipRotation clipRotation;
    private FriendlyByteBuf reloadDate;
    private ResourceLocation clayRes;
    private boolean runSendPacket;
    private int reloadCount = 0;
    private int reloadTick = 0;

    public ClayGuns() {
        registerSetting(aim, noRecoil, noRaiseNG, instantRD, fastFire, noChat, teamGame);
    }

    @Override
    protected void onEnable() {
        if (!ClassUtils.hasClass("clay.byte")) {
            ChatUtils.displayAlert("检测到您不在枪战服务器");
            toggle();
        }
    }

    @Override
    protected void onDisable() {
        if (Utils.isNull())
            return;

        teamName.clear();
        currentTarget = null;
        reloadDate = null;
        runSendPacket = false;
        reloadCount = 0;
        reloadTick = 0;
        mc.options.keyAttack.setDown(GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyAttack.getKey().getValue()) == GLFW.GLFW_PRESS);
        mc.options.keyUse.setDown(GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyUse.getKey().getValue()) == GLFW.GLFW_PRESS);
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (noChat.getValue()) {
            if (event.getPacket() instanceof ClientboundPlayerChatPacket || event.getPacket() instanceof ClientboundSystemChatPacket)
                event.setCancelled(true);
        }

        if (event.getPacket() instanceof ServerboundCustomPayloadPacket payloadPacket) {
            if (payloadPacket.getIdentifier().toDebugFileName().contains("clay")) {
                clayRes = payloadPacket.getIdentifier();
                HexFormat hexFormat = HexFormat.of().withUpperCase();
                FriendlyByteBuf data = payloadPacket.getData();
                byte[] bytes = new byte[data.readableBytes()];
                data.getBytes(data.readerIndex(), bytes);
                if (hexFormat.formatHex(bytes).contains("8B08000000000000FF8B36D")) {
                    if (instantRD.getValue()) {
                        reloadCount++;
                        runSendPacket = true;

                        if (reloadCount == 2) {
                            reloadDate = new FriendlyByteBuf(Unpooled.buffer());
                            data.readerIndex(0);
                            reloadDate.writeBytes(data);
                            event.setCancelled(true);
                            reloadCount = 0;
                        }
                    }
                }
            }
        }
    }

    @EventTarget
    public void onUpdate(EventPlayerTick event) {
        if (aim.getValue()) {
            findTarget();
            executeAimAndAttack();
        }

        if (instantRD.getValue()) {
            if (runSendPacket && reloadDate != null) {
                reloadTick++;

                if (reloadTick >= 3) {
                    PacketUtils.sendPacketNoEvent(new ServerboundCustomPayloadPacket(clayRes, reloadDate));
                    reloadTick = 0;
                    runSendPacket = false;
                }
            }
        }

        if (teamGame.getValue()) {
            String hexData = "0735C81550E2B553EAEADE7FABB5552375E287A441717DF08FAB7E711AC4FEAD4F3B69849ABA503144CEA8F45F5126D70BC652F6DF7025011B461F8B08000000000000FF8B8E8E564ACECF2BCECF4955D2512A494DCC2D2C4D2D4D55502DC849AC4C2D52552849C9558A8DD5512ACDCBCECB2FCF538A0500015BC2173200000020A921FD632ACCBB95BA9FFF4677D29A1CC7348A71388ABC30DE16F93B21B0360165025017DAFA729554F004CBBBBF94E79ACF0A433DF921AD887AAA8CA32CF535E701EF3E36E84BE01FCD551A7755A81B8F31E1AFF9E21F732BFB41F1AF5F786E3464A4F572518881AAF810B353B762EC16A5C4B3C8E2E425BD025AC3C1956BDF7269372F9E09";
            HexFormat hexFormat = HexFormat.of();
            byte[] bytes = hexFormat.parseHex(hexData);
            FriendlyByteBuf buf = new FriendlyByteBuf(Unpooled.wrappedBuffer(bytes));
            PacketUtils.sendPacketNoEvent(new ServerboundCustomPayloadPacket(clayRes, buf));
            teamGame.setValue(false);
        }
    }

    @EventTarget
    public void onMotion(EventMotion event) {
        if (event.getEventType() == EventType.PRE && aim.getValue()) {
            if (currentTarget != null && clipRotation.getRotation() != null) {
                if (rotation.getValue().equals("Player"))
                    clipRotation.getRotation().toPlayer(mc.player);
            }
        }
    }

    @EventTarget
    public void onSlow(EventSlow event) {
        if (mc.options.keyUse.isDown() && noRaiseNG.getValue() && mc.player.getMainHandItem().getItem() instanceof HoeItem) {
            event.setUsingItem(false);
        }
    }

    @EventTarget
    public void onInput(EventMoveInput event) {
        if (mc.options.keyUse.isDown() && noRaiseNG.getValue() && mc.player.getMainHandItem().getItem() instanceof HoeItem) {
            event.setSneakSlowDownMultiplier(1f);
        }
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        teamName.clear();
    }

    @EventTarget
    public void onText(EventText event) {
        if (!event.getText().isEmpty()) {
            if (event.getColor() == Color.WHITE.getRGB()) {
                teamName.add(event.getText());
            }
        }
    }

    private void executeAimAndAttack() {
        boolean runAttack = currentTarget != null && clipRotation != null && clipRotation.isCanSee();

        if (attack.getValue().equals("Auto")) {
            if (resetDown && !runAttack) {
                mc.options.keyAttack.setDown(GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyAttack.getKey().getValue()) == GLFW.GLFW_PRESS);
                mc.options.keyUse.setDown(GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyUse.getKey().getValue()) == GLFW.GLFW_PRESS);
                resetDown = false;
            } else if (!runAttack) return;

            if (runAttack && mc.player.getMainHandItem().getItem() instanceof HoeItem) {
                mc.options.keyAttack.setDown(true);
                mc.options.keyUse.setDown(true);
                resetDown = true;
            }
        }
    }

    private void findTarget() {
        currentTarget = null;
        final float maxRange = range.getValue().floatValue();
        final float maxRangeSq = range.getValue().floatValue() * range.getValue().floatValue();
        final float maxFov = fov.getValue().floatValue();
        LivingEntity bestTarget = null;
        double bestScore = Double.MAX_VALUE;

        Vec3 playerPos = mc.player.getEyePosition(1.0f);

        AABB searchBox = new AABB(
                playerPos.x - maxRange, playerPos.y - maxRange, playerPos.z - maxRange,
                playerPos.x + maxRange, playerPos.y + maxRange, playerPos.z + maxRange
        );

        for (Entity entity : mc.level.getEntities(mc.player, searchBox)) {
            if (!(entity instanceof LivingEntity livingEntity)) continue;
            if (Main.INSTANCE.friendsManager.isFriend(livingEntity.getName().getString())) continue;
            if (!livingEntity.isAlive() || livingEntity == mc.player) continue;
            if (!Utils.isValidEntity(livingEntity) || Targets.isTeam(livingEntity)) continue;
            if (livingEntity.isSpectator()) continue;
            if (teamName.contains(livingEntity.getName().getString())) continue;

            Vec3 entityPos = livingEntity.getBoundingBox().getCenter();
            double dx = entityPos.x - playerPos.x;
            double dy = entityPos.y - playerPos.y;
            double dz = entityPos.z - playerPos.z;
            double distanceSq = dx * dx + dy * dy + dz * dz;
            if (distanceSq > maxRangeSq)
                continue;

            double fovDiff = RotationUtils.getRotationDifference(livingEntity);
            if (fovDiff <= maxFov || fovDiff == 180F) {
                double currentScore = fovDiff + distanceSq * 0.001;
                if (currentScore < bestScore) {
                    clipRotation = ClipRotation.getClipEntity(livingEntity, 0);
                    bestScore = currentScore;

                    if (clipRotation != null && clipRotation.isCanSee())
                        bestTarget = livingEntity;
                }
            }
        }

        currentTarget = bestTarget;
    }

    public boolean canAttackTarget() {
        return currentTarget != null && clipRotation != null && clipRotation.isCanSee();
    }

    public void onFastFire() {
        if (mc.options.keyAttack.isDown() && mc.options.keyUse.isDown() && mc.player.isCrouching()) {
            for (int i = 0; i <= fastCount.getValue().intValue(); i++) {
                ForgeEventFactory.onPreClientTick();
                mc.getProfiler().push("gui");
                mc.getChatListener().tick();
                mc.gui.tick(mc.isPaused());
                mc.getProfiler().pop();
                mc.gameRenderer.pick(1.0F);
                mc.getTutorial().onLookAt(mc.level, mc.hitResult);
                mc.getProfiler().push("gameMode");
                if (!mc.isPaused() && mc.level != null) {
                    mc.gameMode.tick();
                }
            }
            mc.getProfiler().popPush("textures");
        } else if (mc.options.keyUse.isDown() && !mc.player.isCrouching()) {
            ForgeEventFactory.onPreClientTick();
            mc.getProfiler().push("gui");
            mc.getChatListener().tick();
            mc.gui.tick(mc.isPaused());
            mc.getProfiler().pop();
            mc.gameRenderer.pick(1.0F);
            mc.getTutorial().onLookAt(mc.level, mc.hitResult);
            mc.getProfiler().push("gameMode");
            if (!mc.isPaused() && mc.level != null) {
                mc.gameMode.tick();
            }
            mc.getProfiler().popPush("textures");
        } else if (mc.options.keyAttack.isDown()) {
            for (int i = 0; i <= fastCount.getValue().intValue(); i++) {
                ForgeEventFactory.onPreClientTick();
                mc.getProfiler().push("gui");
                mc.getChatListener().tick();
                mc.gui.tick(mc.isPaused());
                mc.getProfiler().pop();
                mc.gameRenderer.pick(1.0F);
                mc.getTutorial().onLookAt(mc.level, mc.hitResult);
                mc.getProfiler().push("gameMode");
                if (!mc.isPaused() && mc.level != null) {
                    mc.gameMode.tick();
                }
            }
            mc.getProfiler().popPush("textures");
        } else {
            ForgeEventFactory.onPreClientTick();
            mc.getProfiler().push("gui");
            mc.getChatListener().tick();
            mc.gui.tick(mc.isPaused());
            mc.getProfiler().pop();
            mc.gameRenderer.pick(1.0F);
            mc.getTutorial().onLookAt(mc.level, mc.hitResult);
            mc.getProfiler().push("gameMode");
            if (!mc.isPaused() && mc.level != null) {
                mc.gameMode.tick();
            }
            mc.getProfiler().popPush("textures");
        }
    }

    public void setXRot(Entity instance, float p_146927_) {
        if (!isCalledByClayPackage() || !noRecoil.getValue()) {
            if (!Float.isFinite(p_146927_)) {
                Util.logAndPauseIfInIde("Invalid entity rotation: " + p_146927_ + ", discarding.");
            } else {
                ObfuscationReflectionHelper.setPrivateValue(Entity.class, instance, p_146927_, "xRot");
            }
        }
    }

    public void setYRot(Entity instance, float p_146923_) {
        if (!isCalledByClayPackage() || !noRecoil.getValue()) {
            if (!Float.isFinite(p_146923_)) {
                Util.logAndPauseIfInIde("Invalid entity rotation: " + p_146923_ + ", discarding.");
            } else {
                ObfuscationReflectionHelper.setPrivateValue(Entity.class, instance, p_146923_, "yRot");
            }
        }
    }

    public boolean isCalledByClayPackage() {
        return StackWalker.getInstance(StackWalker.Option.RETAIN_CLASS_REFERENCE).walk(stream ->
                stream.skip(2).limit(15).anyMatch(frame -> frame.getClassName().startsWith("clay."))
        );
    }
}
