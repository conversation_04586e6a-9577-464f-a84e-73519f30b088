package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.projectiles.InterpolatedVector4;
import com.leave.ink.utils.render.projectiles.ProjectileType;
import com.leave.ink.utils.render.projectiles.TracePoint;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.core.BlockPos;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;


import java.awt.*;
import java.util.List;
@ModuleInfo(name = {
        @Text(label = "Projectiles", language = Language.English),
        @Text(label = "抛物线显示", language = Language.Chinese)
}, category = Category.Render)
public class Projectiles extends Module {

    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    private final ColorSetting color = new ColorSetting(new Color(255, 255, 255, 255));
    public InterpolatedVector4 rotationX = new InterpolatedVector4(0.0F, 0.0F, 0.0F);
    public InterpolatedVector4 rotationY = new InterpolatedVector4(0.0F, 0.0F, 0.0F);

    public Projectiles() {
        registerSetting(color);
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (!mc.player.getMainHandItem().isEmpty()) {
            ProjectileType var4 = ProjectileType.getProjectileThingyForItem( mc.player.getMainHandItem().getItem());
            if (var4 != null) {
                float rotYawRadians = (float) Math.toRadians( mc.player.getYRot() - 25.0F);
                double var7 = 0.2F;
                double averageEdgeLengthDiv2 =  mc.player.getBoundingBox().getSize() / 2.0;
                double var11 = Math.cos(rotYawRadians) * averageEdgeLengthDiv2;
                double var13 = Math.sin(rotYawRadians) * averageEdgeLengthDiv2;
                PoseStack poseStack = event.getPoseStack();
                RenderSystem.enableBlend();
                RenderSystem.defaultBlendFunc();

                RenderSystem.disableDepthTest();
                RenderSystem.depthMask(false);
                RenderSystem.setShader(GameRenderer::getPositionColorShader);
                poseStack.pushPose();
                Camera camera =  mc.gameRenderer.getMainCamera();
                Vec3 cameraPos = camera.getPosition();
                RenderSystem.lineWidth(10.0F);
                Tesselator tesselator = Tesselator.getInstance();
                BufferBuilder bufferBuilder = tesselator.getBuilder();
                bufferBuilder.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);

                List<TracePoint> traceThings = var4.getTraceThings();
                for (int i = 0; i < traceThings.size(); i++) {
                    TracePoint point = traceThings.get(i);
                    double progress = (float) (i + 1) / traceThings.size();
                    double offsetX = var11 * (1 - progress);
                    double offsetZ = var13 * (1 - progress);
                    double offsetY = var7 * (1 - progress);

                    Vec3 pos = point.getPosition()
                            .subtract(cameraPos)
                            .subtract(offsetX, offsetY, offsetZ);

                    float alpha = Math.min(1, i) * 0.05f;
                    bufferBuilder.vertex(poseStack.last().pose(), (float) pos.x, (float) pos.y, (float) pos.z)
                            .color(0f, 0f, 0f, alpha)
                            .endVertex();
                }


                tesselator.end();


                // 绘制白色轨迹
                RenderSystem.lineWidth(2.0F * (float)  mc.getWindow().getGuiScale());
                bufferBuilder.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);

                for (int i = 0; i < traceThings.size(); i++) {
                    TracePoint point = traceThings.get(i);
                    double progress = (float) (i + 1) / traceThings.size();
                    double offsetX = var11 * (1 - progress);
                    double offsetZ = var13 * (1 - progress);
                    double offsetY = var7 * (1 - progress);

                    Vec3 pos = point.getPosition()
                            .subtract(cameraPos)
                            .subtract(offsetX, offsetY, offsetZ);

                    float alpha = Math.min(1, i) * 0.75f;
                    bufferBuilder.vertex(poseStack.last().pose(), (float) pos.x, (float) pos.y, (float) pos.z)
                            .color(1f, 1f, 1f, alpha)
                            .endVertex();
                }

                tesselator.end();

                // 绘制碰撞框
                if (var4.rayTraceResult == null) {
                    if (var4.hitEntity != null) {
/*                        AABB renderBox = getRenderBoundingBox(var4);
                        ESPUtils.renderEntityBoundingBox(poseStack, renderBox, new Color(-16723258), 0.1f);*/
                        RenderUtils.drawLineBox(poseStack,var4.hitEntity.getBoundingBox(),color.getValue());
                    }
                } else {
                    double var49 = var4.traceX - mc.gameRenderer.getMainCamera().getPosition().x;
                    double var42 = var4.traceY - mc.gameRenderer.getMainCamera().getPosition().y;
                    double var45 = var4.traceZ - mc.gameRenderer.getMainCamera().getPosition().z;
                    poseStack.pushPose();
                    poseStack.translate(var49, var42, var45);
                    BlockPos blockPos = new BlockPos(0, 0, 0)
                            .relative((var4.rayTraceResult).getDirection());


                    RenderUtils.rotate(
                            poseStack,
                            45,
                            this.rotationX.interpolateX((float) blockPos.getX()),
                            this.rotationX.interpolateY((float) (-blockPos.getY())),
                            this.rotationX.interpolateZ((float) blockPos.getZ()));
                    RenderUtils.rotate(
                            poseStack,
                            90,
                            this.rotationY.interpolateX((float) blockPos.getZ()),
                            this.rotationY.interpolateY((float) blockPos.getY()),
                            this.rotationY.interpolateZ((float) (-blockPos.getX())));


                    poseStack.translate(-0.5F, 0.0F, -0.5F);
                    RenderSystem.setShader(GameRenderer::getPositionColorShader);
                    AABB box = new AABB(
                            0.0, 0.0, 0.0,  // minX, minY, minZ
                            1.0, 0, 1.0   // maxX, maxY, maxZ
                    );
                    Color color = new Color(-21931);
                    color = new Color(color.getRed(), color.getGreen(), color.getBlue(), 25);

                    //RenderUtils.drawFilledBoundingBox(poseStack, (float) box.minX, (float) box.maxX, (float) box.minY, (float) box.maxY, (float) box.minZ, (float) box.maxZ, color.getRGB(), color.getRGB(), 0.3f);


                    RenderUtils.drawBoundingBox(poseStack, box, color.getRed(), color.getGreen(), color.getBlue());


                    poseStack.popPose();
                }

                // 恢复渲染状态
                poseStack.popPose();
                RenderSystem.enableDepthTest();
                RenderSystem.depthMask(true);
               // RenderSystem.enableTexture();
                RenderSystem.disableBlend();
            }
        }
    }



}
