package com.leave.ink.features.module.modules.movement;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "NoJumpDelay", language = Language.English),
        @Text(label = "没有跳跃延迟", language = Language.Chinese)
}, category = Category.Movement)
public class NoJumpDelay extends Module {
    @Override
    public String getSpaceName() {
        return "No Jump Delay";
    }
}
