package com.leave.ink.features.module.modules.movement;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "InvMove", language = Language.English),
        @Text(label = "背包行走", language = Language.Chinese)
}, category = Category.Movement)
public class InvMove extends Module {
}
