package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.PathUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.multiplayer.ClientPacketListener;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundMoveEntityPacket;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import net.minecraft.network.protocol.game.ServerboundPlayerAbilitiesPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Abilities;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@ModuleInfo(name = {
        @Text(label = "InfiniteAura", language = Language.English),
        @Text(label = "百米大刀", language = Language.Chinese)
}, category = Category.Combat)
public class InfiniteAura extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting modeValue = new ModeSetting("Auto", Arrays.asList("Auto", "Click"));
    @SettingInfo(name = {
            @Text(label = "PacketMode", language = Language.English),
            @Text(label = "发包模式", language = Language.Chinese)
    })
    private final ModeSetting packetValue = new ModeSetting("Position", Arrays.asList("Position", "PosLook"));
    @SettingInfo(name = {
            @Text(label = "DoTeleportBackPacket", language = Language.English),
            @Text(label = "传送返回包", language = Language.Chinese)
    })
    private final BooleanSetting packetBack = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Targets", language = Language.English),
            @Text(label = "最大目标", language = Language.Chinese)
    })
    private final NumberSetting targetsValue = new NumberSetting(3, 1, 10, "#");
    @SettingInfo(name = {
            @Text(label = "CPS", language = Language.English),
            @Text(label = "点击", language = Language.Chinese)
    })
    private final NumberSetting cpsValue =  new NumberSetting(1, 1, 20, "#");
    @SettingInfo(name = {
            @Text(label = "Distance", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting distValue =  new NumberSetting(30, 20, 500, "#");
    @SettingInfo(name = {
            @Text(label = "MoveDistance", language = Language.English),
            @Text(label = "移动距离间隔", language = Language.Chinese)
    })
    private final NumberSetting moveDistanceValue = new NumberSetting(5, 2, 15, "#.0");
    @SettingInfo(name = {
            @Text(label = "NoRegen", language = Language.English),
            @Text(label = "无恢复", language = Language.Chinese)
    })
    private final BooleanSetting noRegenValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "NoLagBack", language = Language.English),
            @Text(label = "无回弹返回", language = Language.Chinese)
    })
    private final BooleanSetting noLagBackValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Swing", language = Language.English),
            @Text(label = "挥手", language = Language.Chinese)
    })
    private final BooleanSetting swingValue = new BooleanSetting(true);
    private final TimeUtils timer = new TimeUtils();
    private Thread thread;

    private int getDelay() {
        return 1000 / cpsValue.getValue().intValue();
    }

    public InfiniteAura() {
        registerSetting(modeValue, packetValue, packetBack, targetsValue, cpsValue, distValue, moveDistanceValue, noRegenValue, noLagBackValue, swingValue);
    }

    @Override
    public String getTag() {
        return modeValue.getValue();
    }

    @Override
    protected void onEnable() {
        if (Utils.isNull())
            return;

        timer.reset();
    }

    @Override
    protected void onDisable() {
        if (Utils.isNull())
            return;

        timer.reset();
        if(thread != null)
            thread.stop();
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (!timer.hasTimeElapsed(getDelay()))
            return;

        if (thread != null && thread.isAlive())
            return;

        if (modeValue.getValue().equals("Auto")) {
            thread = new Thread(this::doTpAura, "Run");
            thread.start();
            timer.reset();
        } else {
            if (mc.options.keyAttack.isDown()) {
                thread = new Thread(() -> {
                    HitResult result = mc.hitResult;
                    if (result == null || result.getType() != HitResult.Type.ENTITY) return;

                    LivingEntity entity = (LivingEntity) ((EntityHitResult) result).getEntity();
                    if (!Utils.isValidEntity(entity))
                        return;

                    if (mc.player.distanceTo(entity) < 3)
                        return;

                    hit(entity, true);
                }, "Run");
                thread.start();
                timer.reset();
            }
        }
    }

    private void doTpAura() {
        if (mc.level == null || mc.player == null) return;

        List<LivingEntity> targets = new ArrayList<>();
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity livingEntity) {
                if (livingEntity != mc.player && Utils.isValidEntity(livingEntity) && !Targets.isTeam(livingEntity) && mc.player.distanceTo(livingEntity) < distValue.getValue().intValue()) {
                    targets.add(livingEntity);
                }
            }
        }

        targets.sort(Comparator.comparingDouble(e -> mc.player.distanceTo(e)));

        if (targets.isEmpty()) return;

        int count = 0;
        for (LivingEntity entity : targets) {
            if (hit(entity, false)) {
                count++;
            }
            if (count >= targetsValue.getValue().intValue()) break;
        }
    }

    public boolean hit(LivingEntity entity, boolean force) {
        List<Vec3> path = PathUtils.findBlinkPath(mc.player.getX(), mc.player.getY(), mc.player.getZ(), entity.getX(), entity.getY(), entity.getZ(), moveDistanceValue.getValue().floatValue());

        if (path.isEmpty()) {
            return false;
        }

        double lastDistance = path.get(path.size() - 1).distanceTo(entity.position());
        if (!force && lastDistance > 10) {
            return false;
        }

        ClientPacketListener connection = mc.getConnection();
        if (connection == null) {
            return false;
        }

        for (Vec3 vec : path) {
            if (packetValue.getValue().equals("Position")) {
                connection.send(new ServerboundMovePlayerPacket.PosRot(vec.x, vec.y, vec.z, mc.player.getYRot(), mc.player.getXRot(), true));
            } else {
                connection.send(new ServerboundMovePlayerPacket.PosRot(vec.x, vec.y, vec.z, mc.player.getYRot(), mc.player.getXRot(), true));
            }
        }

        if (lastDistance > 3 && packetBack.getValue()) {
            connection.send(new ServerboundMovePlayerPacket.PosRot(entity.getX(), entity.getY(), entity.getZ(), mc.player.getYRot(), mc.player.getXRot(), true));
        }

        if (swingValue.getValue()) {
            mc.player.swing(InteractionHand.MAIN_HAND);
        } else {
            connection.send(new ServerboundMovePlayerPacket.Rot(mc.player.getYRot(), mc.player.getXRot(), mc.player.onGround()));
        }
        mc.gameMode.attack(mc.player, entity);

        for (int i = path.size() - 1; i >= 0; i--) {
            Vec3 vec = path.get(i);
            if (packetValue.getValue().equals("Position")) {
                connection.send(new ServerboundMovePlayerPacket.PosRot(vec.x, vec.y, vec.z, mc.player.getYRot(), mc.player.getXRot(), true));
            } else {
                connection.send(new ServerboundMovePlayerPacket.PosRot(vec.x, vec.y, vec.z, mc.player.getYRot(), mc.player.getXRot(), true));
            }
        }

        if (packetBack.getValue()) {
            connection.send(new ServerboundMovePlayerPacket.PosRot(mc.player.getX(), mc.player.getY(), mc.player.getZ(), mc.player.getYRot(), mc.player.getXRot(), true));
        }
        return true;
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        Packet<?> packet = event.getPacket();

        if (packet instanceof ClientboundMoveEntityPacket.PosRot && ((ClientboundMoveEntityPacket.PosRot) packet).getEntity(mc.level) == mc.player) {
            timer.reset();
        }

        boolean isMovePacket = (packet instanceof ServerboundMovePlayerPacket.Pos || packet instanceof ServerboundMovePlayerPacket.PosRot);

        if (noRegenValue.getValue() && packet instanceof ServerboundMovePlayerPacket && !isMovePacket) {
            event.setCancelled(true);
        }

        if (noLagBackValue.getValue() && packet instanceof ClientboundMoveEntityPacket.PosRot && ((ClientboundMoveEntityPacket.PosRot) packet).getEntity(mc.level) == mc.player) {
            Abilities capabilities = new Abilities();
            capabilities.mayfly = true;
            mc.getConnection().send(new ServerboundPlayerAbilitiesPacket(capabilities));
            event.setCancelled(true);
            mc.getConnection().send(new ServerboundMovePlayerPacket.PosRot(
                    ((ClientboundMoveEntityPacket.PosRot) packet).getXa(),
                    ((ClientboundMoveEntityPacket.PosRot) packet).getYa(),
                    ((ClientboundMoveEntityPacket.PosRot) packet).getZa(),
                    ((ClientboundMoveEntityPacket.PosRot) packet).getyRot(),
                    ((ClientboundMoveEntityPacket.PosRot) packet).getxRot(),
                    true
            ));
        }
    }
}
