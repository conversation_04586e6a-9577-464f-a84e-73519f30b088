package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventMoveInput;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventSlow;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.mapping.Mapping;
import com.leave.ink.utils.player.InventoryUtils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.multiplayer.prediction.BlockStatePredictionHandler;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.network.protocol.game.ServerboundPlayerActionPacket;
import net.minecraft.network.protocol.game.ServerboundSetCarriedItemPacket;
import net.minecraft.network.protocol.game.ServerboundUseItemPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.item.Items;
import java.lang.reflect.Field;

@ModuleInfo(name = {
        @Text(label = "Gapple", language = Language.English),
        @Text(label = "自动苹果", language = Language.Chinese)
}, category = Category.Combat)
public class Gapple extends Module {
    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量", language = Language.Chinese)
    })
    private final NumberSetting healthValue = new NumberSetting(10d, 1.0d, 20d, "#.0");

    public int eatTick = 0;
    private int sequence = 0;
    private boolean isEat = false;

    public Gapple() {
        registerSetting(healthValue);
    }

    @Override
    protected void onDisable() {
        isEat = false;
        eatTick = 0;
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacket() instanceof ServerboundPlayerActionPacket playerActionPacket) {
            if (playerActionPacket.getAction() == ServerboundPlayerActionPacket.Action.RELEASE_USE_ITEM) {
                if (eatTick != 0)
                    event.setCancelled(true);
            }
        }
    }

    @EventTarget
    public void onMove(EventMoveInput event) {
        if (eatTick > 0) {
            EventSlow eventSlow = new EventSlow(0.2F, true);
            EventManager.call(eventSlow);

            if (eventSlow.isSlowDown() && !mc.player.isPassenger()) {
                event.setForward(event.getForward() * eventSlow.getAmount());
                event.setStrafe(event.getStrafe() * eventSlow.getAmount());
                ObfuscationReflectionHelper.setPrivateValue(LocalPlayer.class, mc.player, 0, "sprintTriggerTime");
            }
        }
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        int gapple = InventoryUtils.findItem(0, 9, Items.GOLDEN_APPLE);

        if (mc.player.getHealth() <= healthValue.getValue().floatValue()) {
            if (eatTick == 0) {
                if (mc.player.getOffhandItem().is(Items.GOLDEN_APPLE)) {
                    mc.getConnection().send(new ServerboundUseItemPacket(InteractionHand.OFF_HAND, sequence));
                    mc.player.startUsingItem(InteractionHand.OFF_HAND);
                    eatTick = 32;
                } else {
                    if (gapple != -1) {
                        mc.getConnection().send(new ServerboundSetCarriedItemPacket(gapple));
                        mc.getConnection().send(new ServerboundUseItemPacket(InteractionHand.MAIN_HAND, sequence));
                        isEat = true;
                        eatTick = 32;
                    }
                }
            }
        } else {
            if (isEat) {
                mc.getConnection().send(new ServerboundSetCarriedItemPacket(mc.player.getInventory().selected));
                isEat = false;
            }

            if (!mc.player.getOffhandItem().is(Items.GOLDEN_APPLE) || gapple == -1)
                eatTick = 0;
        }

        if (eatTick > 0)
            eatTick--;

        try {
            String blockStatePredictionHandler = Mapping.get(ClientLevel.class, "blockStatePredictionHandler", null);
            if (blockStatePredictionHandler == null)
                return;

            Field field = ClientLevel.class.getDeclaredField(blockStatePredictionHandler);
            field.setAccessible(true);
            BlockStatePredictionHandler handler = (BlockStatePredictionHandler) field.get(mc.level);
            try (BlockStatePredictionHandler pendingUpdateManager = handler.startPredicting()) {
                sequence = pendingUpdateManager.currentSequence();
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public String getTag() {
        return String.format("%.2f", healthValue.getValue().floatValue());
    }
}
