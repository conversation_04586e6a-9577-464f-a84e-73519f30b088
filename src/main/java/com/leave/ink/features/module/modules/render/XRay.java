package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventTick;
import com.leave.ink.events.EventType;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.render.engine.Render3DEngine;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;


import java.awt.Color;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.leave.ink.utils.client.ChatUtils;

@ModuleInfo(
        name = {
                @Text(label = "XRay", language = Language.English),
                @Text(label = "矿物透视", language = Language.Chinese)
        },
        category = Category.Render
)
public class XRay extends Module {

    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    public ModeSetting mode = new ModeSetting("Standard", Arrays.asList("Standard", "Exposed"));

    @SettingInfo(name = {
            @Text(label = "Render Mode", language = Language.English),
            @Text(label = "渲染模式", language = Language.Chinese)
    })
    public ModeSetting renderMode = new ModeSetting("Glow", Arrays.asList("Glow", "Line", "Both"));

    @SettingInfo(name = {
            @Text(label = "Blocks", language = Language.English),
            @Text(label = "方块", language = Language.Chinese)
    })
    public ModeSetting blockType = new ModeSetting("Ores", Arrays.asList("Ores", "Valuables", "Custom"));

    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "范围", language = Language.Chinese)
    })
    public NumberSetting range = new NumberSetting(30.0, 5.0, 100.0, "#");


    @SettingInfo(name = {
            @Text(label = "Diamond Color", language = Language.English),
            @Text(label = "钻石颜色", language = Language.Chinese)
    })
    public ColorSetting diamondColor = new ColorSetting(new Color(0, 255, 255));
    @SettingInfo(name = {
            @Text(label = "DIAMOND", language = Language.English),
            @Text(label = "钻石", language = Language.Chinese)
    })
    public BooleanSetting DIAMOND = new BooleanSetting(true, new SettingAttribute<>(diamondColor, true));

    @SettingInfo(name = {
            @Text(label = "Emerald Color", language = Language.English),
            @Text(label = "绿宝石颜色", language = Language.Chinese)
    })
    public ColorSetting emeraldColor = new ColorSetting(new Color(0, 255, 0));

    @SettingInfo(name = {
            @Text(label = "EMERALD", language = Language.English),
            @Text(label = "绿宝石", language = Language.Chinese)
    })
    public BooleanSetting EMERALD = new BooleanSetting(true,new SettingAttribute<>(emeraldColor, true));
    @SettingInfo(name = {
            @Text(label = "Redstone Color", language = Language.English),
            @Text(label = "红石颜色", language = Language.Chinese)
    })
    public ColorSetting redstoneColor = new ColorSetting(new Color(255, 0, 0));


    @SettingInfo(name = {
            @Text(label = "REDSTONE", language = Language.English),
            @Text(label = "红石", language = Language.Chinese)
    })
    public BooleanSetting REDSTONE = new BooleanSetting(true,new SettingAttribute<>(redstoneColor, true));

    @SettingInfo(name = {
            @Text(label = "Iron Color", language = Language.English),
            @Text(label = "铁颜色", language = Language.Chinese)
    })
    public ColorSetting ironColor = new ColorSetting(new Color(210, 210, 210));

    @SettingInfo(name = {
            @Text(label = "IRON", language = Language.English),
            @Text(label = "铁", language = Language.Chinese)
    })
    public BooleanSetting IRON = new BooleanSetting(true,new SettingAttribute<>(ironColor, true));

    @SettingInfo(name = {
            @Text(label = "Gold Color", language = Language.English),
            @Text(label = "金颜色", language = Language.Chinese)
    })
    public ColorSetting goldColor = new ColorSetting(new Color(255, 215, 0));

    @SettingInfo(name = {
            @Text(label = "GOLD", language = Language.English),
            @Text(label = "金", language = Language.Chinese)
    })
    public BooleanSetting GOLD = new BooleanSetting(true,new SettingAttribute<>(goldColor, true));

    @SettingInfo(name = {
            @Text(label = "Copper Color", language = Language.English),
            @Text(label = "铜颜色", language = Language.Chinese)
    })
    public ColorSetting copperColor = new ColorSetting(new Color(205, 127, 50));


    @SettingInfo(name = {
            @Text(label = "COPPER", language = Language.English),
            @Text(label = "铜", language = Language.Chinese)
    })
    public BooleanSetting COPPER = new BooleanSetting(true,new SettingAttribute<>(copperColor, true));

    @SettingInfo(name = {
            @Text(label = "Lapis Color", language = Language.English),
            @Text(label = "青金石颜色", language = Language.Chinese)
    })
    public ColorSetting lapisColor = new ColorSetting(new Color(0, 0, 255));

    @SettingInfo(name = {
            @Text(label = "LAPIS", language = Language.English),
            @Text(label = "青金石", language = Language.Chinese)
    })
    public BooleanSetting LAPIS = new BooleanSetting(true,new SettingAttribute<>(lapisColor, true));

    @SettingInfo(name = {
            @Text(label = "Coal Color", language = Language.English),
            @Text(label = "煤颜色", language = Language.Chinese)
    })
    public ColorSetting coalColor = new ColorSetting(new Color(50, 50, 50));

    @SettingInfo(name = {
            @Text(label = "COAL", language = Language.English),
            @Text(label = "煤", language = Language.Chinese)
    })
    public BooleanSetting COAL = new BooleanSetting(false,new SettingAttribute<>(coalColor, true));


    @SettingInfo(name = {
            @Text(label = "Other Blocks Color", language = Language.English),
            @Text(label = "其他方块颜色", language = Language.Chinese)
    })
    public ColorSetting otherColor = new ColorSetting(new Color(0, 255, 0));

    @SettingInfo(name = {
            @Text(label = "Cave Minimum Size", language = Language.English),
            @Text(label = "洞穴最小大小", language = Language.Chinese)
    })
    public NumberSetting caveMinSize = new NumberSetting(10.0, 5.0, 50.0, "#");

    @SettingInfo(name = {
            @Text(label = "Minimum Vein Size", language = Language.English),
            @Text(label = "最小矿脉大小", language = Language.Chinese)
    })
    public NumberSetting minimumVeinSize = new NumberSetting(2.0, 1.0, 10.0, "#");

    @SettingInfo(name = {
            @Text(label = "Vein Search Radius", language = Language.English),
            @Text(label = "矿脉搜索半径", language = Language.Chinese)
    })
    public NumberSetting veinSearchRadius = new NumberSetting(2.0, 1.0, 5.0, "#");

    @SettingInfo(name = {
            @Text(label = "Minimum Ore Authenticity Score", language = Language.English),
            @Text(label = "最低矿脉真实度分数", language = Language.Chinese)
    })
    public NumberSetting minOreAuthenticityScore = new NumberSetting(6.0, 1.0, 10.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Use Multithreading", language = Language.English),
            @Text(label = "使用多线程", language = Language.Chinese)
    })
    public BooleanSetting useMultithreading = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Thread Count", language = Language.English),
            @Text(label = "线程数量", language = Language.Chinese)
    })
    public NumberSetting threadCount = new NumberSetting(2.0, 1.0, 8.0, "#");

    @SettingInfo(name = {
            @Text(label = "Chunk Processing Size", language = Language.English),
            @Text(label = "区块处理大小", language = Language.Chinese)
    })
    public NumberSetting chunkSize = new NumberSetting(16.0, 4.0, 32.0, "#");

    @SettingInfo(name = {
            @Text(label = "Max FloodFill Size", language = Language.English),
            @Text(label = "最大填充方块数", language = Language.Chinese)
    })
    public NumberSetting maxCaveFloodFillSize = new NumberSetting(500.0, 100.0, 2000.0, "#");

    @SettingInfo(name = {
            @Text(label = "End Portal Frame Color", language = Language.English),
            @Text(label = "末地传送门颜色", language = Language.Chinese)
    })
    public ColorSetting endPortalFrameColor = new ColorSetting(new Color(128, 0, 128));

    @SettingInfo(name = {
            @Text(label = "Render End Portal Frames", language = Language.English),
            @Text(label = "渲染末地传送门", language = Language.Chinese)
    })
    public BooleanSetting RENDER_END_PORTAL_FRAMES = new BooleanSetting(true, new SettingAttribute<>(endPortalFrameColor, true));

    @SettingInfo(name = {
            @Text(label = "Log Portal Frame Coords", language = Language.English),
            @Text(label = "记录框架坐标", language = Language.Chinese)
    })
    public BooleanSetting logEndPortalFramesSetting = new BooleanSetting(true, new SettingAttribute<>(RENDER_END_PORTAL_FRAMES, true));

    @SettingInfo(name = {
            @Text(label = "Log Mineshaft Coords", language = Language.English),
            @Text(label = "记录矿井坐标", language = Language.Chinese)
    })
    public BooleanSetting logMineshaftCoordinates = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Detect Mineshafts", language = Language.English),
            @Text(label = "探测废弃矿井", language = Language.Chinese)
    })
    public BooleanSetting detectMineshafts = new BooleanSetting(false, new SettingAttribute<>(logMineshaftCoordinates, true));

    private long lastLoggedFrameTime = 0;
    private static final long LOG_FRAME_INTERVAL = 1000; // 1秒

    private final ConcurrentHashMap<BlockPos, Block> oreBlocks = new ConcurrentHashMap<>();
    private final Set<Set<BlockPos>> validCaves = new HashSet<>();
    private long lastScanTime = 0;
    private static final long SCAN_DELAY = 1000;
    private BlockPos lastPlayerPosForScan = null;
    private static final double MIN_MOVEMENT_SQR_FOR_RESCAN = 4.0;
    private ExecutorService caveSearchExecutor;
    private AtomicBoolean isProcessing = new AtomicBoolean(false);
    private static final int[][] DIRECTIONS_OPTIMIZED_FILL = {
            {1, 0, 0}, {-1, 0, 0},
            {0, 1, 0}, {0, -1, 0},
            {0, 0, 1}, {0, 0, -1}
    };

    private final Set<BlockPos> detectedMineshaftLocations = ConcurrentHashMap.newKeySet();
    private long lastLoggedMineshaftTime = 0;
    private static final long MINESHAFT_LOG_INTERVAL = 60000;
    private static final int MINESHAFT_MIN_FEATURES = 10;

    private final Object scanLock = new Object();

    public XRay() {
        registerSetting(mode, renderMode, blockType, range,
                DIAMOND,
                EMERALD,
                REDSTONE,
                IRON,
                GOLD,
                COPPER,
                LAPIS,
                COAL,
                otherColor, caveMinSize,
                minimumVeinSize, veinSearchRadius,
                minOreAuthenticityScore,
                useMultithreading, threadCount, chunkSize, maxCaveFloodFillSize,
                logEndPortalFramesSetting,
                detectMineshafts
        );
    }

    @Override
    public void onEnable() {
        super.onEnable();
        oreBlocks.clear();
        validCaves.clear();
        lastPlayerPosForScan = null;
        lastScanTime = 0;
        lastLoggedFrameTime = 0;
        detectedMineshaftLocations.clear();
        lastLoggedMineshaftTime = 0;
        isProcessing = new AtomicBoolean(false);

        if (useMultithreading.getValue()) {
            initThreadPool();
        }
    }

    @Override
    public void onDisable() {
        super.onDisable();
        shutdownThreadPool();
    }

    private void initThreadPool() {
        shutdownThreadPool();
        int threads = Math.max(1, threadCount.getValue().intValue());
        caveSearchExecutor = Executors.newFixedThreadPool(threads);
        isProcessing = new AtomicBoolean(false);
    }

    private void shutdownThreadPool() {
        if (caveSearchExecutor != null && !caveSearchExecutor.isShutdown()) {
            caveSearchExecutor.shutdown();
            try {
                if (!caveSearchExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    caveSearchExecutor.shutdownNow();
                    if (!caveSearchExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        System.err.println("XRay ThreadPool did not terminate after shutdownNow and wait.");
                    }
                }
            } catch (InterruptedException e) {
                caveSearchExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            } finally {
            }
        }
    }

    @EventTarget
    public void onTick(EventTick event) {
        if (event.getEventType() != EventType.PRE) return;
        if (mc.player == null || mc.level == null) return;

        processFrameNotifications();

        if (mode.getValue().equals("Exposed")) {
            processMineshaftNotifications();
        }

        if (isProcessing.get()) {
            return;
        }

        long currentTime = System.currentTimeMillis();

        if (currentTime - lastScanTime < SCAN_DELAY) {
            return;
        }

        BlockPos currentPlayerPos = mc.player.blockPosition();
        boolean hasMovedSignificantly = true;

        if (lastPlayerPosForScan != null) {
            if (currentPlayerPos.distSqr(lastPlayerPosForScan) < MIN_MOVEMENT_SQR_FOR_RESCAN) {
                hasMovedSignificantly = false;
            }
        } else {
            hasMovedSignificantly = true;
        }

        if (!hasMovedSignificantly && lastPlayerPosForScan != null) {
            return;
        }
        lastScanTime = currentTime;
        lastPlayerPosForScan = currentPlayerPos;
        oreBlocks.clear();
        validCaves.clear();

        if (mc.player == null || mc.level == null) return;

        int scanRange = range.getValue().intValue();

        if (mode.getValue().equals("Standard")) {
            if (useMultithreading.getValue()) {
                standardScanMultithreaded(currentPlayerPos, scanRange);
            } else {
                if (isProcessing.compareAndSet(false, true)) {
                    try {
                        standardScan(currentPlayerPos, scanRange);
                    } finally {
                        isProcessing.set(false);
                    }
                }
            }
        } else if (mode.getValue().equals("Exposed")) {
            if (useMultithreading.getValue()) {
                exposedScanMultithreaded(currentPlayerPos, scanRange);
            } else {
                if (isProcessing.compareAndSet(false, true)) {
                    try {
                        exposedScan(currentPlayerPos, scanRange);
                    } finally {
                        isProcessing.set(false);
                    }
                }
            }
        }
    }

    private void standardScan(BlockPos playerPos, int scanRange) {
        int px = playerPos.getX();
        int py = playerPos.getY();
        int pz = playerPos.getZ();

        for (int x = px - scanRange; x <= px + scanRange; x++) {
            for (int y = Math.max(mc.level.getMinBuildHeight(), py - scanRange); y <= Math.min(mc.level.getMaxBuildHeight(), py + scanRange); y++) {
                for (int z = pz - scanRange; z <= pz + scanRange; z++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    Block block = mc.level.getBlockState(pos).getBlock();
                    if (isTargetBlock(block)) {
                        oreBlocks.put(pos, block);
                    }
                }
            }
        }
    }

    private void exposedScan(BlockPos playerPos, int scanRange) {
        int px = playerPos.getX();
        int py = playerPos.getY();
        int pz = playerPos.getZ();
        validCaves.clear();
        Set<BlockPos> visitedInScan = new HashSet<>();

        for (int x = px - scanRange; x <= px + scanRange; x++) {
            for (int y = Math.max(mc.level.getMinBuildHeight(), py - scanRange); y <= Math.min(mc.level.getMaxBuildHeight(), py + scanRange); y++) {
                for (int z = pz - scanRange; z <= pz + scanRange; z++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    if (isAirLike(pos) && !visitedInScan.contains(pos)) {
                        Set<BlockPos> caveBlocks = floodFillCaveBFS(pos, visitedInScan, scanRange, playerPos);
                        if (caveBlocks.size() >= caveMinSize.getValue().intValue()) {
                            if (RENDER_END_PORTAL_FRAMES.getValue()) {
                                for (BlockPos caveBlock : caveBlocks) {
                                    for (Direction dir : Direction.values()) {
                                        BlockPos adjacentPos = caveBlock.relative(dir);
                                        Block block = mc.level.getBlockState(adjacentPos).getBlock();
                                        if (block == Blocks.END_PORTAL) {
                                            oreBlocks.put(adjacentPos, block);
                                        }
                                    }
                                }
                            }

                            if (validateNaturalCave(caveBlocks)) {
                                synchronized (validCaves) {
                                    validCaves.add(caveBlocks);
                                }

                                for (BlockPos caveBlock : caveBlocks) {
                                    for (Direction dir : Direction.values()) {
                                        BlockPos adjacentPos = caveBlock.relative(dir);
                                        Block block = mc.level.getBlockState(adjacentPos).getBlock();

                                        if (isTargetBlock(block)) {
                                            double authenticityScore = analyzeOreAuthenticity(adjacentPos, block);
                                            if (authenticityScore >= minOreAuthenticityScore.getValue().doubleValue() &&
                                                    isOrePartOfVein(adjacentPos, block,
                                                            minimumVeinSize.getValue().intValue(),
                                                            veinSearchRadius.getValue().intValue())) {
                                                oreBlocks.put(adjacentPos, block);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private Set<BlockPos> floodFillCaveBFS(BlockPos startNodePos, Set<BlockPos> visitedGlobal, int maxRangeFromPlayer, BlockPos playerPos) {
        Set<BlockPos> currentCaveBlocks = new HashSet<>();
        Queue<BlockPos> queue = new LinkedList<>();
        int maxFillSize = maxCaveFloodFillSize.getValue().intValue();

        queue.add(startNodePos);
        currentCaveBlocks.add(startNodePos);

        while (!queue.isEmpty() && currentCaveBlocks.size() < maxFillSize) {
            BlockPos currentPos = queue.poll();

            for (int[] dir : DIRECTIONS_OPTIMIZED_FILL) {
                BlockPos nextPos = new BlockPos(
                        currentPos.getX() + dir[0],
                        currentPos.getY() + dir[1],
                        currentPos.getZ() + dir[2]
                );

                if (nextPos.distManhattan(playerPos) > maxRangeFromPlayer) {
                    continue;
                }

                if (isAirLike(nextPos)) {
                    if (visitedGlobal.add(nextPos)) {
                        if (currentCaveBlocks.size() < maxFillSize) {
                            currentCaveBlocks.add(nextPos);
                            queue.add(nextPos);
                        } else {
                            break;
                        }
                    }
                }
            }
            if (currentCaveBlocks.size() >= maxFillSize) {
                break;
            }
        }
        return currentCaveBlocks;
    }

    private boolean validateNaturalCave(Set<BlockPos> cave) {
        if (cave.stream().anyMatch(this::hasAdjacentNaturalBlocks)) {
            return true;
        }
        final double LINEAR_FACTOR_THRESHOLD = 0.7;
        final double BRANCH_FACTOR_THRESHOLD = 0.3;
        final double HEIGHT_VARIANCE_THRESHOLD = 3.0;
        double linearFactor = calculateLinearFactor(cave);
        if (linearFactor >= LINEAR_FACTOR_THRESHOLD) {
            return false;
        }
        double branchFactor = calculateBranchFactor(cave);
        if (branchFactor <= BRANCH_FACTOR_THRESHOLD) {
            return false;
        }
        double heightVariance = calculateHeightVariance(cave);
        return heightVariance > HEIGHT_VARIANCE_THRESHOLD;
    }
    private double calculateLinearFactor(Set<BlockPos> cave) {
        if (cave.size() < 3) return 1.0;
        BlockPos[] extremePoints = findExtremePoints(cave);
        BlockPos start = extremePoints[0];
        BlockPos end = extremePoints[1];
        double maxDist = start.distManhattan(end);
        double totalDeviation = 0;
        for (BlockPos pos : cave) {
            totalDeviation += pointToLineDistance(pos, start, end);
        }
        double avgDeviation = totalDeviation / cave.size();
        return Math.max(0, Math.min(1, 1 - (avgDeviation / maxDist)));
    }

    private BlockPos[] findExtremePoints(Set<BlockPos> cave) {
        if (cave == null || cave.size() < 2) {
            java.util.List<BlockPos> tempList = new ArrayList<>(Objects.requireNonNullElseGet(cave, HashSet::new));
            if (tempList.isEmpty()) return new BlockPos[]{BlockPos.ZERO, BlockPos.ZERO};
            BlockPos p = tempList.get(0);
            return new BlockPos[]{p, p};
        }

        java.util.List<BlockPos> positions = new ArrayList<>(cave);
        BlockPos p1 = positions.get(0);
        BlockPos p2 = p1;
        int maxDistP1P2 = -1;
        for (BlockPos currentPos : positions) {
            int dist = currentPos.distManhattan(p1);
            if (dist > maxDistP1P2) {
                maxDistP1P2 = dist;
                p2 = currentPos;
            }
        }

        BlockPos p3 = p2;
        int maxDistP2P3 = -1;
        for (BlockPos currentPos : positions) {
            int dist = currentPos.distManhattan(p2);
            if (dist > maxDistP2P3) {
                maxDistP2P3 = dist;
                p3 = currentPos;
            }
        }
        return new BlockPos[]{p2, p3};
    }


    private double pointToLineDistance(BlockPos point, BlockPos lineStart, BlockPos lineEnd) {
        double x0 = point.getX();
        double y0 = point.getY();
        double z0 = point.getZ();

        double x1 = lineStart.getX();
        double y1 = lineStart.getY();
        double z1 = lineStart.getZ();

        double x2 = lineEnd.getX();
        double y2 = lineEnd.getY();
        double z2 = lineEnd.getZ();

        double dx = x2 - x1;
        double dy = y2 - y1;
        double dz = z2 - z1;
        double lineLength2 = dx * dx + dy * dy + dz * dz;

        if (lineLength2 == 0) return Math.sqrt((x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1) + (z0 - z1) * (z0 - z1));
        double t = ((x0 - x1) * dx + (y0 - y1) * dy + (z0 - z1) * dz) / lineLength2;
        t = Math.max(0, Math.min(1, t));

        double projX = x1 + t * dx;
        double projY = y1 + t * dy;
        double projZ = z1 + t * dz;

        return Math.sqrt((x0 - projX) * (x0 - projX) + (y0 - projY) * (y0 - projY) + (z0 - projZ) * (z0 - projZ));
    }


    private double calculateBranchFactor(Set<BlockPos> cave) {
        if (cave.size() < 10) return 0.0;

        int junctions = 0;
        for (BlockPos pos : cave) {
            int adjacentAir = 0;
            for (Direction dir : Direction.values()) {
                if (cave.contains(pos.relative(dir))) {
                    adjacentAir++;
                }
            }

            if (adjacentAir >= 3) {
                junctions++;
            }
        }
        return (double) junctions / cave.size();
    }


    private boolean hasAdjacentNaturalBlocks(BlockPos pos) {
        for (Direction dir : Direction.values()) {
            BlockPos adjacent = pos.relative(dir);
            Block block = mc.level.getBlockState(adjacent).getBlock();
            if (block == Blocks.DEEPSLATE ||
                    block == Blocks.CALCITE ||
                    block == Blocks.DRIPSTONE_BLOCK ||
                    block == Blocks.AMETHYST_BLOCK ||
                    block == Blocks.SMOOTH_BASALT ||
                    block == Blocks.TUFF) {
                return true;
            }
        }
        return false;
    }

    private double calculateHeightVariance(Set<BlockPos> cave) {
        if (cave.size() < 3) return 0.0;
        double avgY = cave.stream().mapToDouble(BlockPos::getY).average().orElse(0);
        double variance = cave.stream()
                .mapToDouble(pos -> Math.pow(pos.getY() - avgY, 2))
                .sum() / cave.size();

        return Math.sqrt(variance);
    }


    private boolean isAirLike(BlockPos pos) {
        BlockState state = mc.level.getBlockState(pos);
        return state.isAir() ||
                !state.canOcclude() ||
                state.getBlock() == Blocks.WATER ||
                state.getBlock() == Blocks.LAVA ||
                state.getBlock() == Blocks.CAVE_AIR;
    }

    private boolean isTargetBlock(Block block) {
        switch (blockType.getValue()) {
            case "Ores" -> {
                return (DIAMOND.getValue() && block == Blocks.DIAMOND_ORE) ||
                        (DIAMOND.getValue() && block == Blocks.DEEPSLATE_DIAMOND_ORE) ||
                        (EMERALD.getValue() && block == Blocks.EMERALD_ORE) ||
                        (EMERALD.getValue() && block == Blocks.DEEPSLATE_EMERALD_ORE) ||
                        (REDSTONE.getValue() && block == Blocks.REDSTONE_ORE) ||
                        (REDSTONE.getValue() && block == Blocks.DEEPSLATE_REDSTONE_ORE) ||
                        (IRON.getValue() && block == Blocks.IRON_ORE) ||
                        (IRON.getValue() && block == Blocks.DEEPSLATE_IRON_ORE) ||
                        (GOLD.getValue() && block == Blocks.GOLD_ORE) ||
                        (GOLD.getValue() && block == Blocks.DEEPSLATE_GOLD_ORE) ||
                        (COPPER.getValue() && block == Blocks.COPPER_ORE) ||
                        (COPPER.getValue() && block == Blocks.DEEPSLATE_COPPER_ORE) ||
                        (LAPIS.getValue() && block == Blocks.LAPIS_ORE) ||
                        (LAPIS.getValue() && block == Blocks.DEEPSLATE_LAPIS_ORE) ||
                        (COAL.getValue() && block == Blocks.COAL_ORE) ||
                        (COAL.getValue() && block == Blocks.DEEPSLATE_COAL_ORE);
            }
            case "Valuables" -> {
                return (block == Blocks.ANCIENT_DEBRIS) ||
                        (block == Blocks.NETHER_GOLD_ORE) ||
                        (block == Blocks.NETHER_QUARTZ_ORE) ||
                        (block == Blocks.OBSIDIAN) ||
                        (block == Blocks.AMETHYST_BLOCK) ||
                        (block == Blocks.BUDDING_AMETHYST);
            }
            default -> {
                return (DIAMOND.getValue() && block == Blocks.DIAMOND_ORE) ||
                        (DIAMOND.getValue() && block == Blocks.DEEPSLATE_DIAMOND_ORE);
            }
        }
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (mc.player == null || mc.level == null) return;
        for (Map.Entry<BlockPos, Block> entry : oreBlocks.entrySet()) {
            BlockPos pos = entry.getKey();
            Block block = entry.getValue();

            AABB box = new AABB(pos);
            Color color = getColorForBlock(block);

            switch (renderMode.getValue()) {
                case "Glow" -> {
                    AABB fillBox = new AABB(
                            pos.getX() + 0.1, pos.getY() + 0.1, pos.getZ() + 0.1,
                            pos.getX() + 0.9, pos.getY() + 0.9, pos.getZ() + 0.9
                    );
                    Color fillColor = new Color(color.getRed(), color.getGreen(), color.getBlue(), 100);
                    Render3DEngine.drawFilledBox(event.getPoseStack(), fillBox, fillColor);
                }
                case "Line" -> {
                    RenderUtils.drawLineBox(event.getPoseStack(), box, color);
                }
                case "Both" -> {
                    AABB fillBox = new AABB(
                            pos.getX() + 0.1, pos.getY() + 0.1, pos.getZ() + 0.1,
                            pos.getX() + 0.9, pos.getY() + 0.9, pos.getZ() + 0.9
                    );
                    Color fillColor = new Color(color.getRed(), color.getGreen(), color.getBlue(), 100);
                    Render3DEngine.drawFilledBox(event.getPoseStack(), fillBox, fillColor);
                    RenderUtils.drawLineBox(event.getPoseStack(), box, color);
                }
            }
        }
        if (mode.getValue().equals("Exposed") && isDebugMode()) {
            for (Set<BlockPos> cave : validCaves) {
                for (BlockPos pos : cave) {
                    AABB box = new AABB(
                            pos.getX() + 0.4, pos.getY() + 0.4, pos.getZ() + 0.4,
                            pos.getX() + 0.6, pos.getY() + 0.6, pos.getZ() + 0.6
                    );
                    Render3DEngine.drawFilledBox(event.getPoseStack(), box, new Color(0, 0, 255, 40));
                }
            }
        }
    }

    private Color getColorForBlock(Block block) {
        if (block == Blocks.DIAMOND_ORE || block == Blocks.DEEPSLATE_DIAMOND_ORE) {
            return diamondColor.getValue();
        } else if (block == Blocks.EMERALD_ORE || block == Blocks.DEEPSLATE_EMERALD_ORE) {
            return emeraldColor.getValue();
        } else if (block == Blocks.REDSTONE_ORE || block == Blocks.DEEPSLATE_REDSTONE_ORE) {
            return redstoneColor.getValue();
        } else if (block == Blocks.IRON_ORE || block == Blocks.DEEPSLATE_IRON_ORE) {
            return ironColor.getValue();
        } else if (block == Blocks.GOLD_ORE || block == Blocks.DEEPSLATE_GOLD_ORE) {
            return goldColor.getValue();
        } else if (block == Blocks.COPPER_ORE || block == Blocks.DEEPSLATE_COPPER_ORE) {
            return copperColor.getValue();
        } else if (block == Blocks.LAPIS_ORE || block == Blocks.DEEPSLATE_LAPIS_ORE) {
            return lapisColor.getValue();
        } else if (block == Blocks.COAL_ORE || block == Blocks.DEEPSLATE_COAL_ORE) {
            return coalColor.getValue();
        } else if (RENDER_END_PORTAL_FRAMES.getValue() && block == Blocks.END_PORTAL) {
            return endPortalFrameColor.getValue();
        } else {
            return otherColor.getValue();
        }
    }
    private boolean isDebugMode() {
        return false;
    }


    private boolean isOrePartOfVein(BlockPos initialOrePos, Block oreType, int minVeinSize, int searchRadius) {
        if (minVeinSize <= 1) return true;
        if (mc.level == null) return false;
        Set<BlockPos> veinBlocks = new HashSet<>();
        Queue<BlockPos> queue = new LinkedList<>();
        veinBlocks.add(initialOrePos);
        queue.add(initialOrePos);
        Set<BlockPos> visitedInBFS = new HashSet<>();
        visitedInBFS.add(initialOrePos);

        while (!queue.isEmpty() && veinBlocks.size() < minVeinSize) {
            BlockPos current = queue.poll();
            for (Direction dir : Direction.values()) {
                BlockPos neighbor = current.relative(dir);
                if (neighbor.distManhattan(initialOrePos) > searchRadius) {
                    continue;
                }
                if (visitedInBFS.contains(neighbor)) {
                    continue;
                }
                visitedInBFS.add(neighbor);
                if (mc.level.getBlockState(neighbor).getBlock() == oreType) {
                    veinBlocks.add(neighbor);
                    queue.add(neighbor);
                    if (veinBlocks.size() >= minVeinSize) {
                        return true;
                    }
                }
            }
        }
        return veinBlocks.size() >= minVeinSize;
    }

    private double analyzeOreAuthenticity(BlockPos orePos, Block oreBlock) {
        if (mc.level == null) return 0.0;

        double score = 0.0;
        score += checkOreHeightValidity(orePos, oreBlock);
        score += checkSurroundingBlocks(orePos, oreBlock);
        score += checkVeinComplexity(orePos, oreBlock);
        score += checkNaturalDistribution(orePos);
        score += checkWiderNeighborhoodContext(orePos, oreBlock);

        int exposedFaces = 0;
        for (Direction dir : Direction.values()) {
            if (isAirLike(orePos.relative(dir))) {
                exposedFaces++;
            }
        }
        if (exposedFaces > 0 && exposedFaces <= 3) {
            score += 1.5;
        } else if (exposedFaces == 0) {
            score += 0.5;
        } else if (exposedFaces > 3) {
            score -= 0.5;
        }

        return score;
    }

    private double checkOreHeightValidity(BlockPos pos, Block block) {
        int y = pos.getY();
        boolean isDeepslateVariantBlock = isDeepslateVariant(block);

        double baseScore = 0.5;
        if ((isDeepslateVariantBlock && y < 0) || (!isDeepslateVariantBlock && y >= 0)) {
            baseScore = 1.0;
        } else if ((isDeepslateVariantBlock && y >= 8) || (!isDeepslateVariantBlock && y < -8)) {
            return -2.0;
        }


        if (block == Blocks.DIAMOND_ORE || block == Blocks.DEEPSLATE_DIAMOND_ORE) {
            if (y >= -64 && y <= 16) {
                double score = (isDeepslateVariantBlock && y < 0) || (!isDeepslateVariantBlock && y >= 0) ? 2.0 : 1.0;
                if (y > -60 && y < -50) score += 1.0;
                if (y < -63 || y > 10) score -= 0.5;
                return baseScore + score;
            }
            return 0.0;
        }

        if (block == Blocks.EMERALD_ORE || block == Blocks.DEEPSLATE_EMERALD_ORE) {
            if (y >= -16 && y <= 256) {
                double score = 1.5;
                if (y > 100) score += 1.0;
                return baseScore + score;
            }
            return 0.0;
        }

        if (block == Blocks.REDSTONE_ORE || block == Blocks.DEEPSLATE_REDSTONE_ORE) {
            if (y >= -64 && y <= 16) {
                double score = (isDeepslateVariantBlock && y < 0) || (!isDeepslateVariantBlock && y >= 0) ? 2.0 : 1.0;
                if (y > -60 && y < -40) score += 0.5;
                return baseScore + score;
            }
            return 0.0;
        }

        if (block == Blocks.IRON_ORE || block == Blocks.DEEPSLATE_IRON_ORE) {
            if (y >= -64 && y <= 256) {
                double score = 1.0;
                if (!isDeepslateVariantBlock && y > 80 && y < 200) score += 1.0;
                else if ((isDeepslateVariantBlock && y < 0 && y > -32) || (!isDeepslateVariantBlock && y > 0 && y < 48)) score += 1.0;
                return baseScore + score;
            }
            return 0.0;
        }

        if (block == Blocks.GOLD_ORE || block == Blocks.DEEPSLATE_GOLD_ORE) {
            if (y >= -64 && y <= 32) {
                double score = (isDeepslateVariantBlock && y < 0) || (!isDeepslateVariantBlock && y >= 0) ? 2.0 : 1.0;
                if (y > -48 && y < -16) score += 0.5;
                return baseScore + score;
            } else if (y > 32 && y < 256 && !isDeepslateVariantBlock) {
                return baseScore + 0.5;
            }
            return 0.0;
        }

        if (block == Blocks.COPPER_ORE || block == Blocks.DEEPSLATE_COPPER_ORE) {
            if (y >= -16 && y <= 112) {
                double score = 1.5;
                if ((!isDeepslateVariantBlock && y > 32 && y < 64) || (isDeepslateVariantBlock && y < 0)) score += 1.0;
                return baseScore + score;
            }
            return 0.0;
        }

        if (block == Blocks.LAPIS_ORE || block == Blocks.DEEPSLATE_LAPIS_ORE) {
            if (y >= -64 && y <= 64) {
                double score = (isDeepslateVariantBlock && y < 0) || (!isDeepslateVariantBlock && y >= 0) ? 2.0 : 1.0;
                if (y > -32 && y < 32) score += 1.0;
                return baseScore + score;
            }
            return 0.0;
        }

        if (block == Blocks.COAL_ORE || block == Blocks.DEEPSLATE_COAL_ORE) {
            if (y >= 0 && y <= 256) {
                double score = 1.0;
                if (!isDeepslateVariantBlock && y > 90) score += 0.5;
                return baseScore + score;
            } else if (y >= -64 && y < 0 && isDeepslateVariantBlock) {
                return baseScore + 1.5;
            }
            return 0.0;
        }

        return 1.0;
    }

    private boolean isDeepslateVariant(Block block) {
        return block == Blocks.DEEPSLATE_DIAMOND_ORE ||
                block == Blocks.DEEPSLATE_EMERALD_ORE ||
                block == Blocks.DEEPSLATE_REDSTONE_ORE ||
                block == Blocks.DEEPSLATE_IRON_ORE ||
                block == Blocks.DEEPSLATE_GOLD_ORE ||
                block == Blocks.DEEPSLATE_COPPER_ORE ||
                block == Blocks.DEEPSLATE_LAPIS_ORE ||
                block == Blocks.DEEPSLATE_COAL_ORE;
    }

    private double checkSurroundingBlocks(BlockPos orePos, Block oreBlock) {
        if (mc.level == null) return 0.0D;

        int validSurroundingCount = 0;
        int totalChecked = 0;
        boolean isDeepslateOre = isDeepslateVariant(oreBlock);

        for (Direction dir : Direction.values()) {
            BlockPos adjPos = orePos.relative(dir);
            Block adjBlock = mc.level.getBlockState(adjPos).getBlock();

            if (isAirLike(adjPos)) {
                continue;
            }

            totalChecked++;

            boolean adjIsDeepslateType = adjBlock == Blocks.DEEPSLATE || adjBlock == Blocks.TUFF || isDeepslateVariant(adjBlock);
            boolean adjIsStoneType = adjBlock == Blocks.STONE || adjBlock == Blocks.GRANITE ||
                    adjBlock == Blocks.DIORITE || adjBlock == Blocks.ANDESITE ||
                    isStandardOreBlock(adjBlock) || adjBlock == Blocks.DIRT || adjBlock == Blocks.GRAVEL;

            if (isDeepslateOre) {
                if (adjIsDeepslateType) {
                    validSurroundingCount++;
                }
            } else {
                if (adjIsStoneType) {
                    validSurroundingCount++;
                }
            }
        }

        if (totalChecked == 0) return 1.0;
        return 3.0 * ((double)validSurroundingCount / totalChecked);
    }

    private boolean isStandardOreBlock(Block block) {
        return block == Blocks.DIAMOND_ORE ||
                block == Blocks.EMERALD_ORE ||
                block == Blocks.REDSTONE_ORE ||
                block == Blocks.IRON_ORE ||
                block == Blocks.GOLD_ORE ||
                block == Blocks.COPPER_ORE ||
                block == Blocks.LAPIS_ORE ||
                block == Blocks.COAL_ORE;
    }

    private double checkVeinComplexity(BlockPos initialPos, Block oreBlock) {
        Set<BlockPos> veinBlocks = new HashSet<>();
        findConnectedOres(initialPos, oreBlock, veinBlocks, veinSearchRadius.getValue().intValue());

        if (veinBlocks.size() < 2) return 0.0;
        if (veinBlocks.size() >= 4 && veinBlocks.size() <= 8) return 2.0;
        if (veinBlocks.size() > 8) return 1.5;

        int xMin = Integer.MAX_VALUE, xMax = Integer.MIN_VALUE;
        int yMin = Integer.MAX_VALUE, yMax = Integer.MIN_VALUE;
        int zMin = Integer.MAX_VALUE, zMax = Integer.MIN_VALUE;

        for (BlockPos pos : veinBlocks) {
            xMin = Math.min(xMin, pos.getX());
            xMax = Math.max(xMax, pos.getX());
            yMin = Math.min(yMin, pos.getY());
            yMax = Math.max(yMax, pos.getY());
            zMin = Math.min(zMin, pos.getZ());
            zMax = Math.max(zMax, pos.getZ());
        }

        int xSpan = xMax - xMin + 1;
        int ySpan = yMax - yMin + 1;
        int zSpan = zMax - zMin + 1;

        long boundingBoxVolume = (long)xSpan * ySpan * zSpan;
        double density = (double)veinBlocks.size() / boundingBoxVolume;

        double score = 1.0;

        boolean isLinearOrPlanar = false;
        if (veinBlocks.size() > 2) {
            int dimsGt1 = 0;
            if (xSpan > 1) dimsGt1++;
            if (ySpan > 1) dimsGt1++;
            if (zSpan > 1) dimsGt1++;

            if (dimsGt1 == 1 && veinBlocks.size() > 2) {
                isLinearOrPlanar = true;
                score -= 1.0;
            } else if (dimsGt1 == 2 && veinBlocks.size() > 3) {
                if (xSpan == 1 || ySpan == 1 || zSpan == 1) {
                    isLinearOrPlanar = true;
                    score -= 0.75;
                }
            }

            if (density < 0.3 && boundingBoxVolume > 8) {
                score -= 0.5;
            } else if (density > 0.8 && veinBlocks.size() > 4) {
                score -= 0.5;
            }
        }

        int dimensionsWithSpanGt1 = 0;
        if (xSpan > 1) dimensionsWithSpanGt1++;
        if (ySpan > 1) dimensionsWithSpanGt1++;
        if (zSpan > 1) dimensionsWithSpanGt1++;

        if (dimensionsWithSpanGt1 >= 2) score += 0.5;
        if (dimensionsWithSpanGt1 == 3) score += 0.5;

        return Math.max(-1.0, score);
    }

    private double checkNaturalDistribution(BlockPos pos) {
        if (mc.level == null) return 0.0D;
        int naturalPatternScore = 0;

        Set<Block> adjacentBlockTypes = new HashSet<>();
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                if (dx == 0 && dz == 0) continue;
                BlockPos checkPos = new BlockPos(pos.getX() + dx, pos.getY(), pos.getZ() + dz);
                adjacentBlockTypes.add(mc.level.getBlockState(checkPos).getBlock());
            }
        }

        if (adjacentBlockTypes.size() >= 3) naturalPatternScore += 1;

        Block blockBelow = mc.level.getBlockState(pos.below()).getBlock();
        if (blockBelow == Blocks.STONE || blockBelow == Blocks.DEEPSLATE || isStandardOreBlock(blockBelow) || isDeepslateVariant(blockBelow)) {
            naturalPatternScore += 1;
        }

        return naturalPatternScore;
    }

    private void findConnectedOres(BlockPos start, Block targetBlock, Set<BlockPos> result, int maxRadius) {
        if (result.contains(start) || result.size() > 15) return;

        if (mc.level.getBlockState(start).getBlock() == targetBlock) {
            result.add(start);

            for (Direction dir : Direction.values()) {
                BlockPos nextPos = start.relative(dir);
                if (nextPos.distManhattan(start) <= maxRadius) {
                    findConnectedOres(nextPos, targetBlock, result, maxRadius);
                }
            }
        }
    }

    private void standardScanMultithreaded(BlockPos playerPos, int scanRange) {
        int chunkSizeValue = chunkSize.getValue().intValue();
        int px = playerPos.getX();
        int py = playerPos.getY();
        int pz = playerPos.getZ();

        if (caveSearchExecutor == null || caveSearchExecutor.isShutdown()) {
            initThreadPool();
        }

        if (!isProcessing.compareAndSet(false, true)) {
            return;
        }

        java.util.List<Future<?>> futures = new ArrayList<>();
        try {
            for (int x = px - scanRange; x <= px + scanRange; x += chunkSizeValue) {
                for (int y = Math.max(mc.level.getMinBuildHeight(), py - scanRange);
                     y <= Math.min(mc.level.getMaxBuildHeight(), py + scanRange);
                     y += chunkSizeValue) {

                    for (int z = pz - scanRange; z <= pz + scanRange; z += chunkSizeValue) {
                        final int startX = x;
                        final int startY = y;
                        final int startZ = z;

                        Future<?> future = caveSearchExecutor.submit(() -> {
                            int endX = Math.min(startX + chunkSizeValue, px + scanRange + 1);
                            int endY = Math.min(startY + chunkSizeValue, Math.min(mc.level.getMaxBuildHeight() + 1, py + scanRange + 1));
                            int endZ = Math.min(startZ + chunkSizeValue, pz + scanRange + 1);

                            for (int cx = startX; cx < endX; cx++) {
                                for (int cy = startY; cy < endY; cy++) {
                                    for (int cz = startZ; cz < endZ; cz++) {
                                        BlockPos pos = new BlockPos(cx, cy, cz);
                                        if (mc.level != null) {
                                            try {
                                                Block block = mc.level.getBlockState(pos).getBlock();
                                                if (isTargetBlock(block)) {
                                                    oreBlocks.put(pos, block);
                                                }
                                            } catch (Exception e) {
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        futures.add(future);
                    }
                }
            }

            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.err.println("XRay standard scan task was interrupted: " + e.getMessage());
                    break;
                } catch (ExecutionException e) {
                    System.err.println("XRay standard scan task failed: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()));
                    if (e.getCause() != null) {
                        e.getCause().printStackTrace(System.err);
                    }
                } catch (Exception e) {
                    System.err.println("XRay standard scan future.get() error: " + e.getMessage());
                    e.printStackTrace(System.err);
                }
            }
        } finally {
            isProcessing.set(false);
        }
    }

    private void exposedScanMultithreaded(BlockPos playerPos, int scanRange) {
        int px = playerPos.getX();
        int py = playerPos.getY();
        int pz = playerPos.getZ();

        if (caveSearchExecutor == null || caveSearchExecutor.isShutdown()) {
            initThreadPool();
        }

        if (!isProcessing.compareAndSet(false, true)) {
            return;
        }

        int chunkSizeValue = chunkSize.getValue().intValue();
        Set<BlockPos> globalVisitedThisScan = ConcurrentHashMap.newKeySet();

        java.util.List<Future<?>> scanFutures = new ArrayList<>();

        try {
            for (int xChunk = px - scanRange; xChunk <= px + scanRange; xChunk += chunkSizeValue) {
                for (int yChunk = Math.max(mc.level.getMinBuildHeight(), py - scanRange);
                     yChunk <= Math.min(mc.level.getMaxBuildHeight(), py + scanRange);
                     yChunk += chunkSizeValue) {
                    for (int zChunk = pz - scanRange; zChunk <= pz + scanRange; zChunk += chunkSizeValue) {
                        final int startX = xChunk;
                        final int startY = yChunk;
                        final int startZ = zChunk;

                        Future<?> future = caveSearchExecutor.submit(() -> {
                            int endX = Math.min(startX + chunkSizeValue, px + scanRange + 1);
                            int endY = Math.min(startY + chunkSizeValue, Math.min(mc.level.getMaxBuildHeight() + 1, py + scanRange + 1));
                            int endZ = Math.min(startZ + chunkSizeValue, pz + scanRange + 1);

                            for (int cx = startX; cx < endX; cx++) {
                                for (int cy = startY; cy < endY; cy++) {
                                    for (int cz = startZ; cz < endZ; cz++) {
                                        if (Thread.currentThread().isInterrupted()) {
                                            return;
                                        }

                                        BlockPos currentPos = new BlockPos(cx, cy, cz);

                                        if (isAirLike(currentPos)) {
                                            if (globalVisitedThisScan.add(currentPos)) {
                                                Set<BlockPos> caveBlocks = floodFillCaveBFS(currentPos, globalVisitedThisScan, scanRange, playerPos);

                                                if (caveBlocks.size() >= caveMinSize.getValue().intValue()) {
                                                    if (RENDER_END_PORTAL_FRAMES.getValue()) {
                                                        for (BlockPos caveBlock : caveBlocks) {
                                                            if (Thread.currentThread().isInterrupted()) return;
                                                            for (Direction dir : Direction.values()) {
                                                                BlockPos adjacentPos = caveBlock.relative(dir);
                                                                try {
                                                                    if (mc.level == null) return;
                                                                    Block block = mc.level.getBlockState(adjacentPos).getBlock();
                                                                    if (block == Blocks.END_PORTAL) {
                                                                        oreBlocks.put(adjacentPos, block);
                                                                    }
                                                                } catch (Exception e) { /* Ignored */ }
                                                            }
                                                        }
                                                    }

                                                    if (validateNaturalCave(caveBlocks)) {
                                                        synchronized (validCaves) {
                                                            validCaves.add(caveBlocks);
                                                        }

                                                        for (BlockPos caveBlock : caveBlocks) {
                                                            if (Thread.currentThread().isInterrupted()) {
                                                                return;
                                                            }
                                                            for (Direction dir : Direction.values()) {
                                                                BlockPos adjacentPos = caveBlock.relative(dir);
                                                                try {
                                                                    if (mc.level == null) return;
                                                                    Block block = mc.level.getBlockState(adjacentPos).getBlock();

                                                                    if (isTargetBlock(block)) {
                                                                        double authenticityScore = analyzeOreAuthenticity(adjacentPos, block);
                                                                        if (authenticityScore >= minOreAuthenticityScore.getValue().doubleValue() &&
                                                                                isOrePartOfVein(adjacentPos, block,
                                                                                        minimumVeinSize.getValue().intValue(),
                                                                                        veinSearchRadius.getValue().intValue())) {
                                                                            oreBlocks.put(adjacentPos, block);
                                                                        }
                                                                    }
                                                                } catch (Exception e) {
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });
                        scanFutures.add(future);
                    }
                }
            }

            for (Future<?> future : scanFutures) {
                try {
                    future.get();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.err.println("XRay exposed scan task was interrupted: " + e.getMessage());
                    if (future != null && !future.isDone()) future.cancel(true);
                    break;
                } catch (ExecutionException e) {
                    System.err.println("XRay exposed scan task failed: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()));
                    if (e.getCause() != null) {
                        e.getCause().printStackTrace(System.err);
                    }
                    if (future != null && !future.isDone()) future.cancel(true);
                } catch (Exception e) {
                    System.err.println("XRay exposed scan future.get() error: " + e.getMessage());
                    e.printStackTrace(System.err);
                    if (future != null && !future.isDone()) future.cancel(true);
                }
            }
        } finally {
            isProcessing.set(false);
        }
    }

    private void processFrameNotifications() {
        if (!logEndPortalFramesSetting.getValue() || mc.player == null || mc.level == null) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime - lastLoggedFrameTime < LOG_FRAME_INTERVAL) {
            return;
        }

        for (Map.Entry<BlockPos, Block> entry : oreBlocks.entrySet()) {
            BlockPos portalPos = entry.getKey();
            Block block = entry.getValue();

            if (block == Blocks.END_PORTAL) {
                String portalMessage = String.format(
                        "末地传送门 (折跃门) 坐标 X:%d, Y:%d, Z:%d",
                        portalPos.getX(), portalPos.getY(), portalPos.getZ());
                ChatUtils.displayAlert(portalMessage);
                lastLoggedFrameTime = currentTime;
                return;
            }
        }
    }

    private void processMineshaftNotifications() {
        if (!detectMineshafts.getValue() || !logMineshaftCoordinates.getValue() || mc.player == null || mc.level == null || validCaves.isEmpty()) {
            return;
        }

        synchronized (validCaves) {
            for (Set<BlockPos> caveAirBlocks : validCaves) {
                if (mc.level == null || caveAirBlocks.isEmpty() || caveAirBlocks.size() < 10) continue;

                BlockPos representativePoint = caveAirBlocks.iterator().next();
                long currentTime = System.currentTimeMillis();

                int featureCount = 0;
                BlockPos firstFeaturePos = null;
                Set<BlockPos> checkedSolidBlocksInCave = new HashSet<>();

                for (BlockPos airPos : caveAirBlocks) {
                    if (Thread.currentThread().isInterrupted()) return;

                    for (Direction dir : Direction.values()) {
                        BlockPos solidCandidatePos = airPos.relative(dir);
                        if (caveAirBlocks.contains(solidCandidatePos) || !checkedSolidBlocksInCave.add(solidCandidatePos)) {
                            continue;
                        }
                        BlockState blockState = mc.level.getBlockState(solidCandidatePos);
                        Block block = blockState.getBlock();
                        if (isMineshaftStructuralBlock(block)) {
                            featureCount++;
                            if (firstFeaturePos == null) firstFeaturePos = solidCandidatePos;

                            if (block == Blocks.OAK_FENCE) {
                                if (mc.level.getBlockState(solidCandidatePos.above()).getBlock() == Blocks.OAK_FENCE ||
                                        mc.level.getBlockState(solidCandidatePos.below()).getBlock() == Blocks.OAK_FENCE) {
                                    featureCount += 2;
                                }
                            } else if (block == Blocks.RAIL) {
                                featureCount++;
                            }
                        }
                        if (featureCount >= MINESHAFT_MIN_FEATURES) break;
                    }
                    if (featureCount >= MINESHAFT_MIN_FEATURES) break;
                }

                if (featureCount >= MINESHAFT_MIN_FEATURES && firstFeaturePos != null) {
                    final BlockPos finalFirstFeaturePos = firstFeaturePos;
                    boolean logThisTime = false;

                    if (detectedMineshaftLocations.add(finalFirstFeaturePos)) {
                        logThisTime = true;
                        while (detectedMineshaftLocations.size() > 20) {
                            if (!detectedMineshaftLocations.isEmpty()) {
                                detectedMineshaftLocations.remove(detectedMineshaftLocations.iterator().next());
                            } else {
                                break;
                            }
                        }
                    } else {
                        if (currentTime - lastLoggedMineshaftTime > MINESHAFT_LOG_INTERVAL) {
                            logThisTime = true;
                        }
                    }

                    if (logThisTime) {
                        String message = String.format("探测到疑似废弃矿井结构，坐标: X:%d, Y:%d, Z:%d",
                                finalFirstFeaturePos.getX(), finalFirstFeaturePos.getY(), finalFirstFeaturePos.getZ());
                        ChatUtils.displayAlert(message);
                        BlockPos playerPos = mc.player.blockPosition();
                        String structureInfo = String.format("在以 (%d,%d,%d) 周围的洞穴 (大小: %d) 发现 %d 个矿井特征。玩家位置 (%d,%d,%d)",
                                representativePoint.getX(), representativePoint.getY(), representativePoint.getZ(), caveAirBlocks.size(), featureCount,
                                playerPos.getX(), playerPos.getY(), playerPos.getZ());
                        ChatUtils.displayAlert(structureInfo);
                        lastLoggedMineshaftTime = currentTime;
                    }
                }
            }
        }
    }

    private boolean isMineshaftStructuralBlock(Block block) {
        return block == Blocks.OAK_FENCE ||
                block == Blocks.OAK_PLANKS ||
                block == Blocks.OAK_LOG ||
                block == Blocks.RAIL ||
                block == Blocks.COBWEB ||
                block == Blocks.TORCH ||
                block == Blocks.CHEST;
    }

    private double checkWiderNeighborhoodContext(BlockPos orePos, Block oreBlock) {
        if (mc.level == null) return 0.0;
        double score = 0.0;
        int stoneCount = 0;
        int airCount = 0;
        int otherOreCount = 0;
        int suspiciousPlacement = 0;
        boolean isDeepslateLevel = orePos.getY() < 0;

        for (int dx = -1; dx <= 1; dx++) {
            for (int dy = -1; dy <= 1; dy++) {
                for (int dz = -1; dz <= 1; dz++) {
                    if (dx == 0 && dy == 0 && dz == 0) continue;

                    BlockPos checkPos = orePos.offset(dx, dy, dz);
                    BlockState blockState = mc.level.getBlockState(checkPos);
                    Block block = blockState.getBlock();

                    if (block == Blocks.STONE || block == Blocks.DEEPSLATE || block == Blocks.TUFF || block == Blocks.GRAVEL || block == Blocks.DIRT) {
                        stoneCount++;
                    } else if (isAirLike(checkPos)) {
                        airCount++;
                    } else if (isTargetBlock(block) && block != oreBlock) {
                        otherOreCount++;
                    } else if (block == Blocks.BEDROCK || block == Blocks.BARRIER) {
                        suspiciousPlacement += 2;
                    } else if (blockState.isSolidRender(mc.level, checkPos)) {

                    } else {

                    }
                }
            }
        }

        if (stoneCount > 15) score += 1.5;
        else if (stoneCount > 10) score += 1.0;
        else if (stoneCount > 5) score += 0.5;
        else score -= 0.5;

        if (airCount > 10) {
            score -= 1.0;
            if (isDeepslateLevel && !isDeepslateVariant(oreBlock)) score -= 0.5;
        } else if (airCount > 5) {
            score -= 0.5;
        }

        if (otherOreCount > 0) score += 0.5 * otherOreCount;

        if (suspiciousPlacement > 0) score -= suspiciousPlacement;

        int strataCorrectCount = 0;
        int strataChecked = 0;
        for (Direction dir : Direction.values()) {
            BlockPos adjacent = orePos.relative(dir);
            Block adjBlock = mc.level.getBlockState(adjacent).getBlock();
            if (adjBlock == Blocks.STONE || adjBlock == Blocks.GRANITE || adjBlock == Blocks.DIORITE || adjBlock == Blocks.ANDESITE) {
                strataChecked++;
                if (!isDeepslateVariant(oreBlock)) strataCorrectCount++;
            } else if (adjBlock == Blocks.DEEPSLATE || adjBlock == Blocks.TUFF) {
                strataChecked++;
                if (isDeepslateVariant(oreBlock)) strataCorrectCount++;
            }
        }
        if (strataChecked > 0) {
            score += (double)strataCorrectCount / strataChecked * 1.0;
        }

        return score;
    }
}

