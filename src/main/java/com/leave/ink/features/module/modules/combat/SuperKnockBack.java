package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventAttack;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.network.protocol.game.ServerboundPlayerCommandPacket;
import net.minecraft.world.entity.LivingEntity;

import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "SuperKnockBack", language = Language.English),
        @Text(label = "超级击退", language = Language.Chinese)
}, category = Category.Combat)
public class SuperKnockBack extends Module {
//    @SettingInfo(name = {
//            @Text(label = "KnockBack", language = Language.English),
//            @Text(label = "击退", language = Language.Chinese)
//    })
//    private final ModeSetting modeSetting = new ModeSetting("");
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delayValue = new NumberSetting(0D, 0D, 500D, "#");
    @SettingInfo(name = {
            @Text(label = "HurtTime", language = Language.English),
            @Text(label = "伤害时间", language = Language.Chinese)
    })
    private final NumberSetting hurtTimeValue = new NumberSetting(10D, 0D, 10D, "#");
    @SettingInfo(name = {
            @Text(label = "OnlyMove", language = Language.English),
            @Text(label = "只在移动时", language = Language.Chinese)
    })
    private final BooleanSetting onlyMove = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "OnlyGround", language = Language.English),
            @Text(label = "只在地面", language = Language.Chinese)
    })
    private final BooleanSetting onlyGround = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Grim", language = Language.English),
            @Text(label = "绕过GrimAC", language = Language.Chinese)
    })
    private final BooleanSetting grim = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Legit", Arrays.asList("Legit", "Normal"));
    private final TimeUtils timeUtils = new TimeUtils();

    public SuperKnockBack() {
        registerSetting(mode,delayValue, hurtTimeValue, onlyMove, onlyGround, grim);
    }

    private boolean worldChange = true;
    private boolean sprinting = false;
    private LivingEntity entity;

    @Override
    public void onEnable() {
        timeUtils.reset();
    }

    @Override
    public void onDisable() {
        worldChange = true;
        Main.INSTANCE.sprintManager.shouldSprint = true;
    }
    @EventTarget
    public void onUpdate(EventUpdate eventUpdate) {
        KillAura aura = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if (aura.target == null || entity == null) Main.INSTANCE.sprintManager.shouldSprint = true;
    }
    @EventTarget
    public void onAttack2(EventAttack event) {
        if(!mode.getValue().equals("Legit")) return;
        if (onlyMove.getValue() && !isMoving())
            return;
        if (onlyGround.getValue() && !mc.player.onGround())
            return;
        if (event.getTargetEntity() instanceof LivingEntity)
            entity = (LivingEntity) event.getTargetEntity();
        KillAura aura = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if (aura.target == null || entity == null) Main.INSTANCE.sprintManager.shouldSprint = true;
        boolean a = Boolean.TRUE.equals(ObfuscationReflectionHelper.getPrivateValue(LocalPlayer.class, mc.player, "wasSprinting"));
        if (mc.player.zza > 0 && a == mc.player.isSprinting())
            Main.INSTANCE.sprintManager.shouldSprint = !a;
    }
    @EventTarget
    public void onAttack(EventAttack event) {
        if (mc.player == null || mc.level == null)
            return;
        if(!mode.getValue().equals("Normal")) return;
        try {
            if (event.getTargetEntity() instanceof LivingEntity)
                entity = (LivingEntity) event.getTargetEntity();

            if (entity != null) {
                if (!Utils.isValidEntity(entity))
                    return;

                if (!mc.player.isSprinting() && grim.getValue())
                    return;

                if (Targets.isTeam(entity))
                    return;

                if (entity.hurtTime >= hurtTimeValue.getValue().floatValue() || !timeUtils.hasTimeElapsed(delayValue.getValue().longValue()))
                    return;

                if (onlyMove.getValue() && !isMoving())
                    return;

                if (onlyGround.getValue() && !mc.player.onGround())
                    return;

                if (mc.player.isSprinting()) {
                    mc.player.setSprinting(true);
                }

                mc.getConnection().send(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.STOP_SPRINTING));
                mc.getConnection().send(new ServerboundPlayerCommandPacket(mc.player, ServerboundPlayerCommandPacket.Action.START_SPRINTING));
                ObfuscationReflectionHelper.setPrivateValue(LocalPlayer.class, mc.player, true, "wasSprinting");
                timeUtils.reset();
                entity = null;
            }
        } catch (Exception a) {
            entity = null;
        }
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        worldChange = true;
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if(!mode.getValue().equals("Normal")) return;

        if (grim.getValue()) {
            if (entity != null) {
                if (event.getPacket() instanceof ServerboundPlayerCommandPacket) {
                    final ServerboundPlayerCommandPacket wrapper = (ServerboundPlayerCommandPacket) event.getPacket();

                    if (wrapper.getAction() != ServerboundPlayerCommandPacket.Action.STOP_SPRINTING && wrapper.getAction() != ServerboundPlayerCommandPacket.Action.START_SPRINTING)
                        return;

                    if (worldChange) {
                        sprinting = wrapper.getAction() == ServerboundPlayerCommandPacket.Action.START_SPRINTING;
                        worldChange = false;
                        return;
                    }

                    if (wrapper.getAction() == ServerboundPlayerCommandPacket.Action.START_SPRINTING) {
                        if (sprinting)
                            event.setCancelled(true);
                        else
                            sprinting = true;
                    } else {
                        if (!sprinting)
                            event.setCancelled(true);
                        else
                            sprinting = false;
                    }
                }
            }
        }
    }

    private boolean isMoving() {
        return mc.player != null && (mc.player.input.forwardImpulse != 0F || mc.player.input.leftImpulse != 0F);
    }
}
