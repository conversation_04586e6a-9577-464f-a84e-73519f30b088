package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPlayerTick;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import net.minecraft.client.multiplayer.MultiPlayerGameMode;

@ModuleInfo(name = {
        @Text(label = "FastBreak", language = Language.English),
        @Text(label = "快速破坏", language = Language.Chinese)
}, category = Category.World)
public class FastBreak extends Module {
    @SettingInfo(name = {
            @Text(label = "BreakSpeed", language = Language.English),
            @Text(label = "破坏速度", language = Language.Chinese)
    })
    private final NumberSetting breakDamage = new NumberSetting(0.8f, 0.1f, 1.0f, "#.0");

    public FastBreak() {
        registerSetting(breakDamage);
    }

    @EventTarget
    public void onUpdate(EventPlayerTick event) {
        if (mc.gameMode == null)
            return;

        float destroyProgress = ObfuscationReflectionHelper.getPrivateValue(MultiPlayerGameMode.class, mc.gameMode, "destroyProgress");
        ObfuscationReflectionHelper.setPrivateValue(MultiPlayerGameMode.class, mc.gameMode, 0, "destroyDelay");

        if (destroyProgress > breakDamage.getValue().floatValue())
            ObfuscationReflectionHelper.setPrivateValue(MultiPlayerGameMode.class, mc.gameMode, 1f, "destroyProgress");
    }
}
