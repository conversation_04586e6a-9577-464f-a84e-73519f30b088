package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import net.minecraft.client.KeyMapping;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.Blocks;
import org.lwjgl.glfw.GLFW;
@ModuleInfo(name = {
        @Text(label = "Eagle", language = Language.English),
        @Text(label = "自动蹲搭", language = Language.Chinese)
}, category = Category.Movement)
public class Eagle extends Module {
    @SettingInfo(name = {
            @Text(label = "Test2", language = Language.English),
            @Text(label = "Test2", language = Language.Chinese)
    })
    private final BooleanSetting test2 = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Test1", language = Language.English),
            @Text(label = "Test1", language = Language.Chinese)
    })
    private final BooleanSetting test1 = new BooleanSetting(true, new SettingAttribute<>(test2, true));
    @SettingInfo(name = {
            @Text(label = "Test111", language = Language.English),
            @Text(label = "Test111", language = Language.Chinese)
    })
    private final BooleanSetting test111 = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "ForwardClose", language = Language.English),
            @Text(label = "前进时关闭", language = Language.Chinese)
    })
    private final BooleanSetting forward = new BooleanSetting(true,
            new SettingAttribute<>(test1, true),
            new SettingAttribute<>(test111, true)
    );

    public Eagle() {
        registerSetting(forward);
    }
    @EventTarget
    public void onUpdate(EventUpdate update) {

        if (!mc.player.onGround())
            return;

        if (GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyShift.getKey().getValue()) == GLFW.GLFW_PRESS)
            return;

        if (forward.getValue()) {
            if (GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyUp.getKey().getValue()) == GLFW.GLFW_PRESS && mc.player.zza != 0) {
                setPressed(false);
                return;
            }
        }
        if (GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) != GLFW.GLFW_PRESS) {
            BlockPos pos = BlockPos.containing(mc.player.getX(), mc.player.getY() - 1.0, mc.player.getZ());
            setPressed(mc.level.getBlockState(pos).getBlock().equals(Blocks.AIR));
        }
    }

    @Override
    public void onDisable() {
        setPressed(false);
    }

    public void setPressed(boolean state) {
        KeyMapping.set(mc.options.keyShift.getKey(), state);
    }
}
