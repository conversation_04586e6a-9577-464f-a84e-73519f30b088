package com.leave.ink.features.module.modules.other;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ServerboundKeepAlivePacket;


import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@ModuleInfo(name = {
        @Text(label = "PingSpoof", language = Language.English),
        @Text(label = "延迟欺骗", language = Language.Chinese)
}, category = Category.Other)
public class PingSpoof extends Module {
    final Map<Packet<?>, Long> packets = new ConcurrentHashMap<>();
    @SettingInfo(name = {
            @Text(label = "MinDelay", language = Language.English),
            @Text(label = "最小延迟", language = Language.Chinese)
    })
    private final NumberSetting minDelay  = new NumberSetting(500, 0, 25000, "#");
    @SettingInfo(name = {
            @Text(label = "MaxDelay", language = Language.English),
            @Text(label = "最大延迟", language = Language.Chinese)
    })
    private final NumberSetting maxDelay  = new NumberSetting(1000.0, 0, 25000, "#");

    public PingSpoof() {
        registerSetting(minDelay, maxDelay);
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if(event.getPacketType() == EventPacket.PacketType.Client) {
            if(event.getPacket() instanceof ServerboundKeepAlivePacket packet) {
                event.setCancelled(true);
                packets.put(packet, System.currentTimeMillis() + random(minDelay.getValue().intValue() , maxDelay.getValue().intValue()));
            }
        }
    }
    @EventTarget
    public void onTickEvent(EventUpdate event) {
        if(Utils.isNull()) return;
        packets.forEach(
                (packet, time) -> {
                    if (time <= System.currentTimeMillis()) {
                        Objects.requireNonNull(mc.getConnection()).send(packet);
                        packets.remove(packet);
                    }
                }
        );
    }
    private int random(final float min, final float max){
        if(min > max || max == min) return 0;
        return (int) (min + (max - min) + Math.random() * (max - min + 1));
    }

}
