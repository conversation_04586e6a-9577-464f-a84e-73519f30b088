package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.utils.Utils;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;

@ModuleInfo(name = {
        @Text(label = "FreeCam", language = Language.English),
        @Text(label = "灵魂出窍", language = Language.Chinese)
}, category = Category.Render)
public class FreeCam extends Module {
    @SettingInfo(name = {
            @Text(label = "Speed", language = Language.English),
            @Text(label = "速度", language = Language.Chinese)
    })
    private final NumberSetting speedValue = new NumberSetting(0.8F, 0.1F, 2F, "#.00");
    @SettingInfo(name = {
            @Text(label = "Fly", language = Language.English),
            @Text(label = "飞行", language = Language.Chinese)
    })
    private final BooleanSetting flyValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Clip", language = Language.English),
            @Text(label = "穿墙", language = Language.Chinese)
    })
    private final BooleanSetting clipValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "NoAction", language = Language.English),
            @Text(label = "无行为", language = Language.Chinese)
    })
    private final BooleanSetting noActionValue = new BooleanSetting(true);

    public FreeCam() {
        registerSetting(speedValue, flyValue, clipValue, noActionValue);
    }

    private double oldX;
    private double oldY;
    private double oldZ;

    @Override
    public void onEnable() {
        if (mc.player == null)
            return;

        oldX = mc.player.getX();
        oldY = mc.player.getY();
        oldZ = mc.player.getZ();

        if (clipValue.getValue())
            mc.player.noPhysics = true;
    }

    @Override
    public void onDisable() {
        if (mc.player == null)
            return;

        mc.player.moveTo(oldX, oldY, oldZ, mc.player.getYRot(), mc.player.getXRot());
        mc.player.noPhysics = false;
        mc.player.setDeltaMovement(0, 0, 0);
        PacketUtils.sendPacketNoEvent(new ServerboundMovePlayerPacket.PosRot(oldX, oldY, oldZ, mc.player.getYRot(), mc.player.getXRot(), mc.player.onGround()));
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (Utils.isNull())
            return;

        if (clipValue.getValue())
            mc.player.noPhysics = true;

        mc.player.fallDistance = 0;

        if (flyValue.getValue()) {
            var speed = speedValue.getValue().floatValue();

            mc.player.setDeltaMovement(0, 0, 0);
            if (mc.options.keyJump.isDown())
                mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, speed, mc.player.getDeltaMovement().z);
            if (mc.options.keyShift.isDown())
                mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, -speed, mc.player.getDeltaMovement().z);

            MovementUtils.strafe(speed);

            mc.player.isSpectator();
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        final Packet<?> packet = event.getPacket();

        if (packet instanceof ServerboundPongPacket
                || packet instanceof ServerboundMovePlayerPacket
                || packet instanceof ServerboundMovePlayerPacket.PosRot
                || packet instanceof ServerboundMovePlayerPacket.Pos
                || packet instanceof ServerboundMovePlayerPacket.Rot
                || packet instanceof ServerboundMovePlayerPacket.StatusOnly
                || packet instanceof ServerboundKeepAlivePacket
                || (noActionValue.getValue() && (packet instanceof ServerboundPlayerActionPacket || packet instanceof ServerboundPlayerCommandPacket)))
            event.setCancelled(true);
    }
}
