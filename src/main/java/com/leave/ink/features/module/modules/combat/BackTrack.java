package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.timer.TimeUtils;
import lombok.Getter;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import java.awt.*;
import java.util.LinkedHashSet;

@ModuleInfo(name = {
        @Text(label = "BackTrack", language = Language.English),
        @Text(label = "回溯", language = Language.Chinese)
}, category = Category.Combat)
public class BackTrack extends Module {

    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "范围", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(0.5, 0.0, 10.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delay = new NumberSetting(150.0, 0.0, 5000.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "NextBacktrackDelay", language = Language.English),
            @Text(label = "下一次回溯延迟", language = Language.Chinese)
    })
    private final NumberSetting nextBacktrackDelay = new NumberSetting(0.0, 0.0, 2000.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "Chance", language = Language.English),
            @Text(label = "几率", language = Language.Chinese)
    })
    private final NumberSetting chance = new NumberSetting(35.0, 0.0, 100.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "PauseOnHurtTime", language = Language.English),
            @Text(label = "受伤时暂停", language = Language.Chinese)
    })
    private final BooleanSetting pauseOnHurtTime = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "HurtTime", language = Language.English),
            @Text(label = "受伤时间", language = Language.Chinese)
    })
    private final NumberSetting hurtTimeValue = new NumberSetting(2.0, 0.0, 10.0, "#.0");

    @SettingInfo(name = {
            @Text(label = "LastAttackTimeToWork", language = Language.English),
            @Text(label = "上次攻击到工作的时间", language = Language.Chinese)
    })
    private final NumberSetting lastAttackTimeToWork = new NumberSetting(0.0, 0.0, 5000.0, "#.0");

    private final TimeUtils chronometer = new TimeUtils();
    private final TimeUtils trackingBufferChronometer = new TimeUtils();
    private final TimeUtils attackChronometer = new TimeUtils();
    private final TimeUtils clearTimer = new TimeUtils();

    private Entity target;
    private TrackedPosition position;
    private final LinkedHashSet<PacketEntry> packetQueue = new LinkedHashSet<>();
    private boolean shouldPause = false;

    private volatile boolean clearing = false;

    public BackTrack() {
        registerSetting(range, delay, nextBacktrackDelay, chance, pauseOnHurtTime, hurtTimeValue, lastAttackTimeToWork);
        chronometer.reset();
        trackingBufferChronometer.reset();
        attackChronometer.reset();
    }

    @EventTarget
    public void onPacket(EventHandleReceivePacket event) {
        if (!isEnable() || Utils.isNull())
            return;

        if (!packetQueue.isEmpty()) {
            chronometer.waitForAtLeast((long) (Math.random() * nextBacktrackDelay.getValue().longValue()));
        }

        synchronized (packetQueue) {
            if (event.isCancelled() || event.getPacket() == null) return;

            if (packetQueue.isEmpty() && !shouldCancelPackets()) return;

            Packet<?> packet = event.getPacket();

            if (packet instanceof ServerboundChatPacket ||
                    packet instanceof ClientboundSystemChatPacket ||
                    packet instanceof ServerboundCommandSuggestionPacket) {
                return;
            }

            if (packet instanceof ClientboundPlayerPositionPacket ||
                    packet instanceof ClientboundDisconnectPacket) {
                clear(true);
                return;
            }

            if (packet instanceof ClientboundSoundPacket soundPacket) {
                if (soundPacket.getSound().get() == SoundEvents.PLAYER_HURT) {
                    return;
                }
            }

            if (packet instanceof ClientboundSetHealthPacket healthPacket) {
                if (healthPacket.getHealth() <= 0) {
                    clear(true);
                    return;
                }
            }

            if (target == null || mc.level == null) return;

            boolean isEntityPacket = packet instanceof ClientboundMoveEntityPacket &&
                    ((ClientboundMoveEntityPacket) packet).getEntity(mc.level) == target;
            boolean isPositionPacket = packet instanceof ClientboundTeleportEntityPacket &&
                    ((ClientboundTeleportEntityPacket) packet).getId() == target.getId();

            if ((isEntityPacket || isPositionPacket) && position != null) {
                Vec3 pos;
                if (packet instanceof ClientboundMoveEntityPacket c03) {
                    double dx = c03.getXa() / 4096.0;
                    double dy = c03.getYa() / 4096.0;
                    double dz = c03.getZa() / 4096.0;

                    pos = position.getPos() != null ? new Vec3(
                            position.getPos().x + dx,
                            position.getPos().y + dy,
                            position.getPos().z + dz
                    ) : null;

                    if (pos != null && (Math.abs(dx) > 5 || Math.abs(dy) > 5 || Math.abs(dz) > 5)) {
                        return;
                    }

                } else {
                    ClientboundTeleportEntityPacket p = (ClientboundTeleportEntityPacket) packet;
                    pos = new Vec3(p.getX(), p.getY(), p.getZ());
                }

                if (position != null && pos != null) {
                    double moveDistance = position.getPos().distanceTo(pos);
                    if (moveDistance > 5.0) {
                        return;
                    }

                    position.setPos(pos);
                }
            }

            event.setCancelled(true);
            packetQueue.add(new PacketEntry(packet));
        }
    }


    @EventTarget
    public void onMotion(EventTick event) {
        if (Utils.isNull())
            return;

        if (target != null && position != null) {
            position.setAttacking(false);
            position.updateActualPos(target.position());
        }
    }

    public void processTarget(Entity entity) {
        if (entity == null || !entity.isAlive() || entity.isRemoved() ||
                (entity instanceof LivingEntity && ((LivingEntity)entity).getHealth() <= 0)) {
            if(target != null && target.getId() == entity.getId()) {
                clear();
            }
            return;
        }

        double actualDistance = entity.position().distanceTo(mc.player.position());
        if (actualDistance > 5.5) {
            if(target != null) {
                clear();
            }
            return;
        }

        boolean isSameTarget = target != null && entity.getId() == target.getId();

        if (isSameTarget) {
            shouldPause = entity instanceof LivingEntity &&
                    ((LivingEntity) entity).hurtTime >= hurtTimeValue.getValue().intValue();

            if (position != null) {
                position.updateActualPos(entity.position());

                actualDistance = position.getActualPos().distanceTo(mc.player.position());
                if (actualDistance > 5.5) {
                    clear();
                    return;
                }
            }

            if (!shouldBacktrack(entity)) {
                clear();
                return;
            }

            if (position == null || needsPositionUpdate(entity)) {
                if (position == null) {
                    position = new TrackedPosition();
                }
                updatePosition(entity);
            }
            return;
        }

        if (!shouldBacktrack(entity)) {
            return;
        }

        clear();
        position = new TrackedPosition();
        updatePosition(entity);
        position.updateActualPos(entity.position());
        target = entity;
    }


    private boolean needsPositionUpdate(Entity entity) {
        if (position == null || position.getPos() == null) return true;

        double distance = entity.position().distanceTo(position.getPos());
        return distance > 3.0;
    }

    private void updatePosition(Entity entity) {
        if (position != null) {
            double distance = entity.distanceTo(mc.player);
            if (distance > 6.0) {
                clear();
                return;
            }
            position.setPos(entity.position());
        }
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (target != null && position != null) {
            AABB backtrackBox = new AABB(
                    position.getPos().x - 0.4,
                    position.getPos().y,
                    position.getPos().z - 0.4,
                    position.getPos().x + 0.4,
                    position.getPos().y + target.getBbHeight(),
                    position.getPos().z + 0.4
            );
            RenderUtils.drawLineBox(event.getPoseStack(), backtrackBox, Color.RED);

            if (position.getActualPos() != null) {
                AABB actualBox = new AABB(
                        position.getActualPos().x - 0.4,
                        position.getActualPos().y,
                        position.getActualPos().z - 0.4,
                        position.getActualPos().x + 0.4,
                        position.getActualPos().y + target.getBbHeight(),
                        position.getActualPos().z + 0.4
                );
                RenderUtils.drawLineBox(event.getPoseStack(), actualBox, Color.GREEN);
            }
        }
    }

    @Override
    public void onEnable() {
        chronometer.reset();
        clearTimer.reset();
        clear(false);
    }

    @Override
    public void onDisable() {
        clear(true);
    }

    private void processPackets(boolean clear) {
        synchronized (packetQueue) {
            long currentTime = System.currentTimeMillis();
            if (clear || packetQueue.stream()
                    .anyMatch(packet -> (currentTime - packet.timestamp) >= delay.getValue().longValue())) {
                packetQueue.forEach(packet -> {
                    if(mc.getConnection() != null) {
                        try {
                            @SuppressWarnings("unchecked")
                            Packet<ClientGamePacketListener> clientPacket = (Packet<ClientGamePacketListener>) packet.packet;
                                clientPacket.handle(mc.getConnection());
                        } catch (Exception ignored) {
                        }
                    }
                });
                packetQueue.clear();
                target = null;
                position = null;
            }
        }
    }

    public void clear(boolean handlePackets, boolean clearOnly) {
        if (clearing) return;
        clearing = true;

        try {
            if (clearOnly) {
                synchronized (packetQueue) {
                    packetQueue.clear();
                }
            } else {
                synchronized (packetQueue) {
                    if (handlePackets && mc.getConnection() != null) {
                        packetQueue.forEach(packet -> {
                            try {
                                @SuppressWarnings("unchecked")
                                Packet<ClientGamePacketListener> clientPacket =
                                        (Packet<ClientGamePacketListener>) packet.packet;
                                mc.tell(() -> {
                                    try {
                                        clientPacket.handle(mc.getConnection());
                                    } catch (Exception ignored) {
                                    }
                                });
                            } catch (Exception ignored) {
                            }
                        });
                    }
                    packetQueue.clear();
                }
            }
        } finally {
            target = null;
            position = null;
            clearing = false;
        }
    }

    private void clear() {
        clear(true, false);
    }

    private void clear(boolean handlePackets) {
        clear(handlePackets, false);
    }

    private boolean shouldBacktrack(Entity target) {
        var killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");

        if (target == null || !target.isAlive() || target.isRemoved()) {
            return false;
        }

        if (target instanceof LivingEntity living && living.getHealth() < 3.0F) {
            return false;
        }

        Vec3 backtrackPos = position != null ? position.getPos() : target.position();
        if (backtrackPos == null) return false;

        double backtrackDistance = backtrackPos.distanceTo(mc.player.position());

        if (backtrackDistance > 6.0 || backtrackDistance < range.getValue().doubleValue()) {
            return false;
        }

        boolean outOfRange = backtrackDistance > range.getValue().floatValue();

        if (!outOfRange) {
            return false;
        }

        if (outOfRange) {
            trackingBufferChronometer.reset();
        }

        return target instanceof LivingEntity
                && killAura.isEnable() && killAura.currentTarget == target &&
                mc.player.tickCount > 10 &&
                Math.random() * 100 < chance.getValue().doubleValue() &&
                !shouldPause() &&
                !attackChronometer.hasTimeElapsed(lastAttackTimeToWork.getValue().longValue());
    }

    private boolean shouldPause() {
        return pauseOnHurtTime.getValue() && shouldPause;
    }

    private boolean shouldCancelPackets() {
        return target != null && target.isAlive() && shouldBacktrack(target);
    }

    @EventTarget
    public void onAttack(EventAttack event) {
        if (!isEnable() || Utils.isNull())
            return;

        attackChronometer.reset();

        if (position != null) {
            position.setAttacking(true);
        }

        Entity entity = event.getTargetEntity();
        if (entity != null) {
            processTarget(entity);
        }
    }

    @EventTarget
    public void onUpdate(EventWorld event) {
        if (shouldCancelPackets()) {
            processPackets(false);
        } else if (!clearing) {
            clear();
        }
    }

    private static class PacketEntry {
        final Packet<?> packet;
        final long timestamp;

        PacketEntry(Packet<?> packet) {
            this.packet = packet;
            this.timestamp = System.currentTimeMillis();
        }
    }

    private static class TrackedPosition {
        @Getter
        private Vec3 pos;
        @Getter
        private Vec3 actualPos;
        private long lastUpdateTime;
        private boolean isAttacking;
        private Vec3 lastValidPos;

        public void setPos(Vec3 pos) {
            if (this.pos != null) {
                double distance = this.pos.distanceTo(pos);
                if (distance > 5.0) {
                    return;
                }
            }
            this.pos = pos;
            if(!isAttacking) {
                this.lastValidPos = pos;
            }
        }

        public void updateActualPos(Vec3 pos) {
            if (pos == null) return;

            if (actualPos != null) {
                long currentTime = System.currentTimeMillis();
                if (!isAttacking && currentTime - lastUpdateTime < 50) return;

                double distance = actualPos.distanceTo(pos);
                double maxDistance = isAttacking ? 20.0 : 10.0;
                if (distance > maxDistance) {
                    if(lastValidPos != null) {
                        this.actualPos = lastValidPos;
                    }
                    return;
                }
            }

            this.actualPos = pos;
            if(!isAttacking) {
                this.lastValidPos = pos;
            }
            this.lastUpdateTime = System.currentTimeMillis();
        }

        public void setAttacking(boolean attacking) {
            this.isAttacking = attacking;
        }
    }
}
