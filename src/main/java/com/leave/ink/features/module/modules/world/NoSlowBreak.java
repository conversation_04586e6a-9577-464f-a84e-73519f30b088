package com.leave.ink.features.module.modules.world;

import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "NoSlowBreak", language = Language.English),
        @Text(label = "没有减速破坏", language = Language.Chinese)
}, category = Category.World)
public class NoSlowBreak extends Module {
    @SettingInfo(name = {
            @Text(label = "Air", language = Language.English),
            @Text(label = "空中", language = Language.Chinese)
    })
    public final BooleanSetting air = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Water", language = Language.English),
            @Text(label = "水中", language = Language.Chinese)
    })
    public final BooleanSetting water = new BooleanSetting(true);

    public NoSlowBreak() {
        registerSetting(air, water);
    }
}
