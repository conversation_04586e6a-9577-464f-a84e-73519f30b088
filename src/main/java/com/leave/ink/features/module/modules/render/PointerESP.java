package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.AntiBot;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.math.Axis;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;

import java.awt.*;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "PotionESP", language = Language.English),
        @Text(label = "指针显示", language = Language.Chinese)
}, category = Category.Render)
public class PointerESP extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Solid", Arrays.asList("Solid", "Line"));
    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    private final ColorSetting color = new ColorSetting(new Color(140, 140, 255, 255));
    @SettingInfo(name = {
            @Text(label = "Size", language = Language.English),
            @Text(label = "大小", language = Language.Chinese)
    })
    private final NumberSetting size = new NumberSetting(-18, -18, 200, "#");

    public PointerESP() {
        registerSetting(mode, color, size);
    }

    @EventTarget
    public void onRender(EventRender2D event) {
        ChatUtils.displayAlert("Yaw:" + mc.player.getYRot());
        var partialTicks = event.getPartialTicks();
        var poseStack = event.getPoseStack();
        int size = 50 + this.size.getValue().intValue();
        double xOffset = mc.getWindow().getGuiScaledWidth() / 2 - 24.5 - this.size.getValue().intValue() / 2.0;
        double yOffset = mc.getWindow().getGuiScaledHeight() / 2 - 25.2 - this.size.getValue().intValue() / 2.0;
        double playerOffsetX = mc.player.getX();
        double playerOffsetZ = mc.player.getZ();

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity livingEntity && Utils.isValidEntity(livingEntity) && !AntiBot.isBot(livingEntity) && !Targets.isTeam(livingEntity) && entity != mc.player) {
                double pos1 = (((livingEntity.getX() + (livingEntity.getX() - livingEntity.xOld) * partialTicks) - playerOffsetX) * 0.2);
                double pos2 = (((livingEntity.getZ() + (livingEntity.getZ() - livingEntity.zOld) * partialTicks) - playerOffsetZ) * 0.2);
                double cos = Math.cos(Math.toRadians(mc.player.getYRot()));
                double sin = Math.sin(Math.toRadians(mc.player.getYRot()));
                double rotY = -(pos2 * cos - pos1 * sin);
                double rotX = -(pos1 * cos + pos2 * sin);
                double var7 = 0 - rotX;
                double var9 = 0 - rotY;
                if (Math.sqrt(var7 * var7 + var9 * var9) < size / 2f - 4) {
                    float angle = (float) (Math.atan2(rotY, rotX) * 180 / Math.PI);
                    float x = (float) (((size / 2) * Math.cos(Math.toRadians(angle))) + xOffset + size / 2);
                    float y = (float) (((size / 2) * Math.sin(Math.toRadians(angle))) + yOffset + size / 2);
                    poseStack.pushPose();
                    poseStack.translate(x, y, 0);
                    poseStack.mulPose(Axis.ZP.rotationDegrees(angle));
                    poseStack.scale(1.5f, 1.0f, 1.0f);
                    switch (mode.getValue().toLowerCase()) {
                        case "solid": {
                            RenderUtils.drawTriangle(poseStack, 0F, 0F, 2.2F, 3F, color.getValue());
                            RenderUtils.drawTriangle(poseStack, 0F, 0F, 1.5F, 3F, color.getValue());
                            RenderUtils.drawTriangle(poseStack, 0F, 0F, 1.0F, 3F, color.getValue());
                            RenderUtils.drawTriangle(poseStack, 0F, 0F, 0.5F, 3F, color.getValue());
                            break;
                        }

                        case "line": {
                            RenderUtils.drawTriangle(poseStack, 0F, 0F, 2.2F, 3F, color.getValue());
                            break;
                        }
                    }
                    poseStack.popPose();
                }
            }
        }
    }
}
