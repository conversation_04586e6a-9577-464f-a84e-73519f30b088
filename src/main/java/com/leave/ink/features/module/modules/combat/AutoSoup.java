package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.InventoryUtils;
import com.leave.ink.utils.timer.TimeUtils;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.network.protocol.game.ServerboundSetCarriedItemPacket;
import net.minecraft.network.protocol.game.ServerboundUseItemPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.inventory.ClickType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "AutoSoup", language = Language.English),
        @Text(label = "自动喝汤", language = Language.Chinese)
}, category = Category.Combat)
public class AutoSoup extends Module {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    public final ModeSetting mode = new ModeSetting("Packet", Arrays.asList("Legit", "Packet"));
    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量", language = Language.Chinese)
    })
    private final NumberSetting healthValue = new NumberSetting(15, 0, 20, "#.0");
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delayValue = new NumberSetting(150, 0, 500, "#");
    @SettingInfo(name = {
            @Text(label = "OnlyInv", language = Language.English),
            @Text(label = "只在背包界面", language = Language.Chinese)
    })
    private final BooleanSetting onlyInvValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "MoveDelay", language = Language.English),
            @Text(label = "移动延迟", language = Language.Chinese)
    })
    private final NumberSetting moveDelayValue = new NumberSetting(100, 0, 500, "#");
    @SettingInfo(name = {
            @Text(label = "MoveToHotbar", language = Language.English),
            @Text(label = "移动背包至物品栏", language = Language.Chinese)
    })
    private final BooleanSetting moveToHotbarValue = new BooleanSetting(true,
            new SettingAttribute<>(onlyInvValue, true),
            new SettingAttribute<>(moveDelayValue, true));

    public AutoSoup() {
        registerSetting(mode, healthValue, delayValue, moveToHotbarValue);
    }

    private final TimeUtils timer = new TimeUtils();
    private final TimeUtils moveTimer = new TimeUtils();
    private int oldSlot;
    public boolean onEat;

    @Override
    public String getTag() {
        return mode.getValue();
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        if (moveToHotbarValue.getValue()) {
            if (!onlyInvValue.getValue() || mc.screen instanceof InventoryScreen) {
                if (moveTimer.hasTimeElapsed(moveDelayValue.getValue().longValue())) {
                    moveMushroomStewToInventory();
                    moveTimer.reset();
                }
            }
        }

        if (!timer.hasTimeElapsed(delayValue.getValue().longValue())) {
            return;
        }

        int soupInHotbar = InventoryUtils.findItem(0, 9, Items.MUSHROOM_STEW);
        if (mc.player.getHealth() <= healthValue.getValue().floatValue() && soupInHotbar != -1) {
            onEat = true;
            oldSlot = mc.player.getInventory().selected;
            if (mode.getValue().equals("Packet")) {
                mc.player.connection.send(new ServerboundSetCarriedItemPacket(soupInHotbar));
                mc.gameMode.useItem(mc.player, InteractionHand.MAIN_HAND);
                mc.player.connection.send(new ServerboundSetCarriedItemPacket(mc.player.getInventory().selected));
            } else {
                mc.player.getInventory().selected = soupInHotbar;
                mc.gameMode.useItem(mc.player, InteractionHand.MAIN_HAND);
                mc.player.getInventory().selected = oldSlot;
            }
            onEat = false;
            timer.reset();
        }
    }

    private void moveMushroomStewToInventory() {
        for (int i = 9; i < mc.player.getInventory().items.size(); i++) {
            ItemStack stack = mc.player.getInventory().getItem(i);
            if (!stack.isEmpty() && stack.getItem() == Items.MUSHROOM_STEW) {
                int emptyHotbarSlot = findHotbarSlot();
                if (emptyHotbarSlot != -1) {
                    mc.gameMode.handleInventoryMouseClick(0, i, emptyHotbarSlot, ClickType.SWAP, mc.player);
                }
                break;
            }
        }
    }

    private int findHotbarSlot() {
        for (int i = 0; i < 9; i++) {
            if (mc.player.getInventory().getItem(i).isEmpty() || mc.player.getInventory().getItem(i).is(Items.BOWL)) {
                return i;
            }
        }
        return -1;
    }
}
