package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventAttack;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;

@ModuleInfo(name = {
        @Text(label = "WTap", language = Language.English),
        @Text(label = "攻击疾跑", language = Language.Chinese)
}, category = Category.Combat)
public class WTap extends Module {

    @SettingInfo(name = {
            @Text(label = "Chance", language = Language.English),
            @Text(label = "几率", language = Language.Chinese)
    })
    public static final NumberSetting chance = new NumberSetting(50f, 0f, 100f, "#");

    private boolean shouldTap = false;
    private boolean stoppedSprintingForTap = false;

    public WTap() {
        registerSetting(chance);
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {

        if (stoppedSprintingForTap) {

            if ((mc.player.zza > 0 || mc.options.keySprint.isDown()) && !mc.player.horizontalCollision && !mc.player.isUsingItem() && !mc.player.hasEffect(net.minecraft.world.effect.MobEffects.BLINDNESS) && mc.player.getFoodData().getFoodLevel() > 6) {
                mc.player.setSprinting(true);
            }
            stoppedSprintingForTap = false;
        }
    }

    @EventTarget
    public void onAttack(EventAttack event) {
        shouldTap = Math.random() * 100 < chance.getValue().floatValue();
        stoppedSprintingForTap = false;

        if (!shouldTap) return;

        if (mc.player.isSprinting()) {
            mc.player.setSprinting(false);
            stoppedSprintingForTap = true;
        }
    }

    @Override
    protected void onDisable() {
        shouldTap = false;
        stoppedSprintingForTap = false;
        super.onDisable();
    }

    @Override
    protected void onEnable() {

        shouldTap = false;
        stoppedSprintingForTap = false;
        super.onEnable();
    }
}
