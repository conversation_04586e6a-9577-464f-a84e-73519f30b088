package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.AntiBot;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.NumberSetting;

import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.rotation.RotationUtils;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import com.mojang.math.Axis;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;

import java.awt.*;
import java.util.Objects;

@ModuleInfo(name = {
        @Text(label = "RadarArrows", language = Language.English),
        @Text(label = "雷达箭头", language = Language.Chinese)
}, category = Category.Render)
public class RadarArrows extends Module {
    @SettingInfo(name = {
            @Text(label = "X", language = Language.English),
            @Text(label = "X", language = Language.Chinese)
    })
    private final NumberSetting x = new NumberSetting(50, 0.0, 1000, "#");
    @SettingInfo(name = {
            @Text(label = "Y", language = Language.English),
            @Text(label = "Y", language = Language.Chinese)
    })
    private final NumberSetting y = new NumberSetting(50, 0.0, 1000, "#");
    @SettingInfo(name = {
            @Text(label = "Height", language = Language.English),
            @Text(label = "高度 ", language = Language.Chinese)
    })
    private final NumberSetting width = new NumberSetting(2.28F, 0.1F, 5.0F, "#.00");
    @SettingInfo(name = {
            @Text(label = "TracerRadius", language = Language.English),
            @Text(label = "追踪半径 ", language = Language.Chinese)
    })
    private final NumberSetting xOffset = new NumberSetting(68F, 20F, 100F, "#");
    @SettingInfo(name = {
            @Text(label = "PitchLock", language = Language.English),
            @Text(label = "Pitch锁定", language = Language.Chinese)
    })
    private final NumberSetting pitchLock = new NumberSetting(42F, 0F, 90F, "#");
    @SettingInfo(name = {
            @Text(label = "CompassRadius", language = Language.English),
            @Text(label = "指针半径 ", language = Language.Chinese)
    })
    private final NumberSetting CRadius = new NumberSetting(47.0F, 0.1F, 70.0F, "#.00");
    @SettingInfo(name = {
            @Text(label = "CircleColor", language = Language.English),
            @Text(label = "圆圈颜色", language = Language.Chinese)
    })
    private final ColorSetting circleColor = new ColorSetting(new Color(0xFFFFFF));
    @SettingInfo(name = {
            @Text(label = "CompassColor", language = Language.English),
            @Text(label = "指针颜色", language = Language.Chinese)
    })
    private final ColorSetting compassColor = new ColorSetting(new Color(0xFFFF00));

    public RadarArrows() {
        registerSetting(x, y, width, xOffset, pitchLock, CRadius, circleColor, compassColor);
    }

    @EventTarget
    public void onRender2D(EventRender2D event) {
        float middleW = x.getValue().intValue();
        float middleH = y.getValue().intValue();
        var poseStack = event.getPoseStack();

        poseStack.pushPose();
        renderCompass(poseStack, middleW + CRadius.getValue().floatValue(), middleH + CRadius.getValue().floatValue());
        poseStack.popPose();
        Color color = compassColor.getValue();
        poseStack.pushPose();
        poseStack.translate(middleW + CRadius.getValue().floatValue(), middleH + CRadius.getValue().floatValue(), 0);
        poseStack.mulPose(Axis.XP.rotationDegrees(90f / Math.abs(90f / Mth.clamp(mc.player.getXRot(), pitchLock.getValue().intValue(), 90f)) - 102));
        poseStack.translate(-(middleW + CRadius.getValue().floatValue()), -(middleH + CRadius.getValue().floatValue()), 0);

        for (Entity e : mc.level.entitiesForRendering()) {
            if (e instanceof LivingEntity entity && entity != mc.player) {
                if (!Utils.isValidEntity(entity) || Targets.isTeam(entity) || AntiBot.isBot(entity))
                    continue;

                poseStack.pushPose();
                float yaw = RotationUtils.getRotations(e) - mc.player.getYRot();
                poseStack.translate(middleW + CRadius.getValue().floatValue(), middleH + CRadius.getValue().floatValue(), 0.0F);
                poseStack.mulPose(Axis.ZP.rotationDegrees(yaw));
                poseStack.translate(-(middleW + CRadius.getValue().floatValue()), -(middleH + CRadius.getValue().floatValue()), 0.0F);
                RenderUtils.drawNewArrow(poseStack, middleW + CRadius.getValue().floatValue(), middleH - xOffset.getValue().intValue() + CRadius.getValue().floatValue(), width.getValue().floatValue() * 5F + 8F, color);
                poseStack.translate(middleW + CRadius.getValue().floatValue(), middleH + CRadius.getValue().floatValue(), 0.0F);
                poseStack.mulPose(Axis.ZP.rotationDegrees(-yaw));
                poseStack.translate(-(middleW + CRadius.getValue().floatValue()), -(middleH + CRadius.getValue().floatValue()), 0.0F);
                poseStack.popPose();
            }
        }
        poseStack.popPose();
    }

    public void renderCompass(PoseStack poseStack, float x, float y) {
        float pitchFactor = Math.abs(90f / Mth.clamp(mc.player.getXRot(), pitchLock.getValue().intValue(), 90f));
        drawEllipsCompas(poseStack, -(int) mc.player.getYRot(), x, y, pitchFactor, 1f, -2f, 1f, circleColor.getValue(), false);
        drawEllipsCompas(poseStack, -(int) mc.player.getYRot(), x, y, pitchFactor, 1f, 0f, 3f, Color.WHITE, true);
    }

    public void drawEllipsCompas(PoseStack poseStack, int yaw, float x, float y, float x2, float y2, float margin, float width, Color color, boolean Dir) {
        drawElipse(poseStack, x, y, x2, y2, 15 + yaw, 75 + yaw, margin, width, color, Dir ? "W" : "");
        drawElipse(poseStack, x, y, x2, y2, 105 + yaw, 165 + yaw, margin, width, color, Dir ? "N" : "");
        drawElipse(poseStack, x, y, x2, y2, 195 + yaw, 255 + yaw, margin, width, color, Dir ? "E" : "");
        drawElipse(poseStack, x, y, x2, y2, 285 + yaw, 345 + yaw, margin, width, color, Dir ? "S" : "");
    }

    public void drawElipse(PoseStack poseStack, float x, float y, float rx, float ry, float start, float end, float margin, float width, Color color, String direction) {
        float sin;
        float cos;
        float endOffset;

        if (start > end) {
            endOffset = end;
            end = start;
            start = endOffset;
        }

        RenderSystem.enableBlend();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder bufferBuilder = tesselator.getBuilder();
        bufferBuilder.begin(VertexFormat.Mode.TRIANGLE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        float radius = CRadius.getValue().floatValue() - margin;
        for (float i = start; i <= end; i += 6) {
            float stage = (i - start) / 360f;
            if (!Objects.equals(direction, ""))
                color = RenderUtils.getColor((int) (stage * 500f));

            cos = (float) Math.cos(i * Math.PI / 180);
            sin = (float) Math.sin(i * Math.PI / 180);
            bufferBuilder.vertex((x + cos * (radius / ry)), (y + sin * (radius / rx)), 0f).color(color.getRGB()).endVertex();
            bufferBuilder.vertex((x + cos * ((radius - width) / ry)), (y + sin * ((radius - width) / rx)), 0f).color(color.getRGB()).endVertex();
        }

        tesselator.end();
        RenderSystem.disableBlend();

        if (!Objects.equals(direction, ""))
            FontRenderers.getModulesRenderer().drawString(poseStack, direction, (x - 2 + Math.cos((start - 15) * Math.PI / 180) * (radius / ry)), (y - 1 + Math.sin((start - 15) * Math.PI / 180) * (radius / rx)), -1);
    }
}
