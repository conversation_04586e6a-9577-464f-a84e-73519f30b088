package com.leave.ink.features.module.modules.combat;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.rotation.*;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.EggItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SnowballItem;
import net.minecraft.world.item.FishingRodItem;


import java.util.*;

@ModuleInfo(name = {
        @Text(label = "CrystalAura", language = Language.English),
        @Text(label = "CrystalAura", language = Language.Chinese)
}, category = Category.Combat)
public class CrystalAura extends Module {
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delay = new NumberSetting(500, 100, 3000, "#");

    @SettingInfo(name = {
            @Text(label = "Range", language = Language.English),
            @Text(label = "距离", language = Language.Chinese)
    })
    private final NumberSetting range = new NumberSetting(5.0, 3.0, 10.0, "#.0");


    public List<LivingEntity> entities = new ArrayList<>();
    public LivingEntity target = null;
    public CrystalAura() {
        registerSetting(
                delay,range
        );

    }
    @Override
    protected void onEnable() {
        entities.clear();
    }

    @EventTarget
    public void onThrow(EventUpdate eventUpdate) {
        Module scaffold = Main.INSTANCE.moduleManager.getModule("Scaffold");
        KillAura ka = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if(scaffold.isEnable()) return;
        boolean calculateRotation = true;
        Rotation rotation;
        if(ka.isEnable()) {
            if (ka.currentTarget != null) {
                calculateRotation = false;
            }
        }
        updateEntities();
        if(entities.isEmpty()) return;

        target = entities.get(0);
        int itemSlot = findProjectileSlot(false);
        if(itemSlot == -1) return;
        if(calculateRotation) {
            rotation = ClipRotation.getHVHRotation(target);
        }else{
            rotation = RotationUtils.targetRotations;
        }
        if(rotation != null) {
            int slotOriginal = mc.player.getInventory().selected;
            RotationUtils.setRotation(rotation, 10.0, MovementFix.NORMAL);

            mc.player.getInventory().selected = itemSlot;
            mc.gameMode.useItem(mc.player, InteractionHand.MAIN_HAND);

           // mc.player.getInventory().selected = slotOriginal;
        }
    }

    public void updateEntities() {
        entities.clear();
        for (Entity entity : mc.level.entitiesForRendering()) {
            if(entity == mc.player) continue;
            if(entity instanceof LivingEntity livingEntity) {
                if(!Utils.isValidEntity(livingEntity)) continue;
                if(RotationUtils.getDistanceToEntityBox(livingEntity) > range.getValue().doubleValue()) continue;
                entities.add(livingEntity);
            }
        }
    }
    /**
     * 查找背包中的可投掷物品或钓鱼竿
     *
     * @return 槽位索引，-2表示副手，-1表示未找到合适的物品
     */
    private int findProjectileSlot(boolean fishingRod) {
        if (mc.player == null || mc.level == null) return -1;


        final ItemStack offhandStack = mc.player.getOffhandItem();
        if (!offhandStack.isEmpty() &&
                (offhandStack.getItem() instanceof EggItem ||
                        offhandStack.getItem() instanceof SnowballItem ||
                        (fishingRod && offhandStack.getItem() instanceof FishingRodItem))) {
            return -2;
        }



            for (int i = 0; i < 9; i++) {
                final ItemStack stack = mc.player.getInventory().getItem(i);

                if (stack.isEmpty())
                    continue;

                if (stack.getItem() instanceof EggItem ||
                        stack.getItem() instanceof SnowballItem ||
                        (fishingRod && stack.getItem() instanceof FishingRodItem)) {
                    return i;
                }
            }


        return -1;
    }

}
