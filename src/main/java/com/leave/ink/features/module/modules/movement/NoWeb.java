package com.leave.ink.features.module.modules.movement;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventUpdate;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.player.MovementUtils;
import net.minecraft.world.phys.AABB;
import java.util.Arrays;
import static net.minecraft.world.level.block.Blocks.COBWEB;

@ModuleInfo(name = {
        @Text(label = "NoWeb", language = Language.English),
        @Text(label = "没有蜘蛛网移动", language = Language.Chinese)
}, category = Category.Movement)
public class NoWeb extends Module {
    @SettingInfo(name = {
            @Text(label = "XzSpeed", language = Language.English),
            @Text(label = "左右速度", language = Language.Chinese)
    })
    private final NumberSetting xz = new NumberSetting(0.5, 0.1, 1.0, "#.0");
    @SettingInfo(name = {
            @Text(label = "UpSpeed", language = Language.English),
            @Text(label = "上速度", language = Language.Chinese)
    })
    private final NumberSetting up = new NumberSetting(0.5, 0.1, 1.0, "#.0");
    @SettingInfo(name = {
            @Text(label = "DownSpeed", language = Language.English),
            @Text(label = "下速度", language = Language.Chinese)
    })
    private final NumberSetting down = new NumberSetting(0.5, 0.1, 1.0, "#.0");
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Normal", Arrays.asList("Normal", "Grim"),
            new SettingAttribute<>(xz, "Normal"),
            new SettingAttribute<>(up, "Normal"),
            new SettingAttribute<>(down, "Normal"));

    public NoWeb() {
        registerSetting(mode);
    }

    @Override
    public String getTag() {
        return mode.getValue();
    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        BlockUtils.searchBlocks(2).forEach((key, value) -> {
            if (value == COBWEB) {
                if (new AABB(key.getX(), key.getY(), key.getZ(), key.getX() + 1, key.getY() + 1, key.getZ() + 1).intersects(mc.player.getBoundingBox())) {
                    mc.player.setDeltaMovement(0, 0, 0);
                    switch (mode.getValue()) {
                        case "Normal":
                            if (MovementUtils.isMoving()) {
                                MovementUtils.strafe(xz.getValue().floatValue());
                            }

                            if (mc.options.keyJump.isDown()) {
                                mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, up.getValue().doubleValue(), mc.player.getDeltaMovement().z);
                                return;
                            }

                            if (mc.options.keyShift.isDown()) {
                                mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, down.getValue().doubleValue(), mc.player.getDeltaMovement().z);
                            }
                            break;

                        case "Grim":
                            if (MovementUtils.isMoving()) {
                                MovementUtils.strafe(0.63999999999999999999999f);
                            }

                            if (mc.options.keyJump.isDown()) {
                                mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, 0.63999999999999999999999f, mc.player.getDeltaMovement().z);
                                return;
                            }

                            if (mc.options.keyShift.isDown()) {
                                mc.player.setDeltaMovement(mc.player.getDeltaMovement().x, -0.63999999999999999999999f, mc.player.getDeltaMovement().z);
                            }
                            break;
                    }
                }
            }
        });
    }
}
