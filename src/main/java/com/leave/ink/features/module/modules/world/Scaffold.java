package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.hud.HudManager;
import com.leave.ink.features.hud.dynamicIsland.impl.ProcessDynamic;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.player.MovementUtils;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.player.PlayerUtils;
import com.leave.ink.utils.player.InventoryUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.leave.ink.utils.rotation.*;
import com.leave.ink.utils.rotation.Rotation;
import com.mojang.blaze3d.platform.InputConstants;
import net.minecraft.client.KeyMapping;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.protocol.game.ServerboundSwingPacket;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.block.*;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;

import org.lwjgl.glfw.GLFW;
import java.awt.*;
import java.util.Arrays;

@ModuleInfo(name = {
        @Text(label = "Scaffold", language = Language.English),
        @Text(label = "搭路", language = Language.Chinese)
}, category = Category.World)
public class Scaffold extends Module {
    @SettingInfo(name = {
            @Text(label = "OffGroundTick", language = Language.English),
            @Text(label = "空中时刻", language = Language.Chinese)
    })
    private final NumberSetting offGroundTick = new NumberSetting(0.0, 0.0, 10.0, "#");
    @SettingInfo(name = {
            @Text(label = "UpTelly", language = Language.English),
            @Text(label = "高搭切换", language = Language.Chinese)
    })
    private final BooleanSetting upTelly = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    private final ModeSetting mode = new ModeSetting("Telly", Arrays.asList("Normal", "Telly"),
            new SettingAttribute<>(offGroundTick, "Telly"),
            new SettingAttribute<>(upTelly, "Telly")
    );

    @SettingInfo(name = {
            @Text(label = "SneakMode", language = Language.English),
            @Text(label = "蹲下模式", language = Language.Chinese)
    })
    private final ModeSetting sneakMode = new ModeSetting("Heypixel", Arrays.asList("Off", "Heypixel", "Legit"));
    @SettingInfo(name = {
            @Text(label = "Sprint", language = Language.English),
            @Text(label = "疾跑", language = Language.Chinese)
    })
    private final ModeSetting sprint = new ModeSetting("OnGround", Arrays.asList("Off", "OnGround", "Full"));
    @SettingInfo(name = {
            @Text(label = "KeepRotation", language = Language.English),
            @Text(label = "保持转头", language = Language.Chinese)
    })
    private final BooleanSetting keepRotationValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "MoveFix", language = Language.English),
            @Text(label = "移动修复", language = Language.Chinese)
    })
    private final BooleanSetting moveFixValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Swing", language = Language.English),
            @Text(label = "挥手", language = Language.Chinese)
    })
    private final BooleanSetting swingValue = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "RandomRotation", language = Language.English),
            @Text(label = "RandomRotation", language = Language.Chinese)
    })
    private final BooleanSetting randomRotation = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "RayCastExact", language = Language.English),
            @Text(label = "光线对准精准大小", language = Language.Chinese)
    })
    private final NumberSetting rayCastExact = new NumberSetting(0.9, 0.1, 1, "#.00");
    @SettingInfo(name = {
            @Text(label = "RayCast", language = Language.English),
            @Text(label = "光线对准", language = Language.Chinese)
    })
    private final BooleanSetting rayCast = new BooleanSetting(false, new SettingAttribute<>(rayCastExact, true));
    @SettingInfo(name = {
            @Text(label = "MarkColor", language = Language.English),
            @Text(label = "圆圈颜色", language = Language.Chinese)
    })
    private final ColorSetting markColor = new ColorSetting(new Color(255, 255, 255, 255));
    @SettingInfo(name = {
            @Text(label = "Mark", language = Language.English),
            @Text(label = "标记", language = Language.Chinese)
    })
    private final BooleanSetting mark = new BooleanSetting(true, new SettingAttribute<>(markColor, true));
    private int ticksOnAir = 0;
    public int fakeItemSlot = -1;
    public double keepY;
    private PlaceInfo currentPlaceInfo = null;
    private Rotation currentRotation = null;

    @Override
    protected boolean showDynamic() {
        return false;
    }

    public Scaffold() {
        registerSetting(mode, sprint, keepRotationValue, moveFixValue, sneakMode, randomRotation, rayCast,
                swingValue, mark);
    }

    private ProcessDynamic processDynamic = null;

    @Override
    public void onEnable() {
        if (Utils.isNull()) return;
        try {
            if (mc.options.keyAttack.isDown())
                KeyMapping.set(mc.options.keyAttack.getKey(), false);

            if (processDynamic == null) {
                processDynamic = new ProcessDynamic("", "", 10, 100);
                processDynamic.sticky = true;
                processDynamic.setKeepAlive(true);
                HudManager.dynamicIsland.addTask(processDynamic);
            }

            int cnt = Math.max(BlockUtils.getBlocksCountInv(), 0);
            processDynamic.current = cnt;
            processDynamic.max = cnt;

            fakeItemSlot = mc.player.getInventory().selected;
            processDynamic.setItemSlot(fakeItemSlot);
        } catch (Exception e) {
            ChatUtils.displayAlert(e.getMessage());
        }
    }

    public boolean isValidBlock(final BlockPos blockPos) {
        if (mc.level == null) return false;

        final BlockState blockState = mc.level.getBlockState(blockPos);
        final Block block = blockState.getBlock();

        if (block instanceof AirBlock || block instanceof LiquidBlock) {
            return false;
        }

        // 排除
        return !(block instanceof ChestBlock)
                && !(block instanceof EnderChestBlock)
                && !(block instanceof ShulkerBoxBlock)
                && !(block instanceof BarrelBlock)
                && !(block instanceof FurnaceBlock)
                && !(block instanceof BlastFurnaceBlock)
                && !(block instanceof SmokerBlock)
                && !(block instanceof HopperBlock)
                && !(block instanceof DispenserBlock)
                && !(block instanceof CraftingTableBlock)
                && !(block instanceof EnchantmentTableBlock)
                && !(block instanceof AnvilBlock)
                && !(block instanceof GrindstoneBlock)
                && !(block instanceof StonecutterBlock)
                && !(block instanceof LoomBlock)
                && !(block instanceof CartographyTableBlock)
                && !(block instanceof LeverBlock)
                && !(block instanceof ButtonBlock)
                && !(block instanceof PressurePlateBlock)
                && !(block instanceof TripWireHookBlock)
                && !(block instanceof DoorBlock)
                && !(block instanceof TrapDoorBlock)
                && !(block instanceof FenceGateBlock)
                && !(block instanceof NoteBlock)
                && !(block instanceof JukeboxBlock)
                && !(block instanceof BeaconBlock)
                && !(block instanceof BrewingStandBlock)
                && !(block instanceof CauldronBlock)
                && !(block instanceof ComparatorBlock)
                && !(block instanceof RepeaterBlock)
                && !(block instanceof CommandBlock)
                && !(block instanceof StructureBlock)
                && !(block instanceof JigsawBlock)
                && !(block instanceof BedBlock)
                && !(block instanceof LecternBlock)
                && !(block instanceof ComposterBlock)
                && !(block instanceof RedStoneWireBlock)
                && !(block instanceof RedstoneTorchBlock)
                && !(block instanceof CakeBlock)
                && !(block instanceof FlowerPotBlock)
                && !(block instanceof SignBlock)
                && !(block instanceof BannerBlock)
                && !(block instanceof WallBannerBlock)
                && !(block instanceof CropBlock)
                && !(block instanceof StemBlock)
                && !(block instanceof AttachedStemBlock)
                && !(block instanceof CocoaBlock)
                && !(block instanceof SweetBerryBushBlock)
                && !(block instanceof SugarCaneBlock)
                && !(block instanceof CactusBlock)
                && !(block instanceof KelpBlock)
                && !(block instanceof SeaPickleBlock)
                && !(block instanceof SpawnerBlock)
                && !(block instanceof TurtleEggBlock)
                && !(block instanceof BeehiveBlock);
    }

    @Override
    public void onDisable() {
        if (mc.player == null && mc.level == null) return;

        processDynamic.setKeepAlive(false);
        processDynamic.sticky = false;
        processDynamic = null;

        mc.options.keyJump.setDown(GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) == GLFW.GLFW_PRESS);

        if (fakeItemSlot != -1) {
            mc.player.getInventory().selected = fakeItemSlot;
            fakeItemSlot = -1;
        }
    }

    @EventTarget
    public void onMouseWheel(EventScroll event) {
        if (Utils.isNull())
            return;

        if (event.getGetScrollDelta() > 0) {
            fakeItemSlot--;
        } else if (event.getGetScrollDelta() < 0) {
            fakeItemSlot++;
        }
    }

    private KillAura aura = null;

    @EventTarget
    public void onStrafeEvent(EventStrafe event) {
        mc.options.keyJump.setDown(
                (mc.player.onGround() && MovementUtils.isMoving() && mode.is("Telly"))
                        || InputConstants.isKeyDown(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()));
    }

    @EventTarget
    public void onMotion(EventMotion eventMotion) {
        if (eventMotion.getEventType() != EventType.PRE) return;
        if (!mc.player.onGround()) {
            ticksOnAir++;
        } else {
            ticksOnAir = 0;
        }

        if (sneakMode.is("Legit")) {
            if (PlayerUtils.getBlockUnderPlayer(mc.player) instanceof AirBlock) {
                if (mc.player.onGround()) {
                    KeyMapping.set(mc.options.keyShift.getKey(), true);
                }
            } else if (mc.player.onGround()) {
                KeyMapping.set(mc.options.keyShift.getKey(), false);
            }
        }

        if (sneakMode.is("Heypixel")) {
            KeyMapping.set(mc.options.keyShift.getKey(), ticksOnAir == 5);
        }
    }

    private boolean canTellyPlace = false;

    @EventTarget
    public void onUpdate(EventUpdate e) {
        if (aura == null)
            aura = ((KillAura) Main.INSTANCE.moduleManager.getModule("KillAura"));
        if (aura.isEnable() && aura.target != null) {
            return;
        }
        if (mc.player.onGround()) {
            keepY = Math.floor(mc.player.getY() - 1.0);
        }
        updateBlock();


        if (mode.is("Telly")) {
            int requiredTicks = upTelly.getValue() ? 1 : offGroundTick.getValue().intValue();
            canTellyPlace = ticksOnAir >= requiredTicks;
            if (canTellyPlace && !mc.player.onGround() && MovementUtils.isMoving()) {
                mc.player.setSprinting(false);
            }
        } else {
            canTellyPlace = true;
        }
        if (!canTellyPlace) return;

        if (currentPlaceInfo != null) {
            currentRotation = RotationUtils.getRotationBlock(currentPlaceInfo.getBlockPos());
            RotationUtils.setRotation(currentRotation, 10, moveFixValue.getValue() ? MovementFix.NORMAL : MovementFix.OFF, randomRotation.getValue());

        }

        if (!rayCast.getValue() || RotationUtils.isLookingAtBlock(RotationUtils.serverRotation, currentPlaceInfo.getBlockPos(), 4, rayCastExact.getValue().floatValue()))
            place();

        if (!keepRotationValue.getValue()) {
            currentPlaceInfo = null;
        }
    }


    @EventTarget
    public void onRender(EventRender2D eventRender2D) {

        int cnt = BlockUtils.getBlocksCountInv();
        double xDif = mc.player.getX() - mc.player.xOld;
        double zDif = mc.player.getZ() - mc.player.zOld;
        double lastDist = Math.sqrt(xDif * xDif + zDif * zDif) * 20.0;
        processDynamic.rightText = String.format("%.2f", lastDist) + " b/s";
        processDynamic.leftText = (cnt > 0 ? (cnt + "") : "No") +  " §fBlocks";
        processDynamic.current = cnt;
    }

    public ItemStack getFakeCurrentItem() {
        return fakeItemSlot >= 0 && fakeItemSlot <= 8 ? mc.player.getInventory().getItem(fakeItemSlot) : ItemStack.EMPTY;
    }

    private void place() {
        if (this.fakeItemSlot < 0) return;

        BlockPos targetPos = BlockPos.containing(mc.player.getX(), getYLevel(), mc.player.getZ());
        if (BlockUtils.getBlock(targetPos) instanceof AirBlock) {
            BlockHitResult hitResult = new BlockHitResult(
                    getVec3(currentPlaceInfo.getBlockPos(), currentPlaceInfo.getEnumFacing()),
                    currentPlaceInfo.getEnumFacing(),
                    currentPlaceInfo.getBlockPos(),
                    false
            );

            if (mc.gameMode.useItemOn(mc.player, InteractionHand.MAIN_HAND, hitResult) == InteractionResult.SUCCESS) {
                if (swingValue.getValue()) {
                    mc.player.swing(InteractionHand.MAIN_HAND);
                } else {
                    mc.player.connection.send(new ServerboundSwingPacket(InteractionHand.MAIN_HAND));
                }
            }
        }
    }

    private Vec3 getVec3(BlockPos pos, Direction face) {
        double x = pos.getX() + 0.5;
        double y = pos.getY() + 0.5;
        double z = pos.getZ() + 0.5;
        double randomOffset = MathUtils.getRandomInRange(0.3, -0.3);

        if (face == Direction.UP || face == Direction.DOWN) {
            x += randomOffset;
            z += MathUtils.getRandomInRange(0.3, -0.3);
        } else if (face == Direction.WEST || face == Direction.EAST) {
            y += randomOffset;
            z += MathUtils.getRandomInRange(0.3, -0.3);
        } else {
            y += randomOffset;
            x += MathUtils.getRandomInRange(0.3, -0.3);
        }
        return new Vec3(x, y, z);
    }

    private void updateBlock() {
        if (fakeItemSlot > 8) fakeItemSlot = 0;
        if (fakeItemSlot < 0) fakeItemSlot = 8;
        for (int i = 0; i < 9; ++i) {
            if (mc.options.keyHotbarSlots[i].isDown()) {
                fakeItemSlot = i;
            }
        }

        if (InventoryUtils.findAutoBlockBlock() != -1) {
            mc.player.getInventory().selected = InventoryUtils.findAutoBlockBlock();
        }
        final BlockPos basePos = BlockPos.containing(mc.player.getX(), getYLevel(), mc.player.getZ());
        if (isValidBlock(basePos) || search(basePos)) {
            return;
        }

        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x == 0 && z == 0) continue;
                if (search(basePos.offset(x, 0, z))) {
                    return;
                }
            }
        }
        if (!isValidBlock(basePos)) {
            this.currentPlaceInfo = null;
        }
    }

    private double calcStepSize(double range) {
        double accuracy = 6;
        return Math.max(range / accuracy, 0.01);
    }

    private boolean search(final BlockPos blockPosition) {
        final Vec3 eyesPos = mc.player.getEyePosition();
        PlaceRotation placeRotation = null;

        double xzRV = 0.5;
        double yRV = 0.5;
        double xzSSV = calcStepSize(xzRV);
        double ySSV = calcStepSize(yRV);

        Direction[] prioritizedSides = new Direction[]{
                Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST,
                Direction.UP, Direction.DOWN
        };

        for (final Direction side : prioritizedSides) {
            final BlockPos neighbor = blockPosition.relative(side);
            if (!isValidBlock(neighbor)) continue;

            final Vec3 dirVec = new Vec3(side.getNormal().getX(), side.getNormal().getY(), side.getNormal().getZ());
            for (double xSearch = 0.5 - xzRV / 2; xSearch <= 0.5 + xzRV / 2; xSearch += xzSSV) {
                for (double ySearch = 0.5 - yRV / 2; ySearch <= 0.5 + yRV / 2; ySearch += ySSV) {
                    for (double zSearch = 0.5 - xzRV / 2; zSearch <= 0.5 + xzRV / 2; zSearch += xzSSV) {
                        final Vec3 posVec = new Vec3(blockPosition.getX() + xSearch, blockPosition.getY() + ySearch, blockPosition.getZ() + zSearch);
                        final Vec3 hitVec = posVec.add(dirVec.x * 0.5, dirVec.y * 0.5, dirVec.z * 0.5);

                        if (eyesPos.distanceToSqr(hitVec) > 25.0 ||
                                eyesPos.distanceToSqr(posVec) > eyesPos.distanceToSqr(posVec.add(dirVec)) ||
                                mc.level.clip(new ClipContext(eyesPos, hitVec, ClipContext.Block.COLLIDER, ClipContext.Fluid.NONE, mc.player)).getType() != HitResult.Type.MISS) {
                            continue;
                        }

                        final double diffX = hitVec.x - eyesPos.x;
                        final double diffY = hitVec.y - eyesPos.y;
                        final double diffZ = hitVec.z - eyesPos.z;
                        final double diffXZ = Math.sqrt(diffX * diffX + diffZ * diffZ);

                        final Rotation rotation = new Rotation(
                                (float) Math.toDegrees(Math.atan2(diffZ, diffX)) - 90F,
                                (float) -Math.toDegrees(Math.atan2(diffY, diffXZ))
                        );

                        final Vec3 rotationVector = RotationUtils.getVectorForRotation(rotation);
                        final Vec3 vector = eyesPos.add(rotationVector.x * 5.0, rotationVector.y * 5.0, rotationVector.z * 5.0);
                        final BlockHitResult obj = mc.level.clip(new ClipContext(eyesPos, vector, ClipContext.Block.COLLIDER, ClipContext.Fluid.NONE, mc.player));

                        if (obj.getType() != HitResult.Type.BLOCK || !obj.getBlockPos().equals(neighbor)) continue;

                        if (placeRotation == null || RotationUtils.getRotationDifference(rotation) < RotationUtils.getRotationDifference(placeRotation.rotation())) {
                            placeRotation = new PlaceRotation(new PlaceInfo(neighbor, side.getOpposite(), hitVec), rotation);
                        }
                    }
                }
            }
        }

        if (placeRotation == null) return false;
        currentPlaceInfo = placeRotation.placeInfo();
        return true;
    }

    public double getYLevel() {
        if (mode.is("Telly")) {
            if (upTelly.getValue()) {
                return mc.player.getY() - 1.0;
            } else {
                if (mc.options.keyJump.isDown()) {
                    return mc.player.getY() - 1.0;
                } else {
                    return MovementUtils.isMoving() ? keepY : mc.player.getY() - 1.0;
                }
            }
        }
        return mc.player.getY() - 1.0;
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {

        processDynamic.setItemSlot(mc.player.getInventory().selected);

        Direction facing = mc.player.getDirection();
        double x = mc.player.getX() + (facing == Direction.WEST ? -0.5 : facing == Direction.EAST ? 0.5 : 0);
        double y = getYLevel();
        double z = mc.player.getZ() + (facing == Direction.NORTH ? -0.5 : facing == Direction.SOUTH ? 0.5 : 0);
        BlockPos blockPos = BlockPos.containing(x, y, z);
        PlaceInfo placeInfo = PlaceInfo.get(blockPos);

        if (mark.getValue() && placeInfo != null) {
            RenderUtils.drawLineBox(event.getPoseStack(), blockPos, markColor.getValue());
        }
    }
}
