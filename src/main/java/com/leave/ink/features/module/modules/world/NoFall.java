package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.*;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.timer.MinecraftTimer;
import com.leave.ink.utils.wrapper.WrapperUtils;
import lombok.Getter;
import net.minecraft.network.protocol.game.ClientboundPlayerPositionPacket;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import java.util.Arrays;

@ModuleInfo(name = {@Text(label = "NoFall", language = Language.English), @Text(label = "摔不死", language = Language.Chinese)}, category = Category.World)
public class NoFall extends Module {
    public static NoFall instance;

    @SettingInfo(name = {@Text(label = "Mode", language = Language.English), @Text(label = "模式", language = Language.Chinese)})
    public final ModeSetting mode = new ModeSetting("Grim",Arrays.asList("Grim", "Extra", "Semi Ground", "No Ground"));

    @SettingInfo(name = {@Text(label = "SkipTick", language = Language.English), @Text(label = "落地时跳过一个方块人包", language = Language.Chinese)})
    private final BooleanSetting skipTick = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Distance", language = Language.English), @Text(label = "距离", language = Language.Chinese)})
    private final NumberSetting distance = new NumberSetting( 3.3, 0, 5,"#.0");
    public boolean sendLag = false;
    private boolean lagged = false;
    private boolean timed = false;
    public boolean doSex = false;

    @Getter
    private boolean jump = false;

    public NoFall() {
        instance = this;
        registerSetting(mode, skipTick, distance);
    }

    @Override
    public void onEnable() {
        reset();
    }

    @Override
    public void onDisable() {
        reset();
    }

    private void reset() {
        if (timed) {
            timed = false;
            MinecraftTimer.setTimerSpeed(1F);
        }

        lagged = false;
        doSex = false;
        sendLag = false;
    }

    @EventTarget
    public void onTick(EventTick event) {
        if (mc.player == null || mc.level == null) return;

        if (mode.is("Grim")) {
            if (lagged && doSex) {
                jump = true;
                doSex = false;
                lagged = false;

                PacketUtils.sendPacket(new ServerboundMovePlayerPacket.StatusOnly(false));
                if (skipTick.getValue()) {
                    WrapperUtils.setSkipTicks(1);
                }
            }
        }
    }

    @EventTarget
    public void onStrafe(EventStrafe event) {
        if (mc.player == null || mc.level == null) return;

        if (mc.player.onGround() && jump && !mc.options.keyJump.isDown()) {
            mc.player.jumpFromGround();
            jump = false;
        }
    }

    @EventTarget
    public void onMotion(EventMotion event) {
        if (event.getEventType() == EventType.POST || mc.player == null || mc.level == null) return;

        switch (mode.getValue()) {
            case "Grim" -> {

                if (!doSex && mc.player.fallDistance > distance.getValue().doubleValue() && !event.isOnGround()) {
                    doSex = true;
                    lagged = false;
                    sendLag = false;
                }

                if (doSex && event.isOnGround()) {
                    event.setOnGround(false);
                    if (!sendLag) {
                        PacketUtils.sendPacketNoEvent(new ServerboundMovePlayerPacket.Pos(event.getX() + 1000, event.getY(), event.getZ(), false));
                        sendLag = true;
                    }
                }
            }

            case "No Ground" -> event.setOnGround(false);

            case "Semi Ground" -> {
                if (mc.player.fallDistance > distance.getValue().doubleValue() && !event.isOnGround()) {
                    PacketUtils.sendPacketNoEvent(new ServerboundMovePlayerPacket.StatusOnly(true));
                    WrapperUtils.setSkipTicks(1);
                }
            }

            case "Extra" -> {
                if (mc.player.fallDistance >= distance.getValue().doubleValue() && !event.isOnGround()) {
                    MinecraftTimer.setTimerSpeed(0.5F);
                    timed = true;
                    PacketUtils.sendPacketNoEvent(new ServerboundMovePlayerPacket.StatusOnly(true));
                    mc.player.fallDistance = 0;
                } else if (timed) {
                    MinecraftTimer.setTimerSpeed(1F);
                    timed = false;
                }
            }
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (mc.player == null || mc.level == null) return;

        switch (event.getPacketType()) {
            case Client -> {
                if (doSex && sendLag && !lagged && event.getPacket() instanceof ServerboundMovePlayerPacket) {
                    event.setCancelled(true);
                }
            }
            case Server -> {
                if (doSex && event.getPacket() instanceof ClientboundPlayerPositionPacket) {
                    lagged = true;
                }
            }
        }
    }

    @Override
    public String getTag() {
        return mode.getValue();
    }
}