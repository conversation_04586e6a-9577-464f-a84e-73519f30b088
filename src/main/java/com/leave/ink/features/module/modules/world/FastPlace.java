package com.leave.ink.features.module.modules.world;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventTick;
import com.leave.ink.events.EventType;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import net.minecraft.client.Minecraft;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;

import java.lang.reflect.Field;

@ModuleInfo(name = {
        @Text(label = "FastPlace", language = Language.English),
        @Text(label = "快速放置", language = Language.Chinese)
}, category = Category.World)
public class FastPlace extends Module {
    @SettingInfo(name = {
            @Text(label = "Delay", language = Language.English),
            @Text(label = "延迟", language = Language.Chinese)
    })
    private final NumberSetting delay = new NumberSetting(0.0D, 0.0D, 4.0d, "#");
    @SettingInfo(name = {
            @Text(label = "BlockOnly", language = Language.English),
            @Text(label = "只有方块时", language = Language.Chinese)
    })
    private final BooleanSetting block = new BooleanSetting(true);
    private Field field = null;

    public FastPlace() {
        registerSetting(delay, block);
    }

    @Override
    public void onEnable() {

    }

    @EventTarget
    public void onUpdate(EventTick event) {
        if(event.getEventType() != EventType.PRE) return;
        Item heldItem = mc.player.getMainHandItem().getItem();

        if (block.getValue()) {
            if (!(heldItem instanceof BlockItem))
            {

                return;
            }
        }

        if (mc.isWindowActive()) {
            int delay = this.delay.getValue().intValue();
            ObfuscationReflectionHelper.setPrivateValue(Minecraft.class, mc,delay,"rightClickDelay");

        }
    }
}
