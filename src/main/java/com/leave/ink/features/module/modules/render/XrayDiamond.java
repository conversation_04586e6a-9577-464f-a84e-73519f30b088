package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.*;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.core.BlockPos;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;
import net.minecraft.util.Mth;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.VoxelShape;
import org.joml.Matrix4f;

import java.awt.*;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import com.leave.ink.utils.client.Multithreading;

@ModuleInfo(
        name = {
                @Text(label = "XrayDiamond", language = Language.English),
                @Text(label = "XrayDiamond", language = Language.Chinese)
        },
        category = Category.Render
)
public class XrayDiamond extends Module {
    @SettingInfo(name = {
            @Text(label = "Wall Hack", language = Language.English),
            @Text(label = "穿墙透视", language = Language.Chinese)
    })
    public BooleanSetting wallHack = new BooleanSetting(false);

    private final Map<Position, BlockState> blockMap = new ConcurrentHashMap<>();
    private final List<BlockPos> uncheckedList = new CopyOnWriteArrayList<>();
    private final List<BlockPos> checkedList = new CopyOnWriteArrayList<>();

    public XrayDiamond() {
        registerSetting(wallHack);
    }
    @Override
    public void onDisable() {
        mc.levelRenderer.allChanged();
    }

    @Override
    public void onEnable() {
        blockMap.clear();
        uncheckedList.clear();
        checkedList.clear();
        mc.levelRenderer.allChanged();
    }

    @EventTarget
    public void onLivingUpdate(EventUpdate event) {
        updatePacketBlockFinding(13);
    }

    @EventTarget
    public void onWorld(EventWorld event) {
        toggle();
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        final Packet<?> packet = event.getPacket();

        if (packet instanceof ClientboundBlockUpdatePacket S23) {
            checkedList.add(S23.getPos());
            blockMap.put(new Position(S23.getPos()), S23.getBlockState());
        }

        if (packet instanceof ClientboundSectionBlocksUpdatePacket S22) {
            S22.runUpdates((pos, state) -> {
                checkedList.add(pos);
                blockMap.put(new Position(pos), state);
            });
        }
    }

    @EventTarget
    public void onRenderBlockSurface(EventBlockSurfaceRender event) {
        if (wallHack.getValue()) event.setCancelled(true);
    }

    @EventTarget
    public void onRender3D(EventRender3D event) {
        blockMap.forEach((pos, state) -> {
            if (state.getBlock() != Blocks.AIR && isValid(state)) {
                BlockPos blockPos = pos.getAsBlockPos();

                if (mc.level.getBlockState(blockPos).getBlock() == Blocks.AIR) return;

                BlockState actualState = mc.level.getBlockState(blockPos);
                Color color = getColor(state);
                int colorRGB = color.getRGB();

                render3DBlockBoundingBoxAtPos(
                        event.getPoseStack(),
                        blockPos,
                        actualState,
                        colorRGB,
                        true,
                        true,
                        0.3F
                );
            }
        });
    }

    public void render3DBlockBoundingBoxAtPos(PoseStack poseStack, BlockPos pos, BlockState state, int color, boolean fillMode, boolean wireframeMode, float fillAlpha) {
        if (state.isAir()) return;

        VoxelShape shape = state.getShape(mc.level, pos);
        if (shape.isEmpty()) return;

        Camera camera = mc.gameRenderer.getMainCamera();
        Vec3 camPos = camera.getPosition();

        double x = pos.getX() - camPos.x;
        double y = pos.getY() - camPos.y;
        double z = pos.getZ() - camPos.z;

        poseStack.pushPose();
        poseStack.translate(x, y, z);

        for (AABB aabb : shape.toAabbs()) {
            float minX = (float) aabb.minX;
            float maxX = (float) aabb.maxX;
            float minY = (float) aabb.minY;
            float maxY = (float) aabb.maxY;
            float minZ = (float) aabb.minZ;
            float maxZ = (float) aabb.maxZ;

            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            RenderSystem.disableDepthTest();
            RenderSystem.setShader(GameRenderer::getPositionColorShader);

            if (fillMode) {
                drawFilledBoundingBox(poseStack, minX, maxX, minY, maxY, minZ, maxZ, color, fillAlpha);
            }

            if (wireframeMode) {
                RenderSystem.disableDepthTest();
                RenderSystem.defaultBlendFunc();
                drawBoundingBox(poseStack, minX, maxX, minY, maxY, minZ, maxZ, color);
            }
        }

        RenderSystem.enableDepthTest();
        poseStack.popPose();
    }

    private void drawFilledBoundingBox(PoseStack poseStack, float minX, float maxX, float minY, float maxY, float minZ, float maxZ,
                                       int startColor, float alpha) {
        Matrix4f matrix = poseStack.last().pose();
        float red = (float)(startColor >> 16 & 255) / 255.0F;
        float green = (float)(startColor >> 8 & 255) / 255.0F;
        float blue = (float)(startColor & 255) / 255.0F;

        BufferBuilder bufferBuilder = Tesselator.getInstance().getBuilder();
        bufferBuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);

        bufferBuilder.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).endVertex();

        BufferUploader.drawWithShader(bufferBuilder.end());
    }

    private void drawBoundingBox(PoseStack poseStack, float minX, float maxX, float minY, float maxY, float minZ, float maxZ,
                                 int startColor) {
        Matrix4f matrix = poseStack.last().pose();
        float red = (float)(startColor >> 16 & 255) / 255.0F;
        float green = (float)(startColor >> 8 & 255) / 255.0F;
        float blue = (float)(startColor & 255) / 255.0F;
        float alpha = (float)(startColor >> 24 & 255) / 255.0F;

        BufferBuilder bufferBuilder = Tesselator.getInstance().getBuilder();
        bufferBuilder.begin(VertexFormat.Mode.DEBUG_LINES, DefaultVertexFormat.POSITION_COLOR);

        bufferBuilder.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, minY, minZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, minZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, maxX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, maxX, maxY, maxZ).color(red, green, blue, alpha).endVertex();

        bufferBuilder.vertex(matrix, minX, minY, maxZ).color(red, green, blue, alpha).endVertex();
        bufferBuilder.vertex(matrix, minX, maxY, maxZ).color(red, green, blue, alpha).endVertex();

        BufferUploader.drawWithShader(bufferBuilder.end());
    }

    private Color getColor(BlockState state) {
        Block block = state.getBlock();

        if (block == Blocks.DIAMOND_ORE || block == Blocks.DEEPSLATE_DIAMOND_ORE) {
            return new Color(0, 255, 255);
        }
        return Color.WHITE;
    }

    private boolean isSurface(BlockPos pos) {
        return (mc.level.getBlockState(pos.above()).getBlock() == Blocks.AIR || mc.level.getBlockState(pos.above()).getBlock() == Blocks.WATER)
                || (mc.level.getBlockState(pos.below()).getBlock() == Blocks.AIR || mc.level.getBlockState(pos.below()).getBlock() == Blocks.WATER)
                || (mc.level.getBlockState(pos.south()).getBlock() == Blocks.AIR || mc.level.getBlockState(pos.south()).getBlock() == Blocks.WATER)
                || (mc.level.getBlockState(pos.north()).getBlock() == Blocks.AIR || mc.level.getBlockState(pos.north()).getBlock() == Blocks.WATER)
                || (mc.level.getBlockState(pos.east()).getBlock() == Blocks.AIR || mc.level.getBlockState(pos.east()).getBlock() == Blocks.WATER)
                || (mc.level.getBlockState(pos.west()).getBlock() == Blocks.AIR || mc.level.getBlockState(pos.west()).getBlock() == Blocks.WATER);
    }

    private boolean isValid(BlockState state) {
        Block block = state.getBlock();
        return block == Blocks.DIAMOND_ORE || block == Blocks.DEEPSLATE_DIAMOND_ORE;
    }

    public void updatePacketBlockFinding(int range) {
        final int playerX = (int)mc.player.getX();
        final int playerY = (int)mc.player.getY();
        final int playerZ = (int)mc.player.getZ();

        Multithreading.runAsync(() -> {
            for (int x = -range; x <= range; x++) {
                for (int y = -range; y <= range; y++) {
                    for (int z = -range; z <= range; z++) {
                        int blockX = playerX + x;
                        int blockY = playerY + y;
                        int blockZ = playerZ + z;

                        BlockPos blockPos = new BlockPos(blockX, blockY, blockZ);

                        if (!mc.level.isLoaded(blockPos))
                            continue;

                        if (isSurface(blockPos))
                            continue;

                        Block block = mc.level.getBlockState(blockPos).getBlock();
                        if (!(block == Blocks.COAL_ORE || block == Blocks.DEEPSLATE_COAL_ORE
                                || block == Blocks.COPPER_ORE || block == Blocks.DEEPSLATE_COPPER_ORE
                                || block == Blocks.IRON_ORE || block == Blocks.DEEPSLATE_IRON_ORE
                                || block == Blocks.GOLD_ORE || block == Blocks.DEEPSLATE_GOLD_ORE
                                || block == Blocks.LAPIS_ORE || block == Blocks.DEEPSLATE_LAPIS_ORE
                                || block == Blocks.DIAMOND_ORE || block == Blocks.DEEPSLATE_DIAMOND_ORE
                                || block == Blocks.REDSTONE_ORE || block == Blocks.DEEPSLATE_REDSTONE_ORE
                                || block == Blocks.EMERALD_ORE || block == Blocks.DEEPSLATE_EMERALD_ORE
                                || block == Blocks.NETHER_QUARTZ_ORE
                                || block == Blocks.ANCIENT_DEBRIS))
                            continue;

                        boolean canDetect = true;

                        for (BlockPos pos : uncheckedList) {
                            if (isSamePos(pos, blockPos)) {
                                canDetect = false;
                                break;
                            }
                        }

                        if (!canDetect) continue;

                        for (BlockPos pos : checkedList) {
                            if (isSamePos(pos, blockPos)) {
                                canDetect = false;
                                break;
                            }
                        }

                        if (!canDetect) continue;

                        uncheckedList.add(blockPos);
                    }
                }
            }
        });
    }

    private boolean isSamePos(BlockPos pos1, BlockPos pos2) {
        return Mth.floor(pos1.getX()) == Mth.floor(pos2.getX()) && Mth.floor(pos1.getY()) == Mth.floor(pos2.getY()) && Mth.floor(pos1.getZ()) == Mth.floor(pos2.getZ());
    }

    public static class Position {
        private final int x, y, z;

        public Position(BlockPos pos) {
            this.x = pos.getX();
            this.y = pos.getY();
            this.z = pos.getZ();
        }

        public BlockPos getAsBlockPos() {
            return new BlockPos(x, y, z);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            Position position = (Position) obj;
            return x == position.x && y == position.y && z == position.z;
        }

        @Override
        public int hashCode() {
            int result = x;
            result = 31 * result + y;
            result = 31 * result + z;
            return result;
        }
    }
}

