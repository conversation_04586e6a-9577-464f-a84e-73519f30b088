package com.leave.ink.features.hud.dynamicIsland;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.hud.EventSkiaProcess;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.utils.animation.impl.ContinualAnimation;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;
import com.leave.ink.utils.animation.impl.ease.EaseBackIn;
import com.leave.ink.utils.animation.impl.ease.EaseInOutQuad;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.client.Minecraft;

import java.awt.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.ListIterator;

public class DynamicIsland implements IMinecraft {
    public Animation panelAnimation = new DecelerateAnimation(250, 1);
    public ContinualAnimation heightAnimation = new ContinualAnimation();
    public ContinualAnimation widthAnimation = new ContinualAnimation();
    private final List<DynamicElement> taskQueue = new ArrayList<>();
    private DynamicElement currentElement = null;
    private DynamicElement lastElement = null;
    private DynamicElement pendingElement = null;
    private float prevShiftW = 0, prevShiftH = 0;

    private boolean isSwitching = false;
    private long switchStartTime = 0;
    private SwitchPhase switchPhase = SwitchPhase.NONE;

    private enum SwitchPhase {
        NONE,
        SHRINKING,
        EXPANDING
    }

    public DynamicIsland() {
        EventManager.register(this);
    }

    public void addTask(DynamicElement element) {
        //panelAnimation = new EaseBackIn(250, 1, 1);
        taskQueue.removeIf(e -> e.getClass().equals(element.getClass()));

        taskQueue.add(element);

        DynamicElement priority = taskQueue.stream()
                .max(Comparator.comparingInt(e -> e.priority))
                .orElse(null);

        if (element.priority < priority.priority) {
            element.state = DynamicElement.State.RETENTION;
            element.timer.reset();
            return;
        }

        if (currentElement != null && currentElement != element) {
            currentElement.startFadeOut();
            currentElement.state = DynamicElement.State.OUT;
            startElementSwitch(element);
        } else {
            currentElement = element;
            element.resetInternalState();
            element.fadeAnimation.reset();
            element.startFadeIn();
        }

        panelAnimation.setDirection(Direction.FORWARDS);
        element.state = DynamicElement.State.IN;
        element.timer.reset();
    }


    private void startElementSwitch(DynamicElement newElement) {
        if (currentElement != null) {
            lastElement = currentElement;
            pendingElement = newElement;
            prevShiftW = (float) lastElement.getShiftWidth();
            prevShiftH = (float) lastElement.getShiftHeight();
            currentElement.startFadeOut();
            switchPhase = SwitchPhase.SHRINKING;
            currentElement = null;
            newElement.resetInternalState();
        } else {
            currentElement = newElement;
            currentElement.resetInternalState();
            currentElement.fadeAnimation.reset();
            currentElement.startFadeIn();
            prevShiftW = 0;
            prevShiftH = 0;
            switchPhase = SwitchPhase.EXPANDING;
            widthAnimation.reset();
            heightAnimation.reset();
        }

        isSwitching = true;
        switchStartTime = System.currentTimeMillis();
    }

    @EventTarget
    public void onSkia(EventSkiaProcess eventSkiaProcess) {
        if (Utils.isNull() || !ElementsHud.dynamicIsland.getValue()) return;

        DynamicElement priority = taskQueue.stream().max(Comparator.comparingInt(e -> e.priority)).orElse(null);

        panelAnimation.setDirection(taskQueue.isEmpty() ? Direction.BACKWARDS : Direction.FORWARDS);

        if (priority != null && currentElement != priority) {
            startElementSwitch(priority);
        }

        float shiftW = 0, shiftH = 0;
        if (isSwitching) {
            long elapsed = System.currentTimeMillis() - switchStartTime;
            float phaseDuration = 200.0f;
            
            if (switchPhase == SwitchPhase.SHRINKING) {
                if (elapsed >= phaseDuration) {
                    switchPhase = SwitchPhase.EXPANDING;
                    currentElement = pendingElement;
                    if (currentElement != null) {
                        currentElement.resetInternalState();
                        currentElement.fadeAnimation.reset();
                        currentElement.startFadeIn();
                    }
                    lastElement = null;
                    pendingElement = null;
                    prevShiftW = 0;
                    prevShiftH = 0;
                    widthAnimation.reset();
                    heightAnimation.reset();
                    
                    switchStartTime = System.currentTimeMillis();
                    elapsed = 0;
                }
                
                if (elapsed < phaseDuration) {
                    float t = Math.min(1.0f, elapsed / phaseDuration);
                    float progress = t * t * (3.0f - 2.0f * t);
                    shiftW = prevShiftW * (1.0f - progress);
                    shiftH = prevShiftH * (1.0f - progress);
                }
            }
            
            if (switchPhase == SwitchPhase.EXPANDING) {
                if (elapsed >= phaseDuration) {
                    isSwitching = false;
                    switchPhase = SwitchPhase.NONE;
                    shiftW = currentElement != null ? (float) currentElement.getShiftWidth() : 0;
                    shiftH = currentElement != null ? (float) currentElement.getShiftHeight() : 0;
                } else {
                    float t = Math.min(1.0f, elapsed / phaseDuration);
                    float progress = t * t * (3.0f - 2.0f * t); // smooth step
                    float targetW = currentElement != null ? (float) currentElement.getShiftWidth() : 0;
                    float targetH = currentElement != null ? (float) currentElement.getShiftHeight() : 0;
                    shiftW = targetW * progress;
                    shiftH = targetH * progress;
                }
            }
        } else {
            shiftW = currentElement != null ? (float) currentElement.getShiftWidth() : 0;
            shiftH = currentElement != null ? (float) currentElement.getShiftHeight() : 0;
        }

        widthAnimation.animate(shiftW, 40);
        heightAnimation.animate(shiftH, 40);

        renderDynamicIsland(eventSkiaProcess.getCanvasStack(), (float) panelAnimation.getOutput(), widthAnimation.getOutput(), heightAnimation.getOutput());
        processTaskQueue();
    }

    private void renderDynamicIsland(CanvasStack canvasStack, float panelOutput, float shiftW, float shiftH) {
        String clientName = Main.NAME;

        String timeText = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        String fpsText = ObfuscationReflectionHelper.getPrivateValue(Minecraft.class, mc, "fps") + " FPS";
        String titleText = ("  - " + fpsText+ " - " + timeText);

        float baseWidth = SkiaFontManager.getBigFont(22).getWidth(Main.NAME) + SkiaFontManager.getClassicFont(20).getWidth("  - 0 FPS - 00:00:00") + 16 + 20;
        float baseHeight = 25;
        float x = mc.getWindow().getGuiScaledWidth() / 2f - baseWidth / 2f;
        float y = 10;

        float width = baseWidth + panelOutput * shiftW * 2;
        float height = baseHeight + panelOutput * shiftH;
        float offsetX = panelOutput * shiftW;
        float posX = x - offsetX;

        SkiaRender.drawRoundedRectWithShadow(canvasStack, posX, y, width, height, 44);

        if (ElementsHud.blur.getValue()) {
            canvasStack.push();
            SkiaRender.scissorRoundedRect(canvasStack, posX, y, width, height, 44, ClipMode.INTERSECT);
            SkiaRender.drawBlurRect(canvasStack, posX, y, width, height, 44, 10);
            canvasStack.pop();
        }

        SkiaRender.drawRoundedRect(canvasStack, posX, y, width, height, 44, new Color(0, 0, 0, 110).getRGB());

        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, posX - 1, y - 1, width + 2, height + 2, 44, ClipMode.INTERSECT);


        canvasStack.canvas.saveLayerAlpha(null, (int) (255 * (currentElement != null ? (1.0f - panelOutput) : 1.0f)));
        SkiaFontManager.getBigFont(22).drawTextWithBlur(canvasStack, clientName, x + 27, y + 6, AuraSyncHud.getColor(0, 0),10 * (currentElement != null ? ( panelOutput) : 0));
        SkiaFontManager.getClassicFont(20).drawTextWithBlur(canvasStack, titleText, x + 22 + SkiaFontManager.getBigFont(22).getWidth(clientName), y + 6, Color.WHITE.getRGB(),10 * (currentElement != null ? ( panelOutput) : 0));
        SkiaRender.drawTTFLogo(canvasStack, x + 7, y + 3, 30, AuraSyncHud.getColor(0, 0));
        canvasStack.pop();

        if (currentElement != null) {
            if (!(panelAnimation.isDone() && panelAnimation.getDirection() == Direction.BACKWARDS)) {
                currentElement.drawLeftElement(canvasStack, posX, y, width, height);
                currentElement.drawRightElement(canvasStack, posX + width, y, width, height);
            }
        }
        canvasStack.pop();
    }

    private void processTaskQueue() {
        ListIterator<DynamicElement> iterator = taskQueue.listIterator();

        while (iterator.hasNext()) {
            DynamicElement element = iterator.next();

            updateElementProperties(element);

            switch (element.state) {
                case IN -> {
                    element.state = DynamicElement.State.RETENTION;
                    element.timer.reset();
                }

                case RETENTION -> {
                    if (element.timer.hasTimeElapsed(element.getRetentionTime(), true)) {
                        element.state = DynamicElement.State.OUT;
                        element.timer.reset();
                        element.startFadeOut();
                    }
                }

                case OUT -> {
                    if (!element.sticky) {
                        if (element.fadeAnimation.isDone() &&
                                element.fadeAnimation.getDirection() == Direction.BACKWARDS) {
                            iterator.remove();
                        }
                    } else {
                        element.state = DynamicElement.State.RETENTION;
                        element.timer.reset();
                        element.startFadeIn();
                    }
                }
            }
        }
    }

    private void updateElementProperties(DynamicElement element) {
        float baseWidth = SkiaFontManager.getBigFont(22).getWidth(Main.NAME) + SkiaFontManager.getClassicFont(20).getWidth("  - 0 FPS - 00:00:00") + 16 + 20;
        float baseHeight = 25;
        float x = mc.getWindow().getGuiScaledWidth() / 2f - baseWidth / 2f;
        float y = 10;

        element.baseWidth = baseWidth;
        element.baseX = x;
        element.baseY = y;
        element.baseHeight = baseHeight;
    }
}
