package com.leave.ink.features.hud.main;

import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import net.minecraft.client.gui.GuiGraphics;

import java.util.Arrays;

public class NotificationHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Mode", language = Language.English),
            @Text(label = "模式", language = Language.Chinese)
    })
    public static final ModeSetting mode = new ModeSetting("Simple", Arrays.asList("Simple","Simple2" ,"NVIDIA","None"));

    public NotificationHud() {
        super("Notification",0,0,0,0);
        registerSetting(mode);
    }

    @Override
    public boolean isMainElement() {
        return true;
    }

    @Override
    public void inGuiDraw(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {}

    @Override
    protected void processDraw(CanvasStack canvasStack) {}
}
