package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;

import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.client.CommonEvents;
import io.github.humbleui.skija.ClipMode;

import java.awt.*;

public class SessionsHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Delete", language = Language.English),
            @Text(label = "删除", language = Language.Chinese)
    })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };
    public SessionsHud() {
        super("Sessions",0,0,80,50);
        registerSetting(delete);
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        String playerName = mc.player.getName().getString();
        String text2 = CommonEvents.kill + " Kills - "+ String.format("%.2f", CommonEvents.dead == 0 ? (float) CommonEvents.kill : (float) CommonEvents.kill / CommonEvents.dead) +" K/D";
        setWidth(Math.max(Math.max(80, SkiaFontManager.getDefaultFont18().getWidth(text2) + 40), SkiaFontManager.getDefaultFont14().getWidth(playerName) + 40));
        int width = (int)getWidth();
        int height = (int)getHeight();
        canvasStack.push();
        SkiaRender.drawRoundedRectWithShadow(canvasStack, 0, 0, width, height, 8);
        SkiaRender.scissorRoundedRect(canvasStack, 0, 0, width, height, 8, ClipMode.INTERSECT);
        if(ElementsHud.blur.getValue()) {
            SkiaRender.drawBlurRect(canvasStack, 0, 0, width, height, 8, 12);
        }
        SkiaRender.drawRoundedRect(canvasStack, 0, 0, width, height, 8, new Color(24, 24, 24, 110).getRGB());
        SkiaRender.drawRoundedRect(canvasStack, 4, 5, 2, 8, 1, AuraSyncHud.getColor(0,0));
        SkiaFontManager.getBigFont(18).drawText(canvasStack, "Sessions", 8, 4, AuraSyncHud.getColor(0,0));
        SkiaRender.drawPlayerHead(canvasStack, mc.player, 4, 16, 30, 30, 10);
        String time = CommonEvents.H + "h " + CommonEvents.M + "m " + CommonEvents.S + "s";

        SkiaFontManager.getDefaultFont18().drawText(canvasStack, playerName, 38, 17, -1);
        SkiaFontManager.getDefaultFont14().drawText(canvasStack, text2, 38, 28, -1);
        SkiaFontManager.getDefaultFont14().drawText(canvasStack, time, 38, 37, -1);

        canvasStack.pop();
    }

}
