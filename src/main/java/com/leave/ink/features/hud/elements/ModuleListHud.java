package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.Anchor;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.*;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.animation.Direction;
import com.mojang.blaze3d.systems.RenderSystem;
import io.github.humbleui.skija.ClipMode;
import io.github.humbleui.skija.FontMetrics;

import java.awt.*;
import java.util.Comparator;
import java.util.List;

import static com.leave.ink.features.hud.main.AuraSyncHud.colorModeValue;

public class ModuleListHud extends AbsHudElement {
    @SettingInfo(name = {@Text(label = "Background", language = Language.English), @Text(label = "背景", language = Language.Chinese)})
    private final BooleanSetting background = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Important", language = Language.English), @Text(label = "重要", language = Language.Chinese)})
    private final BooleanSetting important = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Lowercase", language = Language.English), @Text(label = "小写", language = Language.Chinese)})
    public static final BooleanSetting lower = new BooleanSetting(false);

    @SettingInfo(name = {@Text(label = "Spacing", language = Language.English), @Text(label = "间距", language = Language.Chinese)})
    public static final NumberSetting spacing = new NumberSetting(0, 0, 5, "#.0");

    @SettingInfo(name = {@Text(label = "Row Height", language = Language.English), @Text(label = "行高", language = Language.Chinese)})
    public static final NumberSetting rowHeight = new NumberSetting(16, 11, 22, "#");

    @SettingInfo(name = {@Text(label = "Delete", language = Language.English), @Text(label = "删除", language = Language.Chinese)})
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };

    public ModuleListHud() {
        super("ModuleList", 0.77d, 0.02f, 100, 100);
        registerSetting(background, important, lower, spacing, rowHeight, delete);
    }

    @Override
    public void processDraw(CanvasStack canvasStack) {
        boolean isLeftSide = getAnchor() == Anchor.TOP_LEFT || getAnchor() == Anchor.BOTTOM_LEFT;

        List<Module> modules = Main.INSTANCE.moduleManager.getModules();

        modules.sort(Comparator.<Module>comparingDouble(m -> {
            String name = getLowerCase(m.getName() + (m.getTag() != null ? " §7" + m.getTag() : ""));
            return SkiaFontManager.getHarmonyFont(18).getWidth(name);
        }).reversed());

        float maxWidth = 0;
        if (!modules.isEmpty()) {
            Module m = modules.get(0);
            String longestString = getLowerCase(m.getName() + (m.getTag() != null ? " §7" + m.getTag() : ""));
            maxWidth = SkiaFontManager.getHarmonyFont(18).getWidth(longestString);
        }

        int y = -2;
        double totalFontHeight = 0;

        for (Module module : modules) {
            if (important.getValue() && (module.getCategory() == Category.Hud || module.getCategory() == Category.Render)) continue;

            module.animation.setDirection(module.isEnable() ? Direction.FORWARDS : Direction.BACKWARDS);

            if (!(module.animation.getDirection() == Direction.BACKWARDS && module.animation.isDone())) {
                String string = getLowerCase(module.getName() + (module.getTag() != null ? " §7" + module.getTag() : ""));

                float textWidth = SkiaFontManager.getHarmonyFont(18).getWidth(string);
                float animatedHeight = rowHeight.getValue().floatValue() * (float) module.animation.getOutput();

                float rectX;
                float rectY = (float) (y + 4);
                float rectWidth = textWidth + 4;
                float rectHeight = animatedHeight;

                if (isLeftSide) {
                    rectX = 0;
                } else {
                    rectX = maxWidth - rectWidth;
                }

                scale(canvasStack, isLeftSide, module, rectX, rectY, rectWidth);

                if (background.getValue()) {
                    if (ElementsHud.blur.getValue()) {
                        SkiaRender.drawBlurRect(canvasStack, 0, 0, rectWidth, rectHeight, 0, 10);
                    }
                    SkiaRender.drawRect(canvasStack, 0, 0, rectWidth, rectHeight, new Color(24, 24, 24, 120).getRGB());
                }

                canvasStack.pop();
                y += (int) (animatedHeight + spacing.getValue().floatValue());
                totalFontHeight += (animatedHeight + spacing.getValue().floatValue());
            }
        }

        y = -2;
        int[] counter = new int[]{1};

        for (int i = 0; i < modules.size(); i++) {
            Module module = modules.get(i);
            if (important.getValue() && (module.getCategory() == Category.Hud || module.getCategory() == Category.Render)) continue;

            if (!(module.animation.getDirection() == Direction.BACKWARDS && module.animation.isDone())) {
                String string = getLowerCase(module.getName() + (module.getTag() != null ? " §7" + module.getTag() : ""));

                float textX;
                float animatedHeight = rowHeight.getValue().floatValue() * (float) module.animation.getOutput();
                float rectY = (float) (y + 4);

                FontMetrics metrics = SkiaFontManager.getHarmonyFont(18).font.getMetrics();
                float backgroundCenterY = rectY + (animatedHeight / 2f);
                float textY = backgroundCenterY - (metrics.getDescent() - metrics.getAscent() / 2f) + metrics.getDescent() + 3f;

                float textWidth = SkiaFontManager.getHarmonyFont(18).getWidth(string);

                float rectX;
                float rectWidth = textWidth + 4;

                if (isLeftSide) {
                    rectX = 0;
                } else {
                    rectX = maxWidth - rectWidth;
                }

                textX = rectX + 2.0f;

                scale(canvasStack, isLeftSide, module, textX, textY, textWidth);

                SkiaFontManager.getHarmonyFont(18).drawText(canvasStack, string, (isLeftSide ? 1 : 0.5F), 0, AuraSyncHud.getColor(colorModeValue.getValue().equals("Fade") ? i : counter[0], y), ElementsHud.fontShadow.getValue());
                canvasStack.pop();

                counter[0] = counter[0] + 1;
                RenderSystem.disableScissor();

                y += (int) (animatedHeight + spacing.getValue().floatValue());
            }
        }

        setHeight(totalFontHeight + 4);
        setWidth(maxWidth + 4.0f);
    }

    private void scale(CanvasStack canvasStack, boolean isLeftSide, Module m, float textX, float textY, float textWidth) {
        canvasStack.push();
        if (isLeftSide) {
            canvasStack.translate(textX, textY);
            canvasStack.canvas.scale((float) m.animation.getOutput(), (float) m.animation.getOutput());
        } else {
            canvasStack.translate(textX + textWidth, textY);
            canvasStack.canvas.scale((float) m.animation.getOutput(), (float) m.animation.getOutput());
            canvasStack.translate(-textWidth, 0);
        }
    }

    public static String getLowerCase(String text) {
        return lower.getValue() ? text.toLowerCase() : text;
    }
}
