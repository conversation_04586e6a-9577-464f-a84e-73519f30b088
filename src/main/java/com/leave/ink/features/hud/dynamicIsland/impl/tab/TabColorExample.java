package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import java.awt.*;

/**
 * Tab栏颜色功能使用示例
 * 展示如何使用新的颜色识别功能
 */
public class TabColorExample {
    
    /**
     * 演示颜色解析功能
     */
    public static void demonstrateColorParsing() {
        System.out.println("=== Tab栏颜色识别功能演示 ===\n");
        
        // 演示不同类型的颜色代码解析
        demonstrateSectionColors();
        demonstrateHexColors();
        demonstrateSpecialCases();
        
        System.out.println("=== 演示完成 ===");
    }
    
    /**
     * 演示§颜色代码解析
     */
    private static void demonstrateSectionColors() {
        System.out.println("1. §颜色代码解析演示:");
        System.out.println("   这些是Minecraft标准的颜色代码，常见于各种服务器");
        
        String[] examples = {
            "§c[Admin] RedPlayer",      // 红色管理员
            "§a[VIP] GreenPlayer",      // 绿色VIP
            "§b[Member] BluePlayer",    // 蓝色成员
            "§e[Guest] YellowPlayer",   // 黄色访客
            "§f[Default] WhitePlayer"   // 白色默认
        };
        
        for (String example : examples) {
            Color color = TabColorUtils.extractColorFromString(example);
            System.out.println(String.format("   输入: %-25s -> 颜色: %s", 
                "\"" + example + "\"", formatColor(color)));
        }
        System.out.println();
    }
    
    /**
     * 演示十六进制颜色解析
     */
    private static void demonstrateHexColors() {
        System.out.println("2. 十六进制颜色解析演示:");
        System.out.println("   这些是1.16+版本支持的自定义颜色，可以显示任意RGB颜色");
        
        String[] examples = {
            "§x§f§f§0§0§0§0[Owner] RedOwner",        // 纯红色 #FF0000
            "§x§0§0§f§f§0§0[Mod] GreenMod",          // 纯绿色 #00FF00
            "§x§f§f§a§5§0§0[Helper] OrangeHelper",   // 橙色 #FFA500
            "§x§8§0§0§0§8§0[Builder] PurpleBuilder", // 紫色 #800080
            "§x§f§f§d§7§0§0[Donator] GoldDonator"    // 金色 #FFD700
        };
        
        for (String example : examples) {
            Color color = TabColorUtils.extractColorFromString(example);
            System.out.println(String.format("   输入: %-35s -> 颜色: %s", 
                "\"" + example.substring(0, Math.min(example.length(), 30)) + "...\"", 
                formatColor(color)));
        }
        System.out.println();
    }
    
    /**
     * 演示特殊情况处理
     */
    private static void demonstrateSpecialCases() {
        System.out.println("3. 特殊情况处理演示:");
        System.out.println("   展示如何处理各种边缘情况和特殊格式");
        
        String[] examples = {
            "NormalPlayer",                    // 无颜色代码
            "§c§lBoldRedPlayer",              // 多个格式代码
            "Prefix§cRed§rReset§aGreen",      // 多个颜色切换
            "&cPluginColorPlayer",            // &颜色代码（插件格式）
            "§x§g§g§g§g§g§g InvalidHex",      // 无效的十六进制
            "§zInvalidCode Player",           // 无效的颜色代码
            "",                               // 空字符串
            "§"                               // 只有§符号
        };
        
        for (String example : examples) {
            Color color = TabColorUtils.extractColorFromString(example);
            System.out.println(String.format("   输入: %-25s -> 颜色: %s", 
                "\"" + example + "\"", formatColor(color)));
        }
        System.out.println();
    }
    
    /**
     * 格式化颜色输出
     */
    private static String formatColor(Color color) {
        if (color == null) {
            return "null (将使用默认颜色)";
        }
        
        // 检查是否为默认颜色
        if (TabColorUtils.isDefaultColor(color)) {
            return String.format("RGB(%d, %d, %d) [默认白色]", 
                color.getRed(), color.getGreen(), color.getBlue());
        }
        
        return String.format("RGB(%d, %d, %d) [#%06X]", 
            color.getRed(), color.getGreen(), color.getBlue(), 
            color.getRGB() & 0xFFFFFF);
    }
    
    /**
     * 演示在游戏中的实际使用场景
     */
    public static void demonstrateGameUsage() {
        System.out.println("=== 游戏中的实际使用场景 ===\n");
        
        System.out.println("在游戏中，这个功能会自动:");
        System.out.println("1. 从PlayerInfo.getTabListDisplayName()中提取颜色");
        System.out.println("2. 如果没有显示名称，则从PlayerTeam中获取团队颜色");
        System.out.println("3. 如果都没有，则使用基于游戏模式的默认颜色");
        System.out.println();
        
        System.out.println("支持的服务器类型:");
        System.out.println("- 原版Minecraft服务器");
        System.out.println("- Bukkit/Spigot/Paper服务器");
        System.out.println("- 使用颜色插件的服务器 (如EssentialsX, LuckPerms等)");
        System.out.println("- 支持团队系统的服务器");
        System.out.println();
        
        System.out.println("效果:");
        System.out.println("- Tab栏中的玩家名字会显示与原版相同的颜色");
        System.out.println("- 管理员、VIP等特殊玩家会显示对应的颜色");
        System.out.println("- 团队成员会显示团队颜色");
        System.out.println("- 普通玩家显示基于游戏模式的颜色");
    }
    
    /**
     * 主方法 - 运行所有演示
     */
    public static void main(String[] args) {
        demonstrateColorParsing();
        System.out.println();
        demonstrateGameUsage();
    }
}
