package com.leave.ink.features.hud;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.hud.EventSkiaProcess;
import com.leave.ink.features.module.modules.settings.ClientSetting;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import lombok.Getter;
import lombok.Setter;
import net.minecraft.client.gui.GuiGraphics;


import java.awt.*;

@Setter @Getter
public abstract class AbsHudElement extends SettingManager implements IMinecraft {
    private final double[][] posProportion = new double[4][2];
    private final double[] centerProportion = new double[2];
    private Anchor anchor = Anchor.TOP_LEFT;
    private final String name;
    private int index;

    protected double XProportion = 0;
    protected double YProportion = 0;
    protected double height = 100;
    protected double width = 100;

    private boolean dragging = false;
    public double tempHeight = -1;
    public double tempWidth = -1;
    private double tempX;
    private double tempY;

    public AbsHudElement(String name) {
        this.name = name;
        index = Main.INSTANCE.hudManager.getHudElements().size();
    }

    public AbsHudElement(String name, double XProportion, double YProportion, double width, double height) {
        this(name);
        this.XProportion = XProportion;
        this.YProportion = YProportion;
        this.width = width;
        this.height = height;
        EventManager.register(this);

    }

    public void onDrawing2D(EventSkiaProcess eventRender2D) {
        CanvasStack canvasStack = eventRender2D.getCanvasStack();

        tickPos();
        canvasStack.push();
        canvasStack.translate((float) getDrawX(), (float) getDrawY());
        processDraw(canvasStack);
        canvasStack.pop();
    }

    protected abstract void processDraw(CanvasStack canvasStack);

    private void tickPos() {
        if(tempWidth == -1) tempWidth = mc.getWindow().getGuiScaledWidth();
        if (tempHeight == -1) tempHeight = mc.getWindow().getGuiScaledHeight();

        adjustPositionToBounds();

        //右上X Y
        posProportion[0][0] = XProportion + width / tempWidth;
        posProportion[0][1] = YProportion;
        //左上X Y
        posProportion[1][0] = XProportion;
        posProportion[1][1] = YProportion;
        //左下
        posProportion[2][0] = XProportion;
        posProportion[2][1] = YProportion + height / tempHeight;
        //右下
        posProportion[3][0] = XProportion + width / tempWidth;
        posProportion[3][1] = YProportion + height / tempHeight;

        centerProportion[0] = XProportion + (posProportion[0][0] - posProportion[1][0]) / 2f;
        centerProportion[1] = YProportion + (posProportion[3][1] - posProportion[0][1]) / 2f;

        if (centerProportion[0] >= 0.5f)
            if(centerProportion[1] >= 0.5f)
                anchor = Anchor.BOTTOM_RIGHT;//右下
            else
                anchor = Anchor.TOP_RIGHT;//右上
        else
            if(centerProportion[1] >= 0.5f)
                anchor = Anchor.BOTTOM_LEFT;//左下
            else
                anchor = Anchor.TOP_LEFT;//左上
    }

    public void inGuiDraw(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {
        PoseStack poseStack = guiGraphics.pose();

        if(dragging && isActive()) {
            this.XProportion = (this.tempX + mouseX) / mc.getWindow().getGuiScaledWidth();
            this.YProportion = (this.tempY + mouseY) / mc.getWindow().getGuiScaledHeight();
        }

        float[] hsb = new float[3];
        Color guiColor = ClientSetting.color.getValue();
        Color.RGBtoHSB(guiColor.getRed(), guiColor.getGreen(), guiColor.getBlue(), hsb);
        float brightness = isActive() ? 1.0f : 0.5f;
        hsb[2] = brightness;
        Color color = new Color(Color.HSBtoRGB(hsb[0], hsb[1], hsb[2]));

        if(isActive())
            RenderUtils.drawRect(poseStack, getDrawX(), getDrawY(), getDrawX() + width, getDrawY() + height, new Color(21, 21, 21, 97).getRGB());

        RenderUtils.drawOutline(guiGraphics, getDrawX(), getDrawY(), width, height, color.getRGB());
    }

    protected boolean isActive() {
        return DesignGuiScreen.selectedElement.equals(getElementName());
    }

    public void mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && isHovered(mouseX, mouseY, getDrawX(), getDrawY(), width, height)) {
            dragging = true;
            tempWidth = mc.getWindow().getGuiScaledWidth();
            tempHeight = mc.getWindow().getGuiScaledHeight();

            this.tempX = getDrawX() - mouseX;
            this.tempY = getDrawY() - mouseY;
            DesignGuiScreen.selectedElement = getElementName();//set active
        }
    }

    public boolean isMainElement() {
        return false;
    }

    public double getDrawX() {
        switch (anchor) {
            case TOP_RIGHT -> {
                return (posProportion[0][0] * mc.getWindow().getGuiScaledWidth() - width);
            }
            case BOTTOM_RIGHT -> {
                return (posProportion[3][0] * mc.getWindow().getGuiScaledWidth() - width);
            }
            default -> {
                return (posProportion[1][0] * mc.getWindow().getGuiScaledWidth());
            }
        }
    }

    public double getDrawY() {
        switch (anchor) {
            case BOTTOM_LEFT -> {
                return (posProportion[2][1] * mc.getWindow().getGuiScaledHeight() - height);
            }
            case BOTTOM_RIGHT -> {
                return (posProportion[3][1] * mc.getWindow().getGuiScaledHeight() - height);
            }
            default -> {
                return (posProportion[0][1] * mc.getWindow().getGuiScaledHeight());
            }
        }
    }

    public void mouseReleased(double p_94722_, double p_94723_, int p_94724_) {
        dragging = false;

    }

    protected void registerSetting(Setting<?>... settings) {
        try {
            registerSetting(this, settings);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isHovered(double mouseX, double mouseY, double x, double y, double width, double height) {
        return mouseX >= x && mouseX - width <= x && mouseY >= y && mouseY - height <= y;
    }

    public String getNameKey() {
        return name;
    }

    public String getElementName() {
        return name + index;
    }

    private void adjustPositionToBounds() {
        double screenWidth = mc.getWindow().getGuiScaledWidth();
        double screenHeight = mc.getWindow().getGuiScaledHeight();
        double currentX = XProportion * screenWidth;
        double currentY = YProportion * screenHeight;
        double adjustedX = currentX;
        double adjustedY = currentY;
        if (currentX + width > screenWidth) {
            adjustedX = screenWidth - width;
        }
        if (currentX < 0) {
            adjustedX = 0;
        }
        if (currentY + height > screenHeight) {
            adjustedY = screenHeight - height;
        }
        if (currentY < 0) {
            adjustedY = 0;
        }
        if (adjustedX != currentX || adjustedY != currentY) {
            XProportion = adjustedX / screenWidth;
            YProportion = adjustedY / screenHeight;
        }
    }
}
