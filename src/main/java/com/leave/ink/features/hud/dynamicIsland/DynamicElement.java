package com.leave.ink.features.hud.dynamicIsland;

import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.utils.animation.impl.ContinualAnimation;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;
import com.leave.ink.utils.timer.TimeUtils;

public abstract class DynamicElement implements IMinecraft {
    public Animation fadeAnimation = new DecelerateAnimation(300, 1, Direction.FORWARDS); // 增加到300ms，与切换时间协调
    public TimeUtils timer = new TimeUtils();

    public float baseX, baseY, baseWidth, baseHeight;
    public boolean sticky = false;
    public int priority;

    public Style style = Style.SMALL;
    public State state = State.IN;

    public String rightText;
    public String leftText;

    public DynamicElement(String left, String right) {
        this(left, right, 0); // Default = 0 (1-10)
    }

    public DynamicElement(String left, String right, int priority) {
        fadeAnimation.reset();
        timer.reset();
        this.leftText = left;
        this.rightText = right;
        this.priority = priority;
    }

    public void drawLeftElement(CanvasStack canvasStack, float x, float y, float width, float height) {
        canvasStack.push();
        canvasStack.translate(x, y);
        canvasStack.canvas.saveLayerAlpha(null, (int) (255 * (float) fadeAnimation.getOutput()));
        left(canvasStack, x, y, width, height);
        canvasStack.canvas.restore();
        canvasStack.pop();
    }

    public void drawRightElement(CanvasStack canvasStack, float x, float y, float width, float height) {
        canvasStack.push();
        canvasStack.translate(x, y);
        canvasStack.canvas.saveLayerAlpha(null, (int) (255 * (float) fadeAnimation.getOutput()));
        right(canvasStack, x, y, width, height);
        canvasStack.canvas.restore();
        canvasStack.pop();
    }

    public void startFadeIn() {
        fadeAnimation.setDirection(Direction.FORWARDS);
        fadeAnimation.reset();
    }

    public void startFadeOut() {
        fadeAnimation.setDirection(Direction.BACKWARDS);
        fadeAnimation.reset();
    }

    public void resetInternalState() {
    }

    public abstract void left(CanvasStack canvasStack, float x, float y, float width, float height);
    public abstract void right(CanvasStack canvasStack, float x, float y, float width, float height);

    public double getShiftWidth() {
        return 50;
    }

    public double getShiftHeight() {
        return 0;
    }

    public long getRetentionTime() {
        return 1000;
    }

    public enum State {
        IN,
        RETENTION,
        OUT,
        WAITING
    }


    public enum Style {
        BIG, SMALL
    }
}
