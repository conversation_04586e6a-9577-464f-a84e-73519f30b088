package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.fbo.FrameBuffers;
import com.leave.ink.ui.skija.fbo.GameFrameBuffer;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.platform.Lighting;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.world.item.ItemStack;

import java.awt.*;

public class InventoryHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊", language = Language.Chinese)
    })
    public static final BooleanSetting blur = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Delete", language = Language.English),
            @Text(label = "删除", language = Language.Chinese)
    })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };
    
    public InventoryHud() {
        super("Inventory", 0, 0, 177, 79);
        registerSetting(blur, delete);
    }
    
    @Override
    public void processDraw(CanvasStack canvasStack) {
        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer("Inventory");
        gameFrameBuffer.execute(eventRender2D -> {
            PoseStack poseStack = eventRender2D.getPoseStack();
            poseStack.pushPose();
            RenderSystem.enableDepthTest();
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            Lighting.setupFor3DItems();
            renderInventory(poseStack);
            renderInventory2(poseStack);
            renderInventory3(poseStack);
            poseStack.popPose();
        });

        //shadow
        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, 0, 0, (float)getWidth(),(float) getHeight(),8, ClipMode.DIFFERENCE);
        SkiaRender.drawRoundedRectWithShadow(canvasStack,0, 0, (float)getWidth(),(float) getHeight(),8);
        canvasStack.pop();

        if(blur.getValue()) {
            SkiaRender.drawBlurRect(canvasStack,  0,0,(float) getWidth(),(float)getHeight(), 8, 12);
        }
        SkiaRender.drawRoundedRect(canvasStack,  0,0,(float) getWidth(),(float)getHeight(), 8, new Color(24, 24, 24, 110).getRGB());
        SkiaRender.drawRoundedRect(canvasStack,  4,5,2,8, 4, AuraSyncHud.getColor(0,0));
        SkiaFontManager.getBigFont(18).drawText(canvasStack, "Inventory", 8, 4, AuraSyncHud.getColor(0,0));
        canvasStack.push();
        SkiaRender.scissorRect(canvasStack, 0, 0, (float)getWidth(), (float)getHeight(), ClipMode.INTERSECT);
        gameFrameBuffer.draw(canvasStack);
        canvasStack.pop();
    }
    
    private void renderInventory(PoseStack stack) {
        ItemStack armourStack;
        int xOffset = 4;
        for (int index = 9; index <= 17; index++) {
            armourStack = mc.player.getInventory().getItem(index);
            RenderUtils.renderItemStack(stack, armourStack, xOffset, 18);
            xOffset += 19;
        }
    }

    private void renderInventory2(PoseStack stack) {
        ItemStack armourStack;
        int xOffset = 4;
        for (int index = 18; index <= 26; index++) {
            armourStack = mc.player.getInventory().getItem(index);
            RenderUtils.renderItemStack(stack, armourStack, xOffset, 38);
            xOffset += 19;
        }
    }

    private void renderInventory3(PoseStack stack) {
        ItemStack armourStack;
        int xOffset = 4;
        for (int index = 27; index <= 35; index++) {
            armourStack = mc.player.getInventory().getItem(index);
            RenderUtils.renderItemStack(stack, armourStack, xOffset, 58);
            xOffset += 19;
        }
    }
}

