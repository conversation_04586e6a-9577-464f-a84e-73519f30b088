package com.leave.ink.features.hud.main;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventEntityJoinWorld;
import com.leave.ink.events.EventPacket;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.elements.*;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.rotation.RotationUtils;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.screens.ChatScreen;
import net.minecraft.network.protocol.game.ClientboundSetTimePacket;
import net.minecraft.util.Mth;

import java.util.Arrays;
import java.util.Objects;

public class ElementsHud extends AbsHudElement {

    @SettingInfo(name = {@Text(label = "ModuleList", language = Language.English), @Text(label = "模块列表", language = Language.Chinese)})
    public static final ButtonSetting moduleListButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(ModuleListHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Inventory", language = Language.English), @Text(label = "背包显示", language = Language.Chinese)})
    public static final ButtonSetting inventoryButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(InventoryHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Title", language = Language.English), @Text(label = "标题", language = Language.Chinese)})
    public static final ButtonSetting titleButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(TitleHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Target", language = Language.English), @Text(label = "目标显示", language = Language.Chinese)})
    public static final ButtonSetting targetButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(TargetHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Potion", language = Language.English), @Text(label = "药水显示", language = Language.Chinese)})
    public static final ButtonSetting potionButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(PotionHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "KeyBinds", language = Language.English), @Text(label = "按键显示", language = Language.Chinese)})
    public static final ButtonSetting keyBindsButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(KeyBindsHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Radar", language = Language.English), @Text(label = "雷达", language = Language.Chinese)})
    public static final ButtonSetting radarButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(RadarHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Sessions", language = Language.English), @Text(label = "信息", language = Language.Chinese)})
    public static final ButtonSetting sessionsButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(SessionsHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Scoreboard", language = Language.English), @Text(label = "计分板", language = Language.Chinese)})
    public static final ButtonSetting scoreboardButton = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.addElement(ScoreboardHud.class);
        }
    };

    @SettingInfo(name = {@Text(label = "Clear All Elements", language = Language.English), @Text(label = "清理全部组件", language = Language.Chinese)})
    public static final ButtonSetting clearAllElements = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.clear();
        }
    };

    @SettingInfo(name = {@Text(label = "Info", language = Language.English), @Text(label = "信息显示", language = Language.Chinese)})
    public static final BooleanSetting infoHud = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Dynamic Island", language = Language.English), @Text(label = "灵动岛", language = Language.Chinese)})
    public static final BooleanSetting dynamicIsland = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Blur", language = Language.English), @Text(label = "模糊", language = Language.Chinese)})
    public static final BooleanSetting blur = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Shadow", language = Language.English), @Text(label = "阴影", language = Language.Chinese)})
    public static final BooleanSetting shadow = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Font Shadow", language = Language.English), @Text(label = "字体阴影", language = Language.Chinese)})
    public static final BooleanSetting fontShadow = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Chat Avoidance", language = Language.English), @Text(label = "聊天框避让", language = Language.Chinese)})
    public static final BooleanSetting chatAvoidance = new BooleanSetting(true);

    @SettingInfo(name = {@Text(label = "Chat Offset", language = Language.English), @Text(label = "聊天框偏移", language = Language.Chinese)})
    public static final NumberSetting chatOffset = new NumberSetting(13.0, 10.0, 20.0, "#.0");

    private final float[] tickRates = new float[20];
    private int nextIndex;
    private long timeLastTimeUpdate = -1L;
    private long timeGameJoined;

    private boolean isChatOpen = false;
    private float currentYOffset = 0f;
    private float targetYOffset = 0f;
    private static final float ANIMATION_SPEED = 0.15f;

    public ElementsHud() {
        super("Elements", 0,0,0,0);
        registerSetting(moduleListButton, inventoryButton, titleButton, targetButton,potionButton, keyBindsButton,radarButton, sessionsButton, scoreboardButton, infoHud, dynamicIsland, clearAllElements,blur,shadow,fontShadow, chatAvoidance, chatOffset);

    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacket() instanceof ClientboundSetTimePacket) {
            long now = System.currentTimeMillis();
            float timeElapsed = (float) (now - this.timeLastTimeUpdate) / 1000.0F;
            tickRates[this.nextIndex] = Mth.clamp(20.0F / timeElapsed, 0.0F, 20.0F);
            nextIndex = (this.nextIndex + 1) % this.tickRates.length;
            timeLastTimeUpdate = now;
        }
    }

    @EventTarget
    public void onTick(EventEntityJoinWorld event) {
        if (event.getEntity() == mc.player) {
            Arrays.fill(this.tickRates, 0.0F);
            nextIndex = 0;
            timeLastTimeUpdate = System.currentTimeMillis();
            timeGameJoined = this.timeLastTimeUpdate;
        }
    }

    public float getTickRate() {
        if (mc.player == null) return 0f;
        if (System.currentTimeMillis() - timeGameJoined < 4000) return 20f;
        int numTicks = 0;
        float sumTickRates = 0.0f;
        for (float tickRate : tickRates) {
            if (tickRate > 0) {
                sumTickRates += tickRate;
                numTicks++;
            }
        }

        String text = String.format("%.2f", sumTickRates / numTicks);
        return Float.parseFloat(text);
    }
    private boolean isChatScreenOpen() {
        return mc.screen instanceof ChatScreen;
    }
    private void updatePositionAnimation() {
        if (!chatAvoidance.getValue()) {
            currentYOffset = 0f;
            targetYOffset = 0f;
            isChatOpen = false;
            return;
        }

        boolean chatOpen = isChatScreenOpen();
        if (chatOpen != isChatOpen) {
            isChatOpen = chatOpen;
            targetYOffset = chatOpen ? chatOffset.getValue().floatValue() : 0f;
        }
        if (Math.abs(currentYOffset - targetYOffset) > 0.1f) {
            currentYOffset += (targetYOffset - currentYOffset) * ANIMATION_SPEED;
        } else {
            currentYOffset = targetYOffset;
        }
    }


    @Override
    public void inGuiDraw(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {}

    @Override
    public boolean isMainElement() {
        return true;
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        int screenHeight = mc.getWindow().getGuiScaledHeight();
        updatePositionAnimation();

        if(infoHud.getValue()) {
            float tpsY = screenHeight - 15 - currentYOffset;
            float fpsY = screenHeight - 27 - currentYOffset;

            SkiaFontManager.getDefaultFont18().drawText(canvasStack, "Tps: §f" + getTickRate(),  4,  tpsY, AuraSyncHud.getColor(0, 0), fontShadow.getValue());
            SkiaFontManager.getDefaultFont18().drawText(canvasStack, "Fps: §f" + ObfuscationReflectionHelper.getPrivateValue(Minecraft.class, mc, "fps"),  4,  fpsY, AuraSyncHud.getColor(0, 0), fontShadow.getValue());
        }
    }
}
