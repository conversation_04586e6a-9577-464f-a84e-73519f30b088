package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;

import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.ui.skija.utils.ImageHelper;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.ChatFormatting;
import net.minecraft.client.resources.language.I18n;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffectUtil;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;

@SuppressWarnings("all")
public class PotionHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊", language = Language.Chinese)
    })
    public static final BooleanSetting blur = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Delete", language = Language.English),
            @Text(label = "删除", language = Language.Chinese)
    })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };
    public PotionHud() {
        super("PotionHud", 0, 0, 100, 40);
        registerSetting(blur, delete);
    }

    public void clearIconSizeCache() {
        iconSizeCache.clear();
    }

    public void clearAllCaches() {
        effectMaxDurations.clear();
        iconSizeCache.clear();
    }

    private final Map<MobEffect, Integer> effectMaxDurations = new HashMap<>();
    private final Map<ResourceLocation, int[]> iconSizeCache = new HashMap<>();
    private int[] getPotionIconSize(ResourceLocation resourceLocation) {
        if (iconSizeCache.containsKey(resourceLocation)) {
            return iconSizeCache.get(resourceLocation);
        }
        
        try {
            io.github.humbleui.skija.Image fullImage = ImageHelper.extractRegionLocal(resourceLocation, 0, 0, 128, 128);
            if (fullImage != null && !fullImage.isClosed()) {
                int width = fullImage.getWidth();
                int height = fullImage.getHeight();
                if (width > 0 && height > 0 && width <= 128 && height <= 128) {
                    int[] size = new int[]{width, height};
                    iconSizeCache.put(resourceLocation, size);
                    return size;
                }
            }
        } catch (Exception e) {
        }
        int[] defaultSize = new int[]{18, 18};
        iconSizeCache.put(resourceLocation, defaultSize);
        return defaultSize;
    }
    private float calculateDisplaySize(int imageWidth, int imageHeight) {
        float targetSize = 18.0f;
        if (imageWidth != 18 || imageHeight != 18) {
            float scaleX = targetSize / imageWidth;
            float scaleY = targetSize / imageHeight;
            float scale = Math.min(scaleX, scaleY);
            
            float displaySize = Math.min(imageWidth, imageHeight) * scale;
            if (displaySize < 12.0f) {
                displaySize = 12.0f;
            } else if (displaySize > 24.0f) {
                displaySize = 24.0f;
            }
            
            return displaySize;
        }
        
        return targetSize;
    }
    
    @Override
    protected void processDraw(CanvasStack canvasStack) {
        //shadow
        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, 0, 0, (float)getWidth(),(float) getHeight(),8, ClipMode.DIFFERENCE);
        SkiaRender.drawRoundedRectWithShadow(canvasStack,0, 0, (float)getWidth(),(float) getHeight(),8);
        canvasStack.pop();

        if(blur.getValue()) {
            SkiaRender.drawBlurRect(canvasStack,  0,0,(float) getWidth(),(float)getHeight(), 8, 12);
        }
        SkiaRender.drawRoundedRect(canvasStack,  0,0,(float) getWidth(),(float)getHeight(), 8, new Color(24, 24, 24, 110).getRGB());
        SkiaRender.drawRoundedRect(canvasStack,  4,5,2,8, 4, AuraSyncHud.getColor(0,0));
        SkiaFontManager.getBigFont(18).drawText(canvasStack, "Potion", 8, 4, AuraSyncHud.getColor(0,0));
        double y = 15;
        int index = 0;


        for (MobEffectInstance effect : mc.player.getActiveEffects()) {
            MobEffect potion = effect.getEffect();
            String potionType = I18n.get(potion.getDescriptionId());
            int duration = effect.getDuration();
            effectMaxDurations.compute(potion, (k, v) -> (v == null || duration > v) ? duration : v);
            int maxTime = effectMaxDurations.getOrDefault(potion, duration);

            switch (effect.getAmplifier()) {
                case 1 -> potionType += " II";
                case 2 -> potionType += " III";
                case 3 -> potionType += " IV";
                case 4 -> potionType += " IV+";
                default -> potionType += " I";
            }
            String durationText = effect.getDuration() < 600 ?
                    (effect.getDuration() < 300 ? ChatFormatting.RED : ChatFormatting.YELLOW) +
                            MobEffectUtil.formatDuration(effect, 1.0f).getString() :
                    ChatFormatting.WHITE + MobEffectUtil.formatDuration(effect, 1.0f).getString();

            ResourceLocation resourceLocation = new ResourceLocation("minecraft", "textures/mob_effect/" + BuiltInRegistries.MOB_EFFECT.getKey(potion).getPath() + ".png");
            int[] iconSize = getPotionIconSize(resourceLocation);
            int imageWidth = iconSize[0];
            int imageHeight = iconSize[1];
            io.github.humbleui.skija.Image image = ImageHelper.extractRegionLocal(resourceLocation, 0, 0, imageWidth, imageHeight);
            float displaySize = calculateDisplaySize(imageWidth, imageHeight);
            
            SkiaRender.drawImage(canvasStack, image, 3, (float) y, displaySize, displaySize);
            SkiaFontManager.getDefaultFont16().drawText(canvasStack, potionType, 23, (float) y + 2, -1);
            SkiaFontManager.getDefaultFont14().drawText(canvasStack, durationText, (float) getWidth() - SkiaFontManager.getDefaultFont14().getWidth(durationText) - 6, (float) y + 3, -1);
            SkiaRender.drawRoundedRect(canvasStack, 23, (float) y + 14, (float) getWidth() - 30, 3, 4, new Color(24, 24, 24, 150).getRGB());

            float percent = Math.min(effect.getDuration() / (float) maxTime, 1.0f);
            SkiaRender.drawRoundedRect(canvasStack, 23, (float) y + 14, ((float) getWidth() - 30)*percent, 3, 4, AuraSyncHud.getColor(index,y));
            ++index;
            y+=22;
        }
        setHeight(15 + (double) mc.player.getActiveEffects().size() *  22);
        /*
        double y = 15;
        RenderUtils.drawShadow(poseStack, 0, 0,(float) getWidth(), (float) getHeight());
        if(blur.getValue())
            RenderUtils.drawBlur(getDrawX(),getDrawY(), getWidth(), getHeight(), partialTicks);
        RenderUtils.drawRect(poseStack, 0, 0, getWidth(), 15, new Color(22,22,22, 202).getRGB());
        FontRenderers.sf_bold18.drawString(poseStack, "Potions", 3, 5, -1);
        RenderUtils.drawRect(poseStack, 0, 0, getWidth(), 1, AuraSyncHud.getColor(0,0));
        for (MobEffectInstance effect : mc.player.getActiveEffects()) {
            y += 2;
            MobEffect potion = effect.getEffect();
            String potionType = I18n.get(potion.getDescriptionId());

            switch (effect.getAmplifier()) {
                case 1 -> potionType += " II";
                case 2 -> potionType += " III";
                case 3 -> potionType += " IV";
                case 4 -> potionType += " IV+";
                default -> potionType += " I";
            }

            String durationText = effect.getDuration() < 600 ?
                    (effect.getDuration() < 300 ? ChatFormatting.RED : ChatFormatting.YELLOW) +
                            MobEffectUtil.formatDuration(effect, 1.0f).getString() :
                    ChatFormatting.WHITE + MobEffectUtil.formatDuration(effect, 1.0f).getString();
//            guiGraphics.drawString(mc.font, potionType, 0,0, potion.getColor(), true);
            RenderUtils.drawRect(poseStack, 0, y - 2, getWidth(), y + 22 , new Color(22,22,22,150).getRGB());

            guiGraphics.blit(new ResourceLocation("minecraft", "textures/mob_effect/" + BuiltInRegistries.MOB_EFFECT.getKey(potion).getPath() + ".png"),
                    3, (int) y, 0, 0, 18, 18, 18, 18);


            FontRenderers.misans18.drawString(poseStack, potionType, 23, y + 2, -1);
            FontRenderers.misans16.drawString(poseStack, durationText, 23, y + 12, -1);
            y += 22;

        }
        setHeight(y);

         */
    }
//    @SubscribeEvent
//    public void onPotionIcon(RenderGuiOverlayEvent.Pre event) {
//        if (mc.player == null || mc.level == null)
//            return;
//
//        if (event.getOverlay() == VanillaGuiOverlay.POTION_ICONS.type())
//            event.setCanceled(true);
//    }
}
