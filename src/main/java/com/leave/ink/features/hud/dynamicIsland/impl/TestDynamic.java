package com.leave.ink.features.hud.dynamicIsland.impl;

import com.leave.ink.features.hud.dynamicIsland.DynamicElement;
import com.leave.ink.ui.skija.CanvasStack;

public class TestDynamic extends DynamicElement {
    private long time;
    public TestDynamic(String left, String right, long time) {
        super(left, right);
        this.time = time;
    }

    @Override
    public long getRetentionTime() {
        return time;
    }

    @Override
    public void left(CanvasStack canvasStack, float x, float y, float width, float height) {

    }

    @Override
    public void right(CanvasStack canvasStack, float x, float y, float width, float height) {

    }
}
