package com.leave.ink.features.hud.dynamicIsland.impl;

import com.leave.ink.features.hud.dynamicIsland.DynamicElement;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import net.minecraft.ChatFormatting;

import java.awt.*;

public class ModuleDynamic extends DynamicElement {
    private final boolean enable;
    private final String moduleName;
    private static final String ICON_ON_CHAR = "b";
    private static final String ICON_OFF_CHAR = "a";

    private float iconOffsetX = 8f;
    private float iconOffsetY = -2f;
    private float iconSize = 60f;

    public ModuleDynamic(String moduleName, boolean enable) {
        super(moduleName, moduleName);
        this.enable = enable;
        this.moduleName = moduleName;
    }

    @Override
    public double getShiftWidth() {
        float moduleNameWidth = SkiaFontManager.getDefaultFont18().getWidth(moduleName);
        float iconWidthA = SkiaRender.getIconsFontWidth(ICON_OFF_CHAR, iconSize);
        float iconWidthB = SkiaRender.getIconsFontWidth(ICON_ON_CHAR, iconSize);
        float maxIconWidth = Math.max(iconWidthA, iconWidthB);
        return Math.max(moduleNameWidth + maxIconWidth - 100, -30);
    }

    @Override
    public double getShiftHeight() {
        return 2;
    }

    @Override
    public void left(CanvasStack canvasStack, float x, float y, float width, float height) {
        String iconChar = enable ? ICON_ON_CHAR : ICON_OFF_CHAR;
        int iconColor = getIconColor(iconChar);
        SkiaRender.drawIconsFont(canvasStack, iconChar, iconOffsetX, iconOffsetY + 1, iconSize, iconColor);
    }
    private int getIconColor(String iconChar) {
        if (ICON_ON_CHAR.equals(iconChar)) {
            return new Color(255, 255, 255).getRGB();
        }
        if (ICON_OFF_CHAR.equals(iconChar)) {
            return new Color(255, 255, 255).getRGB();
        }
        return Color.WHITE.getRGB();
    }

    @Override
    public void right(CanvasStack canvasStack, float x, float y, float width, float height) {
        float moduleNameWidth = SkiaFontManager.getDefaultFont18().getWidth(moduleName);
        SkiaFontManager.getDefaultFont18().drawTextWithBlur(canvasStack, moduleName, -moduleNameWidth - 8, 8, Color.WHITE.getRGB(),(float) (1-fadeAnimation.getOutput()) * 10);
    }

    @Override
    public long getRetentionTime() {
        return 1500;
    }
}
