package com.leave.ink.features.hud.main;

import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.utils.render.RenderUtils;
import net.minecraft.client.gui.GuiGraphics;

import java.awt.*;
import java.util.Arrays;

public class AuraSyncHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Color", language = Language.English),
            @Text(label = "颜色", language = Language.Chinese)
    })
    public static final ColorSetting colorValue = new ColorSetting(new Color(7, 158, 255, 255));

    @SettingInfo(name = {
            @Text(label = "GradientColor1", language = Language.English),
            @Text(label = "渐变颜色1", language = Language.Chinese)
    })
    public static final ColorSetting gradientColor1 = new ColorSetting(new Color(128, 171, 255));
    @SettingInfo(name = {
            @Text(label = "GradientColor2", language = Language.English),
            @Text(label = "渐变颜色2", language = Language.Chinese)
    })
    public static final ColorSetting gradientColor2 = new ColorSetting(new Color(160, 72, 255));
    @SettingInfo(name = {
            @Text(label = "Speed", language = Language.English),
            @Text(label = "渐变速度", language = Language.Chinese)
    })
    public static final NumberSetting gradientSpeed = new NumberSetting(10,1,100,"#");
    @SettingInfo(name = {
            @Text(label = "ColorMode", language = Language.English),
            @Text(label = "颜色模式", language = Language.Chinese)
    })
    public static final ModeSetting colorModeValue = new ModeSetting("Fade", Arrays.asList("Rainbow", "Fade", "Static", "Gradient"),
            new SettingAttribute<>(colorValue, "Static", "Fade"),
            new SettingAttribute<>(gradientColor1, "Gradient"),
            new SettingAttribute<>(gradientColor2, "Gradient"),
            new SettingAttribute<>(gradientSpeed, "Gradient")
    );


    public AuraSyncHud() {
        super("AuraSync",0,0,0,0);
        registerSetting(colorModeValue);
    }
    private static int[] counter = new int[]{1};
    public static int getColor(int index, double y) {

        return switch (colorModeValue.getValue()) {
            case "Rainbow" -> RenderUtils.astolfoRainbow(index * 100, 15, 107);
            case "Fade" -> RenderUtils.fade(colorValue.getValue(), index, 100).getRGB();
            case "Static" -> colorValue.getValue().getRGB();
            case "Gradient" -> RenderUtils.getGradientOffset(gradientColor1.getValue(), gradientColor2.getValue(), (Math.abs(((System.currentTimeMillis()) / 100D * gradientSpeed.getValue().doubleValue())) / 100D) + (y / 50f)).getRGB();

            default -> Color.WHITE.getRGB();
        };
    }
    @Override
    public void inGuiDraw(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {

    }

    @Override
    public boolean isMainElement() {
        return true;
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {

    }
}

