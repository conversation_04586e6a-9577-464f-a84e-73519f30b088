package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import com.leave.ink.Main;
import com.leave.ink.features.hud.dynamicIsland.DynamicElement;
import com.leave.ink.features.module.modules.other.NameProtect;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.IconFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.Utils;
import lombok.Setter;
import net.minecraft.ChatFormatting;
import net.minecraft.Optionull;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.client.multiplayer.ServerData;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.GameType;
import net.minecraft.world.scores.PlayerTeam;

import java.awt.*;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Pattern;

@Setter
public class TabOverlayDynamic extends DynamicElement {
    private static final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");
    public static float listHeight = 0, listWidth = 0;
    private boolean keepAlive = false;

    private double cachedShiftWidth = 0;
    private double cachedShiftHeight = 0;
    private boolean needRecalculate = true;
    private long lastCalculationTime = 0;

    private int lastPlayerCount = 0;
    private int lastWindowWidth = 0;
    private int lastWindowHeight = 0;
    private String lastServerInfo = "";

    private double targetShiftWidth = 0;
    private double targetShiftHeight = 0;
    private double transitionSpeed = 0.15;

    public TabOverlayDynamic(String left, String right) {
        super(left, right,10);
        this.sticky = true;
    }

    @Override
    public void startFadeIn() {
        super.startFadeIn();
        needRecalculate = true;
        if (cachedShiftWidth == 0 && cachedShiftHeight == 0) {
            recalculateSize();
            cachedShiftWidth = targetShiftWidth;
            cachedShiftHeight = targetShiftHeight;
        }
    }

    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
        needRecalculate = true;
    }

    @Override
    public void resetInternalState() {
        cachedShiftWidth = 0;
        cachedShiftHeight = 0;
        targetShiftWidth = 0;
        targetShiftHeight = 0;
        needRecalculate = true;
        lastCalculationTime = 0;
        lastPlayerCount = 0;
        lastWindowWidth = 0;
        lastWindowHeight = 0;
        lastServerInfo = "";
    }

    private static final Comparator<PlayerInfo> PLAYER_COMPARATOR =
            Comparator.<PlayerInfo>comparingInt((p_253306_)
                    -> p_253306_.getGameMode() == GameType.SPECTATOR ? 1 : 0).thenComparing((p_269613_)
                    -> Optionull.mapOrDefault(p_269613_.getTeam(), PlayerTeam::getName, "")).thenComparing((p_253305_)
                    -> p_253305_.getProfile().getName(), String::compareToIgnoreCase);

    private List<PlayerInfo> getPlayerInfos() {
        return mc.player.connection.getListedOnlinePlayers().stream().sorted(PLAYER_COMPARATOR).toList();
    }

    private String truncateText(String text, int maxLength) {
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + "...";
    }

    private int playersPerColumn() {
        int windowHeight = mc.getWindow().getGuiScaledHeight();
        int windowWidth = mc.getWindow().getGuiScaledWidth();

        int widthBonus = 0;
        if (windowWidth >= 1920) {
            widthBonus = 4;
        } else if (windowWidth >= 1600) {
            widthBonus = 3;
        } else if (windowWidth >= 1366) {
            widthBonus = 2;
        }

        return Math.min(Math.min(Math.max(4, (windowHeight / 2 - 60) / 16), 8 + widthBonus), Math.max(4, (windowHeight / 2 - 60) / 16));
    }

    private int maxColumns() {
        int windowWidth = mc.getWindow().getGuiScaledWidth();
        List<PlayerInfo> players = getPlayerInfos();

        if (players.isEmpty()) return 1;

        float maxColumnWidth = 0;
        for (PlayerInfo playerInfo : players) {
            String displayName = playerInfo.getProfile().getName();
            maxColumnWidth = Math.max(maxColumnWidth, 16 + SkiaFontManager.getDefaultFont14().getWidth(displayName + " " + playerInfo.getLatency() + "ms"));
        }

        float availableWidth = windowWidth - 160;
        int maxPossibleColumns = (int) Math.floor(availableWidth / (maxColumnWidth + 6));

        return Math.max(1, Math.min(maxPossibleColumns, 8));
    }

    @Override
    public long getRetentionTime() {
        return keepAlive ? Long.MAX_VALUE : 0;
    }

    @Override
    public double getShiftHeight() {
        if (needRecalculate || hasKeyFactorsChanged()) {
            recalculateSize();
        }
        if (Math.abs(cachedShiftHeight - targetShiftHeight) > 0.5) {
            cachedShiftHeight += (targetShiftHeight - cachedShiftHeight) * transitionSpeed;
        } else {
            cachedShiftHeight = targetShiftHeight;
        }

        return cachedShiftHeight;
    }

    @Override
    public double getShiftWidth() {
        if (needRecalculate || hasKeyFactorsChanged()) {
            recalculateSize();
        }
        if (Math.abs(cachedShiftWidth - targetShiftWidth) > 0.5) {
            cachedShiftWidth += (targetShiftWidth - cachedShiftWidth) * transitionSpeed;
        } else {
            cachedShiftWidth = targetShiftWidth;
        }

        return cachedShiftWidth;
    }
    private boolean hasKeyFactorsChanged() {
        List<PlayerInfo> players = getPlayerInfos();
        int currentPlayerCount = players.size();
        int currentWindowWidth = mc.getWindow().getGuiScaledWidth();
        int currentWindowHeight = mc.getWindow().getGuiScaledHeight();
        String currentServerInfo = getServerInfo();

        boolean changed = currentPlayerCount != lastPlayerCount ||
                currentWindowWidth != lastWindowWidth ||
                currentWindowHeight != lastWindowHeight ||
                !currentServerInfo.equals(lastServerInfo);

        if (changed) {
            lastPlayerCount = currentPlayerCount;
            lastWindowWidth = currentWindowWidth;
            lastWindowHeight = currentWindowHeight;
            lastServerInfo = currentServerInfo;
            return true;
        }
        return System.currentTimeMillis() - lastCalculationTime > 1000;
    }
    private void recalculateSize() {
        List<PlayerInfo> players = getPlayerInfos();
        if (players.isEmpty()) {
            targetShiftHeight = 60;
        } else {
            int playersPerColumn = playersPerColumn();
            int maxColumns = maxColumns();
            int columns = Math.min(maxColumns, (int) Math.ceil(players.size() / (double) playersPerColumn));
            int actualPlayersPerColumn = (int) Math.ceil(players.size() / (double) columns);
            int rowsNeeded = Math.min(actualPlayersPerColumn, players.size());
            targetShiftHeight = Math.max(60, rowsNeeded * 16 + 54);
        }
        if (players.isEmpty()) {
            targetShiftWidth = -24;
        } else {
            int playersPerColumn = playersPerColumn();
            int maxColumns = maxColumns();
            int columns = Math.min(maxColumns, (int) Math.ceil(players.size() / (double) playersPerColumn));

            float maxColumnWidth = 0;
            for (PlayerInfo playerInfo : players) {
                String displayName = getDisplayName(playerInfo);
                // 使用getCleanTextWidth方法计算宽度
                maxColumnWidth = Math.max(maxColumnWidth, 16 + getCleanTextWidth(displayName + " " + playerInfo.getLatency() + "ms"));
            }

            float totalWidth;
            if (columns == 1) {
                totalWidth = maxColumnWidth;
            } else {
                totalWidth = columns * maxColumnWidth + (columns - 1) * 6;
            }

            String serverInfo = getServerInfo();
            String serverMOTD = getServerMOTD();
            String playerCount = getPlayerCount();

            float serverInfoWidth = SkiaFontManager.getDefaultFont16().getWidth(serverInfo);
            float motdWidth = !serverMOTD.isEmpty() ? SkiaFontManager.getDefaultFont12().getWidth(truncateText(serverMOTD, 50)) : 0;
            float playerCountWidth = SkiaFontManager.getDefaultFont14().getWidth(playerCount);

            totalWidth = Math.max(totalWidth, serverInfoWidth);
            totalWidth = Math.max(totalWidth, motdWidth);
            totalWidth = Math.max(totalWidth, playerCountWidth);

            int windowWidth = mc.getWindow().getGuiScaledWidth();
            float maxAllowedWidth = windowWidth - 160;
            totalWidth = Math.min(totalWidth, maxAllowedWidth);

            targetShiftWidth = (totalWidth - 130) / 2;
        }
        needRecalculate = false;
        lastCalculationTime = System.currentTimeMillis();
    }

    private String getDisplayName(PlayerInfo playerInfo) {
        // 直接使用PlayerInfo的getTabDisplayName()方法获取原版Tab UI的显示名称
        Component displayNameComponent = playerInfo.getTabDisplayName();
        String coloredName;

        if (displayNameComponent != null) {
            // 如果有自定义的Tab显示名称，使用它
            coloredName = Utils.getStringFromFormattedCharSequence(displayNameComponent.getVisualOrderText());
        } else {
            // 否则使用团队格式化的名称
            coloredName = Utils.getStringFromFormattedCharSequence(
                PlayerTeam.formatNameForTeam(
                    playerInfo.getTeam(),
                    Component.literal(playerInfo.getProfile().getName())
                ).getVisualOrderText()
            );
        }

        // 保持与NameProtect功能的兼容性
        NameProtect nameProtect = (NameProtect) Main.INSTANCE.moduleManager.getModule("NameProtect");
        if (nameProtect.isEnable() && mc.player != null && coloredName.contains(mc.player.getName().getString())) {
            coloredName = coloredName.replace(mc.player.getName().getString(), "Hide");
        }

        return coloredName;
    }

    private void drawColoredText(CanvasStack canvasStack, String text, float x, float y, boolean shadow) {
        if (text == null || text.isEmpty()) return;

        // 检查文本是否包含图标
        if (IconFont.containsIcons(text)) {
            IconFont.drawTextWithIcons(canvasStack, text, x, y, Color.WHITE.getRGB(), shadow);
            return;
        }

        float currentX = x;
        int currentColor = Color.WHITE.getRGB();
        StringBuilder currentSegment = new StringBuilder();

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '§' && i + 1 < text.length()) {
                // 渲染当前累积的文本段
                if (!currentSegment.isEmpty()) {
                    SkiaFontManager.getDefaultFont14().drawText(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow);
                    currentX += SkiaFontManager.getDefaultFont14().getWidth(currentSegment.toString());
                    currentSegment.setLength(0);
                }

                char colorChar = text.charAt(i + 1);

                // 处理十六进制颜色代码 (§x§r§r§g§g§b§b)
                if (colorChar == 'x' && i + 13 < text.length()) {
                    try {
                        StringBuilder hexColor = new StringBuilder();
                        for (int j = 2; j < 14; j += 2) {
                            if (text.charAt(i + j) == '§') {
                                hexColor.append(text.charAt(i + j + 1));
                            }
                        }
                        if (hexColor.length() == 6) {
                            currentColor = 0xFF000000 | Integer.parseInt(hexColor.toString(), 16);
                            i += 13;
                            continue;
                        }
                    } catch (NumberFormatException ignored) {
                    }
                }

                // 处理标准颜色代码
                ChatFormatting format = ChatFormatting.getByCode(colorChar);
                if (format != null) {
                    if (format.isColor()) {
                        Integer color = format.getColor();
                        if (color != null) {
                            currentColor = color | 0xFF000000;
                        }
                    } else if (format == ChatFormatting.RESET) {
                        currentColor = Color.WHITE.getRGB();
                    }
                }
                i++; // 跳过颜色代码字符
                continue;
            }

            currentSegment.append(c);
        }

        // 渲染剩余的文本
        if (!currentSegment.isEmpty()) {
            SkiaFontManager.getDefaultFont14().drawText(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow);
        }
    }

    private float getCleanTextWidth(String text) {
        return SkiaFontManager.getDefaultFont14().getWidth(COLOR_PATTERN.matcher(text).replaceAll(""));
    }




    private String getServerInfo() {
        if (mc.getCurrentServer() != null) {
            return mc.getCurrentServer().ip;
        }
        if (mc.hasSingleplayerServer()) {
            return "Singleplayer";
        }
        return "Unknown Server";
    }

    private String getServerMOTD() {
        if (mc.getCurrentServer() != null) {
            return ChatFormatting.stripFormatting(mc.getCurrentServer().motd.getString());
        }
        return "";
    }

    private String getPlayerCount() {
        List<PlayerInfo> players = getPlayerInfos();
        if (mc.getCurrentServer() != null) {
            ServerData serverData = mc.getCurrentServer();
            if (serverData.players != null) {
                return serverData.players.online() + "/" + serverData.players.max();
            }
        }
        return players.size() + " players";
    }

    @Override
    public void left(CanvasStack canvasStack, float x, float y, float width, float height) {
        List<PlayerInfo> players = getPlayerInfos();

        String serverInfo = getServerInfo();
        String serverMOTD = getServerMOTD();
        String playerCount = getPlayerCount();

        float currentY = 12;

        float serverInfoWidth = SkiaFontManager.getDefaultFont16().getWidth(serverInfo);
        float serverInfoX = (width - serverInfoWidth) / 2f;
        SkiaFontManager.getDefaultFont16().drawText(canvasStack, serverInfo, serverInfoX, currentY, new Color(255, 255, 85).getRGB());
        currentY += 16;

        if (!serverMOTD.isEmpty()) {
            String truncatedMOTD = truncateText(serverMOTD, 50);
            float motdWidth = SkiaFontManager.getDefaultFont12().getWidth(truncatedMOTD);
            float motdX = (width - motdWidth) / 2f;
            SkiaFontManager.getDefaultFont12().drawText(canvasStack, truncatedMOTD, motdX, currentY, new Color(170, 170, 170).getRGB());
        }

        currentY += 12;
        float playerCountWidth = SkiaFontManager.getDefaultFont14().getWidth(playerCount);
        float playerCountX = (width - playerCountWidth) / 2f;
        SkiaFontManager.getDefaultFont14().drawText(canvasStack, playerCount, playerCountX, currentY, new Color(85, 255, 85).getRGB());

        if (players.isEmpty()) return;

        int playersPerColumn = playersPerColumn();
        int maxColumns = maxColumns();
        int columns = Math.min(maxColumns, (int) Math.ceil(players.size() / (double) playersPerColumn));

        int actualPlayersPerColumn = (int) Math.ceil(players.size() / (double) columns);

        float maxColumnWidth = 0;
        for (PlayerInfo playerInfo : players) {
            String displayName = getDisplayName(playerInfo);
            // 使用getCleanTextWidth方法计算宽度
            maxColumnWidth = Math.max(maxColumnWidth, 16 + getCleanTextWidth(displayName + " " + playerInfo.getLatency() + "ms"));
        }

        float totalContentWidth;
        if (columns == 1) {
            totalContentWidth = maxColumnWidth;
        } else {
            totalContentWidth = columns * maxColumnWidth + (columns - 1) * 6;
        }

        float startX = (width - totalContentWidth) / 2f;

        int playerIndex = 0;
        for (int col = 0; col < columns; col++) {
            for (int row = 0; row < Math.min(actualPlayersPerColumn, players.size() - col * actualPlayersPerColumn); row++) {
                if (playerIndex >= players.size()) break;
                PlayerInfo playerInfo = players.get(playerIndex);

                try {
                    float headX = startX + col * (maxColumnWidth + 6);
                    float headY = currentY + row * 16;
                    float textX = headX + 14 + 2;
                    float textY = headY + 11;

                    SkiaRender.drawTabPlayerHead(canvasStack, playerInfo, headX, headY + 9, 14, 14, 2);

                    String displayName = truncateText(getDisplayName(playerInfo), 18);

                    // 使用drawColoredText来渲染带颜色的玩家名字
                    drawColoredText(canvasStack, displayName, textX, textY, false);

                    // 使用getCleanTextWidth方法计算名字宽度
                    float nameWidth = getCleanTextWidth(displayName);
                    Color pingColor = getPingColor(playerInfo);
                    SkiaFontManager.getDefaultFont12().drawText(canvasStack, " " + playerInfo.getLatency() + "ms", textX + nameWidth, textY, pingColor.getRGB());

                    playerIndex++;
                } catch (Exception e) {
                    playerIndex++;
                }
            }
        }
    }

    private Color getPingColor(PlayerInfo playerInfo) {
        int ping = playerInfo.getLatency();
        if (ping < 50) {
            return new Color(85, 255, 85);
        } else if (ping < 100) {
            return new Color(255, 255, 85);
        } else if (ping < 200) {
            return new Color(255, 170, 85);
        } else {
            return new Color(255, 85, 85);
        }
    }

    private Color getGameModeColor(PlayerInfo playerInfo) {
        return switch (playerInfo.getGameMode()) {
            case SPECTATOR -> new Color(128, 128, 128);
            case CREATIVE -> new Color(255, 215, 0);
            case SURVIVAL -> new Color(255, 255, 255);
            case ADVENTURE -> new Color(0, 255, 0);
        };
    }

    @Override
    public void right(CanvasStack canvasStack, float x, float y, float width, float height) {}
}
