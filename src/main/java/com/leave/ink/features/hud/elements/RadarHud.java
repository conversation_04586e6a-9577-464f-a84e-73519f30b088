package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.render.RenderUtils;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;

import java.awt.*;

public class RadarHud extends AbsHudElement {
    @SettingInfo(name = {@Text(label = "ScaleFactor", language = Language.English), @Text(label = "缩放大小", language = Language.Chinese)})
    public final NumberSetting scaleFactor = new NumberSetting(1, 0.1d, 10d, "#.0");
    @SettingInfo(name = {
            @Text(label = "Size", language = Language.English),
            @Text(label = "雷达大小", language = Language.Chinese)
    })
    public final NumberSetting radarSize = new NumberSetting(90, 80, 140, "#");

    @SettingInfo(name = {@Text(label = "Delete", language = Language.English), @Text(label = "删除", language = Language.Chinese)})
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };
    public RadarHud() {
        super("Radar",0,0,100,100);
        registerSetting(scaleFactor, radarSize, delete);
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        int size = radarSize.getValue().intValue();
        setHeight(size);
        setWidth(size);
        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, 0, 0, (float)getWidth(),(float) getHeight(),8, ClipMode.DIFFERENCE);
        SkiaRender.drawRoundedRectWithShadow(canvasStack,0, 0, (float)getWidth(),(float) getHeight(),8);
        canvasStack.pop();

        if(ElementsHud.blur.getValue()) {
            SkiaRender.drawBlurRect(canvasStack, 0, 0, (float) size,(float) size,0,12);
        }
        SkiaRender.drawRect(canvasStack, 0, 0, (float) getWidth(),(float) getHeight(), new Color(24, 24, 24, 119).getRGB());

        float middleX = size / 2f, middleY = size / 2f;
        SkiaRender.drawRect(canvasStack, 2, middleY -0.5f, size - 2,  0.5f, new Color(120, 120, 120, 150).getRGB());
        SkiaRender.drawRect(canvasStack, middleX - 0.5f, 2, 0.5f, size - 2, new Color(120, 120, 120, 150).getRGB());
        canvasStack.translate(middleX,middleY);
        canvasStack.canvas.rotate(-mc.player.getYRot());
        canvasStack.canvas.rotate(180);

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (!(entity instanceof LivingEntity livingEntity))
                continue;
            if (entity == mc.player) continue;
            if (!Utils.isValidEntity(livingEntity))
                continue;
            double dx = RenderUtils.interpolate(entity.getX(), entity.xOld, mc.getFrameTime()) - mc.player.getX();
            double dz = RenderUtils.interpolate(entity.getZ(), entity.zOld, mc.getFrameTime()) - mc.player.getZ();
            double dy = entity.getY() - mc.player.getY();
            dx /= scaleFactor.getValue().doubleValue();
            dz /= scaleFactor.getValue().doubleValue();
            double radarRotation = Math.toRadians(180 + mc.player.getYRot());
            double unRotatedX = dx * Math.cos(radarRotation) + dz * Math.sin(radarRotation);
            double unRotatedY = -dx * Math.sin(radarRotation) + dz * Math.cos(radarRotation);
            double margin = 4;
            double halfWidth = size / 2.0 - margin;
            double halfHeight = size / 2.0 - margin;
            if (Math.abs(unRotatedX) > halfWidth || Math.abs(unRotatedY) > halfHeight) {
                double factorX = halfWidth / Math.abs(unRotatedX);
                double factorY = halfHeight / Math.abs(unRotatedY);
                double factor = Math.min(factorX, factorY);
                unRotatedX *= factor;
                unRotatedY *= factor;
            }


            double finalX = unRotatedX * Math.cos(radarRotation) - unRotatedY * Math.sin(radarRotation);
            double finalY = unRotatedX * Math.sin(radarRotation) + unRotatedY * Math.cos(radarRotation);

            canvasStack.push();
            canvasStack.translate((float) finalX, (float) finalY);
            double worldDistance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            float maxArrowSize = 14.0f;
            float minArrowSize = 9.0f;
            double maxDistanceThreshold = 60.0;
            float sizeArrow;
            if (worldDistance >= maxDistanceThreshold) {
                sizeArrow = minArrowSize;
            } else {
                sizeArrow = (float)(maxArrowSize - (worldDistance / maxDistanceThreshold) * (maxArrowSize - minArrowSize));
            }

            boolean team = Targets.isTeam(livingEntity);
//            canvasStack.canvas.rotate(livingEntity.getYRot());
//            canvasStack.canvas.rotate(180);
            dy = Math.abs(dy);
            double threshold = 40.0; // 高度差阈值，当高度差>=10时alpha达到最小值
            int minAlpha = 100;
            int maxAlpha = 255;

            if (dy > threshold) dy = threshold;

            int alpha = (int)(maxAlpha - (dy / threshold) * (maxAlpha - minAlpha));
            Color color = new Color(255,0,0,alpha);
            if(team) color = new Color(0,255,0,alpha);
            //RenderUtils.drawNewArrow(poseStack, 0, -sizeArrow / 2f, sizeArrow, color);
            sizeArrow /= 4;
            SkiaRender.drawRoundedRect(canvasStack, -sizeArrow / 2f, -sizeArrow / 2f, sizeArrow, sizeArrow, 5, color.getRGB());
            canvasStack.pop();
        }
//        poseStack.mulPose(Axis.ZP.rotationDegrees(-mc.player.getYRot()));
//        poseStack.mulPose(Axis.ZP.rotationDegrees(180));
        /*
        assert mc.level != null;
        int size = radarSize.getValue().intValue();
        setHeight(size);
        setWidth(size);
        RenderUtils.drawShadow(poseStack, 0, 0,(float) getWidth(), (float) getHeight());
        if(blur.getValue())
            RenderUtils.drawBlur(getDrawX(),getDrawY(),getWidth(),getHeight(), partialTicks);
        RenderUtils.drawRect(poseStack, 0, 0, getWidth(), getHeight(), new Color(24, 24, 24, 119).getRGB());

        float middleX = size / 2f, middleY = size / 2f;
        RenderUtils.drawRect(poseStack, 2, middleY -0.5f, size - 2, middleY + 0.5f, new Color(120, 120, 120, 150).getRGB());
        RenderUtils.drawRect(poseStack, middleX - 0.5f, 2, middleX + 0.5f, size - 2, new Color(120, 120, 120, 150).getRGB());
        poseStack.translate(middleX,middleY,0);
        poseStack.mulPose(Axis.ZP.rotationDegrees(-mc.player.getYRot()));
        poseStack.mulPose(Axis.ZP.rotationDegrees(180));



        for (Entity entity : mc.level.entitiesForRendering()) {
            if(!(entity instanceof LivingEntity livingEntity))
                continue;
            if(entity == mc.player) continue;
            if (!Utils.isValidEntity(livingEntity))
                continue;
            double dx = RenderUtils.interpolate(entity.getX(), entity.xOld, partialTicks) - mc.player.getX();
            double dz = RenderUtils.interpolate(entity.getZ(), entity.zOld, partialTicks) - mc.player.getZ();
            double dy = entity.getY() - mc.player.getY();
            dx /= scaleFactor.getValue().doubleValue();
            dz /= scaleFactor.getValue().doubleValue();
            double radarRotation = Math.toRadians(180 + mc.player.getYRot());
            double unRotatedX = dx * Math.cos(radarRotation) + dz * Math.sin(radarRotation);
            double unRotatedY = -dx * Math.sin(radarRotation) + dz * Math.cos(radarRotation);
            double margin = 4;
            double halfWidth = size / 2.0 - margin;
            double halfHeight = size / 2.0 - margin;
            if (Math.abs(unRotatedX) > halfWidth || Math.abs(unRotatedY) > halfHeight) {
                double factorX = halfWidth / Math.abs(unRotatedX);
                double factorY = halfHeight / Math.abs(unRotatedY);
                double factor = Math.min(factorX, factorY);
                unRotatedX *= factor;
                unRotatedY *= factor;
            }


            double finalX = unRotatedX * Math.cos(radarRotation) - unRotatedY * Math.sin(radarRotation);
            double finalY = unRotatedX * Math.sin(radarRotation) + unRotatedY * Math.cos(radarRotation);

            boolean team = Targets.isTeam(livingEntity);

            if(team)
                RenderUtils.drawCircle(poseStack, (float) finalX, (float) finalY, 1.5f, Color.GREEN.getRGB(), 100);
//            if(livingEntity.hurtTime > 0) {
//                RenderUtils.drawCircle(poseStack, (float) finalX, (float) finalY, 1.5f, Color.RED.getRGB(), 100);
//
//            }

//            RenderUtils.drawRect(poseStack, (float) finalX, (float) finalY, (float) arrowX, (float) arrowY, Color.WHITE.getRGB());
            poseStack.pushPose();

            poseStack.translate(finalX,finalY,0);
            double worldDistance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            float maxArrowSize = 14.0f;
            float minArrowSize = 9.0f;
            double maxDistanceThreshold = 60.0;
            float sizeArrow;
            if (worldDistance >= maxDistanceThreshold) {
                sizeArrow = minArrowSize;
            } else {
                sizeArrow = (float)(maxArrowSize - (worldDistance / maxDistanceThreshold) * (maxArrowSize - minArrowSize));
            }

            poseStack.mulPose(Axis.ZP.rotationDegrees(livingEntity.getYRot()));
            poseStack.mulPose(Axis.ZP.rotationDegrees(180));
            dy = Math.abs(dy);
            double threshold = 40.0; // 高度差阈值，当高度差>=10时alpha达到最小值
            int minAlpha = 100;
            int maxAlpha = 255;

            if (dy > threshold) dy = threshold;

            int alpha = (int)(maxAlpha - (dy / threshold) * (maxAlpha - minAlpha));
            Color color = new Color(255,0,0,alpha);
            if(team) color = new Color(0,255,0,alpha);
            RenderUtils.drawNewArrow(poseStack, 0, -sizeArrow / 2f, sizeArrow, color);

            poseStack.popPose();

//            RenderUtils.drawCircle(poseStack, (float) finalX, (float) finalY, 1, color, 100);
//            RenderUtils.drawRect(poseStack, finalX, finalY, finalX + 1, finalY + 1,color );
        }

         */
    }
}
