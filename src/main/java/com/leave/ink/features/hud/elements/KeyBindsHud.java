package com.leave.ink.features.hud.elements;

import com.external.ui.ExternalUI;
import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;

import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import io.github.humbleui.skija.ClipMode;

import java.awt.*;
import java.util.List;

public class KeyBindsHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊", language = Language.Chinese)
    })
    public static final BooleanSetting blur = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Delete", language = Language.English),
            @Text(label = "删除", language = Language.Chinese)
    })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };
    public KeyBindsHud() {
        super("KeyBinds");
        registerSetting(blur, delete);
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        setWidth(110);

        //shadow
        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, 0, 0, (float)getWidth(),(float) getHeight(),8, ClipMode.DIFFERENCE);
        SkiaRender.drawRoundedRectWithShadow(canvasStack,0, 0, (float)getWidth(),(float) getHeight(),8);
        canvasStack.pop();

        if(blur.getValue()) {
            SkiaRender.drawBlurRect(canvasStack,  0,0,(float) getWidth(),(float)getHeight(), 8, 12);
        }
        SkiaRender.drawRoundedRect(canvasStack,  0,0,(float) getWidth(),(float)getHeight(), 8, new Color(24, 24, 24, 110).getRGB());
        SkiaRender.drawRoundedRect(canvasStack,  4,5,2,8, 4, AuraSyncHud.getColor(0,0));
        SkiaFontManager.getBigFont(18).drawText(canvasStack, "KeyBinds", 8, 4, AuraSyncHud.getColor(0,0));
        float y = 12 + 6;
        List<Module> modules = Main.INSTANCE.moduleManager.getModules().stream().filter(it -> it.getKey() != 0).toList();
        for (Module module : modules) {
            String n = ExternalUI.getKeyName(module.getKey());
            if(n.contains("Unknown keycode: ")) n = "Unknown";
            SkiaFontManager.getDefaultFont16().drawText(canvasStack, module.getName(), 4, y, -1);
            SkiaFontManager.getDefaultFont16().drawText(canvasStack, "[" + n + "]", (float) getWidth() - SkiaFontManager.getDefaultFont16().getWidth("[" + n + "]") - 4, y, -1);
            y += 12;
        }
        setHeight(y);

//        RenderUtils.drawShadow(poseStack, 0, 0,(float) getWidth(), (float) getHeight() - 3);
//        if(blur.getValue())
//            RenderUtils.drawBlur(getDrawX() + 1,getDrawY() + 1, 110 - 1, getHeight() - 3, partialTicks);
//        List<Module> modules = Main.INSTANCE.moduleManager.getModules().stream().filter(it -> it.getKey() != 0).toList();
//        RenderUtils.drawRect(poseStack, 0, 0, 110, 15, new Color(24, 24, 24, 150).getRGB());
//        RenderUtils.drawRect(poseStack, 0, 0, 110, modules.size() * 12 + 16, new Color(24, 24, 24, 150).getRGB());
//        RenderUtils.drawRect(poseStack, 0, 0, 110, 1, AuraSyncHud.getColor(0,0));
//
//        FontRenderers.sf_bold18.drawString(poseStack, "KeyBinds", 3, 5, -1);
//        double y = 12 + 6;
//        for (Module module : modules) {
//
//            String n = ExternalUI.getKeyName(module.getKey());
////            if(n == null) n = "Unknown";
//            FontRenderers.misans18.drawString(poseStack, module.getName(), 4, y, -1);
//            String text = ChatFormatting.WHITE + "[" +ChatFormatting.GRAY +n+ ChatFormatting.WHITE + "]";
//            FontRenderers.misans18.drawString(poseStack, text,( module.isEnable()? 86 : 82) - FontRenderers.misans18.getStringWidth(text) - 2, y, -1);
//            if(module.isEnable())
//                FontRenderers.misans18.drawString(poseStack, ChatFormatting.WHITE + "[" +ChatFormatting.GREEN + "ON" + ChatFormatting.WHITE + "]", 86, y, -1);
//            else
//                FontRenderers.misans18.drawString(poseStack, ChatFormatting.WHITE + "[" +ChatFormatting.RED + "OFF" + ChatFormatting.WHITE + "]", 82, y, -1);
//            y += 12;
//        }
//        setHeight(y);

    }

}
