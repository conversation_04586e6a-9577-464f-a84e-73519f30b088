package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.DesignGuiScreen;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.manager.HeypixelManager;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;
import com.leave.ink.utils.timer.TimeUtils;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.client.gui.screens.ChatScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;

import java.awt.*;

public class TargetHud extends AbsHudElement {
    private Animation animation = new DecelerateAnimation(250, 1);

    private LivingEntity prevTarget;
    private LivingEntity lastTarget;
    private Animation healthAnimation = new DecelerateAnimation(500, 1); // 或者你喜欢的速度
    private float lastHealth = 0f;
    float smoothHealth;

    private float shadowHealth = 0f;

    private long lastDamageTime = System.currentTimeMillis();
    private final long SHADOW_DELAY = 300; // 受伤后延迟多久再开始收缩残影

    @SettingInfo(name = {
            @Text(label = "OnlyKillAura", language = Language.English),
            @Text(label = "OnlyKillAura", language = Language.Chinese)
    })
    public static final BooleanSetting onlyAura = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊", language = Language.Chinese)
    })
    public static final BooleanSetting blur = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "DurationTime", language = Language.English),
            @Text(label = "持续时间", language = Language.Chinese)
    })
    private final NumberSetting durationTime = new NumberSetting(3000, 0, 10000, "#");

    @SettingInfo(name = {
            @Text(label = "ShowDistance", language = Language.English),
            @Text(label = "显示距离", language = Language.Chinese)
    })
    public static final BooleanSetting showDistance = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "ShowHurtTime", language = Language.English),
            @Text(label = "ShowHurtTime", language = Language.Chinese)
    })
    public static final BooleanSetting showHurtTime = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Delete", language = Language.English),
            @Text(label = "删除", language = Language.Chinese)
    })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };

    public TargetHud() {
        super("Target", 0.5d, 0.5d, 100, 38);
        registerSetting(durationTime, onlyAura, blur, showDistance, showHurtTime, delete);
    }

    private final TimeUtils durationTimer = new TimeUtils();
    @Override
    protected void processDraw(CanvasStack canvasStack) {

        LivingEntity otherTarget = null;
        if(!onlyAura.getValue()) {

            if(mc.hitResult != null && mc.hitResult.getType() == HitResult.Type.ENTITY && mc.hitResult instanceof EntityHitResult entityHitResult) {
                if(entityHitResult.getEntity() instanceof LivingEntity livingEntity)
                {
                    otherTarget = livingEntity;
                }
            }
//            otherTarget = mc.hitResult.getType() == HitResult.Type.ENTITY ? ((LivingEntity) ((EntityHitResult) mc.hitResult).getEntity()) : null;
        }
        KillAura killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
        LivingEntity target = killAura.target != null ?
                killAura.target :
                (mc.screen instanceof DesignGuiScreen || mc.screen instanceof ChatScreen) ? mc.player : otherTarget;
        if (target != null)
            prevTarget = target;
        if(target == null) {
            boolean a = durationTimer.hasTimeElapsed(durationTime.getValue().intValue(), true);
            if(a)
                animation.setDirection(Direction.BACKWARDS);
        }else {
            durationTimer.reset();
            animation.setDirection(Direction.FORWARDS);
        }
        if(prevTarget == null) return;

        float width = Math.max(38 + SkiaFontManager.getDefaultFont20().getWidth(prevTarget.getName().getString()), 118);
        setWidth(width);
        setHeight(44);

        canvasStack.push();
        canvasStack.translate((float) width / 2f, (float) height / 2f);
        canvasStack.canvas.scale((float) animation.getOutput(), (float) animation.getOutput());


        //shadow
        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, (float) -width / 2f, (float) -height / 2f,(float) width, (float)height,8, ClipMode.DIFFERENCE);
        SkiaRender.drawRoundedRectWithShadow(canvasStack,(float) -width / 2f, (float) -height / 2f,(float) width, (float)height,8);
        canvasStack.pop();


        if(blur.getValue()) {
            SkiaRender.drawBlurRect(canvasStack, (float) -width / 2f, (float) -height / 2f,(float) width, (float)height, 8, 12);
        }
        SkiaRender.drawRoundedRect(canvasStack, (float) -width / 2f, (float) -height / 2f,(float) width, (float)height, 8, new Color(0, 0, 0, 100).getRGB());

        SkiaFontManager.getDefaultFont20().drawText(canvasStack, prevTarget.getName().getString(), (float) -width / 2f + 35, (float) -height / 2f + 4, -1);
        String formatted = String.format("%.2f", HeypixelManager.getEntityHealth(prevTarget));
        SkiaFontManager.getDefaultFont14().drawText(canvasStack, "Health: " + formatted, (float) -width / 2f + 35, (float) -height / 2f + 16, new Color(255,255,255,170).getRGB());
        if(showDistance.getValue()) {
            String distance ="Distance: " + String.format("%.2f",  RotationUtils.getDistanceToEntityBox(prevTarget));
            SkiaFontManager.getDefaultFont14().drawText(canvasStack, distance, (float) -width / 2f + 35, (float) -height / 2f + 16 + 8, new Color(255,255,255,170).getRGB());

        }

        //hurtTime
        if(showHurtTime.getValue()) {
            SkiaRender.drawCircularProgress(canvasStack, width / 2f-13,(float) -height / 2f + 25,8,2, (float) (prevTarget.hurtTime / 10.0),new Color(0, 0, 0, 100).getRGB(), Color.CYAN.getRGB());
            SkiaFontManager.getDefaultFont14().drawText(canvasStack, "" + prevTarget.hurtTime, width / 2f - 15.3f - (prevTarget.hurtTime == 10 ? 0.5f : 0), (float) -height / 2f + 20.5f, Color.WHITE.getRGB());
        }

//
//        SkiaRender.drawCircularProgress(canvasStack, -width / 2f + 65,(float) -height / 2f + 29,12,3, (float) (prevTarget.hurtTime / 10.0),new Color(0, 0, 0, 100).getRGB(), Color.RED.getRGB());

        if(prevTarget instanceof Player player) {
            SkiaRender.drawPlayerHead(canvasStack, player, -width / 2f + 5, (float) -height / 2f + 5, 28, 28,10);
            if(player.hurtTime > 0 && player.isAlive()) {
                SkiaRender.drawRoundedRect(canvasStack, -width / 2f + 5, (float) -height / 2f + 5, 28, 28, 10, new Color(255, 0, 0, 100).getRGB());
            }
        }

        float currentHealth = HeypixelManager.getEntityHealth(prevTarget);

        if (lastTarget != prevTarget) {
            smoothHealth = currentHealth;
            lastHealth = currentHealth;
            healthAnimation.reset();
            healthAnimation.setDirection(Direction.FORWARDS);
        }

        if (currentHealth != lastHealth) {
            healthAnimation.reset();
            healthAnimation.setDirection(Direction.FORWARDS);
            lastHealth = currentHealth;
        }
        float progress = (float) healthAnimation.getOutput();
        smoothHealth += (lastHealth - smoothHealth) * progress;

        smoothHealth = Math.max(0, Math.min(smoothHealth, prevTarget.getMaxHealth()));

        float maxHealth = prevTarget.getMaxHealth();


        smoothHealth += (currentHealth - smoothHealth) * 0.2f;
        if (smoothHealth < shadowHealth - 0.1f) {
            if (System.currentTimeMillis() - lastDamageTime > SHADOW_DELAY) {
                shadowHealth -= 0.5f;
                shadowHealth = Math.max(smoothHealth, shadowHealth);
            }
        } else {
            shadowHealth = smoothHealth;
            lastDamageTime = System.currentTimeMillis(); // 重置时间
        }


        SkiaRender.drawRoundedRect(canvasStack,
                -width / 2f + 5,
                (float) (-height / 2f + 36),
                width - 10,
                4,
                5,
                new Color(0, 0, 0, 100).getRGB());

        float shadowRate = shadowHealth / maxHealth;
        SkiaRender.drawRoundedRect(canvasStack,
                -width / 2f + 5,
                (float) (-height / 2f + 36),
                (width - 10) * shadowRate,
                4,
                5,
                new Color(255, 255, 255, 74).getRGB()); // 白色残影

        float rate = smoothHealth / maxHealth;
        SkiaRender.drawRoundedRect(canvasStack,
                -width / 2f + 5,
                (float) (-height / 2f + 36),
                (width - 10) * rate,
                4,
                5,
                AuraSyncHud.getColor(0, 0));

        canvasStack.pop();

        lastTarget = prevTarget;
    }
}
