package com.leave.ink.features.hud;

import com.leave.ink.Main;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import org.jetbrains.annotations.NotNull;

import java.util.Iterator;

public class DesignGuiScreen extends Screen implements IMinecraft {
    public static String selectedElement = "";

    public DesignGuiScreen(Component p_96550_) {
        super(p_96550_);
    }

    @Override
    protected void init() {
        super.init();
    }

    @Override
    public void render(@NotNull GuiGraphics p_96562_, int mouseX, int mouseY, float partialTicks) {
        super.render(p_96562_, mouseX, mouseY, partialTicks);
        try {
            for (AbsHudElement hudElement : Main.INSTANCE.hudManager.getHudElements()) {
                hudElement.inGuiDraw(p_96562_, mouseX, mouseY, partialTicks);
            }
        } catch (Exception ignored) {}

    }

    @Override
    public boolean mouseClicked(double p_94695_, double p_94696_, int p_94697_) {
        for (AbsHudElement hudElement : Main.INSTANCE.hudManager.getHudElements()) {
            hudElement.mouseClicked(p_94695_, p_94696_, p_94697_);
        }

        return super.mouseClicked(p_94695_, p_94696_, p_94697_);
    }

    @Override
    public boolean mouseReleased(double p_94722_, double p_94723_, int p_94724_) {
        for (AbsHudElement hudElement : Main.INSTANCE.hudManager.getHudElements()) {
            hudElement.mouseReleased(p_94722_, p_94723_, p_94724_);
        }
        return super.mouseReleased(p_94722_, p_94723_, p_94724_);
    }

    @Override
    public boolean mouseDragged(double p_94699_, double p_94700_, int p_94701_, double p_94702_, double p_94703_) {
        return super.mouseDragged(p_94699_, p_94700_, p_94701_, p_94702_, p_94703_);
    }

    @Override
    public boolean isPauseScreen() {
        return false;
    }
}
