package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import com.leave.ink.utils.Utils;
import net.minecraft.ChatFormatting;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.network.chat.TextColor;
import net.minecraft.world.scores.PlayerTeam;

import java.awt.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Tab栏玩家名字颜色解析工具类
 * 用于从PlayerInfo中提取和解析玩家名字的颜色信息
 */
public class TabColorUtils {
    
    // 默认颜色 - 白色
    private static final Color DEFAULT_COLOR = new Color(255, 255, 255);
    
    // §颜色代码的正则表达式
    private static final Pattern SECTION_COLOR_PATTERN = Pattern.compile("§([0-9a-fA-F])");
    
    // §x十六进制颜色的正则表达式 (§x§r§r§g§g§b§b格式)
    private static final Pattern HEX_COLOR_PATTERN = Pattern.compile("§x(§[0-9a-fA-F]){6}");
    
    /**
     * 从PlayerInfo中提取玩家名字的颜色
     *
     * @param playerInfo 玩家信息
     * @return 玩家名字的颜色，如果没有找到颜色则返回默认颜色
     */
    public static Color extractPlayerNameColor(PlayerInfo playerInfo) {
        if (playerInfo == null) {
            return DEFAULT_COLOR;
        }

        // 首先尝试从tabListDisplayName获取颜色
        Component displayName = playerInfo.getTabListDisplayName();
        if (displayName != null) {
            Color color = extractColorFromComponent(displayName);
            if (color != null) {
                return color;
            }

            // 如果Component方式失败，尝试从原始字符串解析
            String rawText = displayName.getString();
            if (rawText != null && !rawText.isEmpty()) {
                Color stringColor = extractColorFromString(rawText);
                if (stringColor != null) {
                    return stringColor;
                }
            }
        }

        // 如果没有tabListDisplayName或没有颜色信息，尝试从团队获取颜色
        PlayerTeam team = playerInfo.getTeam();
        if (team != null) {
            Color teamColor = extractTeamColor(team);
            if (teamColor != null) {
                return teamColor;
            }
        }

        // 如果都没有，返回默认颜色
        return DEFAULT_COLOR;
    }
    
    /**
     * 从Component中提取颜色信息
     * 
     * @param component 文本组件
     * @return 提取的颜色，如果没有找到则返回null
     */
    public static Color extractColorFromComponent(Component component) {
        if (component == null) {
            return null;
        }
        
        // 首先检查组件的样式
        Style style = component.getStyle();
        if (style != null) {
            TextColor textColor = style.getColor();
            if (textColor != null) {
                return new Color(textColor.getValue() | 0xFF000000);
            }
        }
        
        // 如果样式中没有颜色，尝试从字符串中解析
        String text = Utils.getStringFromFormattedCharSequence(component.getVisualOrderText());
        return extractColorFromString(text);
    }
    
    /**
     * 从字符串中提取颜色代码
     *
     * @param text 包含颜色代码的文本
     * @return 提取的颜色，如果没有找到则返回null
     */
    public static Color extractColorFromString(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }

        // 首先尝试解析十六进制颜色 (§x§r§r§g§g§b§b格式)
        Color hexColor = parseHexColor(text);
        if (hexColor != null) {
            return hexColor;
        }

        // 然后尝试解析§颜色代码
        Color sectionColor = parseSectionColor(text);
        if (sectionColor != null) {
            return sectionColor;
        }

        // 最后尝试解析其他格式的颜色代码
        Color otherColor = parseOtherColorFormats(text);
        if (otherColor != null) {
            return otherColor;
        }

        return null;
    }
    
    /**
     * 解析§x十六进制颜色代码
     * 格式: §x§r§r§g§g§b§b (每个颜色分量用两个§字符表示)
     * 
     * @param text 包含颜色代码的文本
     * @return 解析的颜色，如果解析失败则返回null
     */
    private static Color parseHexColor(String text) {
        Matcher matcher = HEX_COLOR_PATTERN.matcher(text);
        if (matcher.find()) {
            String hexPart = matcher.group();
            try {
                // 提取十六进制颜色值
                StringBuilder hexBuilder = new StringBuilder();
                for (int i = 2; i < hexPart.length(); i += 2) {
                    if (i + 1 < hexPart.length() && hexPart.charAt(i) == '§') {
                        hexBuilder.append(hexPart.charAt(i + 1));
                    }
                }
                
                if (hexBuilder.length() == 6) {
                    int rgb = Integer.parseInt(hexBuilder.toString(), 16);
                    return new Color(rgb | 0xFF000000);
                }
            } catch (NumberFormatException e) {
                // 解析失败，继续尝试其他方法
            }
        }
        return null;
    }
    
    /**
     * 解析§颜色代码 (§0-§f)
     *
     * @param text 包含颜色代码的文本
     * @return 解析的颜色，如果解析失败则返回null
     */
    private static Color parseSectionColor(String text) {
        // 查找最后一个颜色代码，因为后面的颜色会覆盖前面的
        Color lastColor = null;
        for (int i = 0; i < text.length() - 1; i++) {
            if (text.charAt(i) == '§') {
                char colorCode = text.charAt(i + 1);
                ChatFormatting formatting = ChatFormatting.getByCode(colorCode);
                if (formatting != null && formatting.isColor()) {
                    Integer colorValue = formatting.getColor();
                    if (colorValue != null) {
                        lastColor = new Color(colorValue | 0xFF000000);
                    }
                }
            }
        }
        return lastColor;
    }

    /**
     * 解析其他格式的颜色代码
     * 包括一些服务器插件使用的特殊格式
     *
     * @param text 包含颜色代码的文本
     * @return 解析的颜色，如果解析失败则返回null
     */
    private static Color parseOtherColorFormats(String text) {
        // 尝试解析&颜色代码 (一些插件使用&而不是§)
        String normalizedText = text.replace('&', '§');
        if (!normalizedText.equals(text)) {
            return parseSectionColor(normalizedText);
        }

        // 可以在这里添加更多的颜色格式支持
        // 例如: RGB(r,g,b), #RRGGBB 等

        return null;
    }
    
    /**
     * 从PlayerTeam中提取团队颜色
     * 
     * @param team 玩家团队
     * @return 团队颜色，如果没有找到则返回null
     */
    private static Color extractTeamColor(PlayerTeam team) {
        if (team == null) {
            return null;
        }
        
        ChatFormatting teamColor = team.getColor();
        if (teamColor != null && teamColor.isColor()) {
            Integer colorValue = teamColor.getColor();
            if (colorValue != null) {
                return new Color(colorValue | 0xFF000000);
            }
        }
        
        return null;
    }
    
    /**
     * 获取纯净的显示名称（移除颜色代码）
     * 
     * @param playerInfo 玩家信息
     * @return 纯净的显示名称
     */
    public static String getCleanDisplayName(PlayerInfo playerInfo) {
        if (playerInfo == null) {
            return "";
        }
        
        if (playerInfo.getTabListDisplayName() != null) {
            return ChatFormatting.stripFormatting(playerInfo.getTabListDisplayName().getString());
        } else {
            return playerInfo.getProfile().getName();
        }
    }
    
    /**
     * 检查颜色是否为默认颜色
     * 
     * @param color 要检查的颜色
     * @return 如果是默认颜色则返回true
     */
    public static boolean isDefaultColor(Color color) {
        return color.equals(DEFAULT_COLOR);
    }
    
    /**
     * 获取默认颜色
     * 
     * @return 默认颜色
     */
    public static Color getDefaultColor() {
        return DEFAULT_COLOR;
    }
}
