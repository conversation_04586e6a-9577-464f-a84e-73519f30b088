package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import net.minecraft.ChatFormatting;
import net.minecraft.client.multiplayer.PlayerInfo;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.network.chat.TextColor;
import net.minecraft.world.scores.PlayerTeam;

import java.awt.*;

public class TabColorUtils {
    private static final Color DEFAULT_COLOR = new Color(255, 255, 255);
    public static Color extractPlayerNameColor(PlayerInfo playerInfo) {
        if (playerInfo == null) {
            return DEFAULT_COLOR;
        }

        // 从tabListDisplayName获取颜色
        Component displayName = playerInfo.getTabListDisplayName();
        if (displayName != null) {
            Color color = getColorFromComponent(displayName);
            if (color != null) {
                return color;
            }
        }

        // 从团队获取颜色
        PlayerTeam team = playerInfo.getTeam();
        if (team != null && team.getColor().isColor()) {
            Integer teamColor = team.getColor().getColor();
            if (teamColor != null) {
                return new Color(teamColor | 0xFF000000);
            }
        }

        return DEFAULT_COLOR;
    }

    private static Color getColorFromComponent(Component component) {
        Style style = component.getStyle();
        if (style != null) {
            TextColor textColor = style.getColor();
            if (textColor != null) {
                return new Color(textColor.getValue() | 0xFF000000);
            }
        }
        return null;
    }

    public static String getCleanDisplayName(PlayerInfo playerInfo) {
        if (playerInfo == null) {
            return "";
        }

        if (playerInfo.getTabListDisplayName() != null) {
            return ChatFormatting.stripFormatting(playerInfo.getTabListDisplayName().getString());
        } else {
            return playerInfo.getProfile().getName();
        }
    }
    
}

