package com.leave.ink.features.hud.elements;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.ButtonSetting;

import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.mojang.blaze3d.vertex.PoseStack;
import io.github.humbleui.skija.Paint;
import io.github.humbleui.types.RRect;
import com.leave.ink.ui.skija.SkiaUtils;

import java.awt.Color;

public class TitleHud extends AbsHudElement {
    @SettingInfo(name = {
            @Text(label = "Delete", language = Language.English),
            @Text(label = "删除", language = Language.Chinese)
    })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };
    public TitleHud() {
        super("Title", 0, 0, 220, 70);
        registerSetting(delete);
    }
    private int i = 1;
    private long lastFrameTime = 0;


    @EventTarget
    public void onRender(EventRender2D e) {
        PoseStack poseStack = e.getPoseStack();
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        drawNormalContent(canvasStack);
    }

    private void drawNormalContent(CanvasStack canvasStack) {
        float rectWidth = 200;
        float rectHeight = 50;
        float cornerRadius = 10f;

        Paint strokePaint = new Paint()
            .setColor(new Color(255, 255, 255, 25).getRGB())
            .setAntiAlias(true)
            .setStroke(true)
            .setStrokeWidth(1.0f);

        RRect topRect = RRect.makeXYWH(
            SkiaUtils.transformCoord(10),
            SkiaUtils.transformCoord(10),
            SkiaUtils.transformCoord(rectWidth),
            SkiaUtils.transformCoord(rectHeight),
            cornerRadius
        );

        canvasStack.canvas.drawRRect(topRect, strokePaint);
        strokePaint.close();

        SkiaFontManager.getBigFont(22).drawText(canvasStack, "LEAVE Title", 25, 20, AuraSyncHud.getColor(0, 0));
        
        // 显示TTF logo - 动态字体大小，您设置多大就是多大
        //SkiaRender.drawTTFLogo(canvasStack, 180, 20, 60, AuraSyncHud.getColor(0, 0));
    }


}