package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import java.awt.*;

/**
 * Tab颜色功能测试类
 * 用于测试颜色解析功能是否正常工作
 */
public class TabColorTest {
    
    /**
     * 测试颜色解析功能
     */
    public static void testColorParsing() {
        System.out.println("=== Tab颜色解析功能测试 ===");
        
        // 测试§颜色代码
        testSectionColors();
        
        // 测试十六进制颜色
        testHexColors();
        
        // 测试&颜色代码
        testAmpersandColors();
        
        System.out.println("=== 测试完成 ===");
    }
    
    private static void testSectionColors() {
        System.out.println("\n--- 测试§颜色代码 ---");
        
        String[] testCases = {
            "§c红色玩家",
            "§a绿色玩家", 
            "§b青色玩家",
            "§e黄色玩家",
            "§f白色玩家",
            "§0黑色玩家",
            "§9蓝色玩家",
            "普通玩家", // 无颜色
            "§c§l粗体红色玩家", // 多个格式代码
            "前缀§c红色§r重置玩家" // 多个颜色代码
        };
        
        for (String testCase : testCases) {
            Color color = TabColorUtils.extractColorFromString(testCase);
            System.out.println("输入: \"" + testCase + "\" -> 颜色: " + colorToString(color));
        }
    }
    
    private static void testHexColors() {
        System.out.println("\n--- 测试十六进制颜色 ---");
        
        String[] testCases = {
            "§x§f§f§0§0§0§0红色玩家", // 红色 #FF0000
            "§x§0§0§f§f§0§0绿色玩家", // 绿色 #00FF00  
            "§x§0§0§0§0§f§f蓝色玩家", // 蓝色 #0000FF
            "§x§f§f§f§f§f§f白色玩家", // 白色 #FFFFFF
            "§x§a§b§c§d§e§f彩色玩家"  // 自定义颜色 #ABCDEF
        };
        
        for (String testCase : testCases) {
            Color color = TabColorUtils.extractColorFromString(testCase);
            System.out.println("输入: \"" + testCase + "\" -> 颜色: " + colorToString(color));
        }
    }
    
    private static void testAmpersandColors() {
        System.out.println("\n--- 测试&颜色代码 ---");
        
        String[] testCases = {
            "&c红色玩家",
            "&a绿色玩家",
            "&b青色玩家",
            "&e黄色玩家"
        };
        
        for (String testCase : testCases) {
            Color color = TabColorUtils.extractColorFromString(testCase);
            System.out.println("输入: \"" + testCase + "\" -> 颜色: " + colorToString(color));
        }
    }
    
    private static String colorToString(Color color) {
        if (color == null) {
            return "null";
        }
        return String.format("RGB(%d, %d, %d) [#%06X]", 
            color.getRed(), color.getGreen(), color.getBlue(), 
            color.getRGB() & 0xFFFFFF);
    }
    
    /**
     * 测试默认颜色功能
     */
    public static void testDefaultColor() {
        System.out.println("\n--- 测试默认颜色 ---");
        
        Color defaultColor = TabColorUtils.getDefaultColor();
        System.out.println("默认颜色: " + colorToString(defaultColor));
        
        // 测试是否为默认颜色的判断
        System.out.println("是否为默认颜色: " + TabColorUtils.isDefaultColor(defaultColor));
        System.out.println("红色是否为默认颜色: " + TabColorUtils.isDefaultColor(Color.RED));
    }
    
    /**
     * 模拟测试玩家名字颜色提取
     */
    public static void simulatePlayerNameColorExtraction() {
        System.out.println("\n--- 模拟玩家名字颜色提取 ---");
        
        // 这里可以添加模拟的PlayerInfo测试
        // 由于需要Minecraft环境，这里只是展示测试框架
        System.out.println("注意: 完整的PlayerInfo测试需要在Minecraft环境中运行");
    }
}
