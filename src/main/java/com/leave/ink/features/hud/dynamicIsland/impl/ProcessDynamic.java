package com.leave.ink.features.hud.dynamicIsland.impl;

import com.leave.ink.features.hud.dynamicIsland.DynamicElement;
import com.leave.ink.features.hud.main.AuraSyncHud;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.fbo.FrameBuffers;
import com.leave.ink.ui.skija.fbo.GameFrameBuffer;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.animation.impl.ContinualAnimation;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.platform.Lighting;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import io.github.humbleui.skija.ClipMode;
import lombok.Setter;
import net.minecraft.world.item.ItemStack;

import java.awt.*;

public class ProcessDynamic extends DynamicElement {
    private final ContinualAnimation continualAnimation = new ContinualAnimation();
    public int current, max;
    @Setter
    private int itemSlot = -1;
    @Setter
    private boolean keepAlive = false;

    public ProcessDynamic(String left, String right, int current, int max) {
        super(left, right, 2);
        this.current = current;
        this.max = max;
    }

    @Override
    public double getShiftHeight() {
        return 12;
    }

    @Override
    public double getShiftWidth() {
        return 30;
    }

    @Override
    public long getRetentionTime() {
        return keepAlive ? Long.MAX_VALUE : 400;
    }

    @Override
    public void left(CanvasStack canvasStack, float x, float y, float width, float height) {
        if(mc.player == null) return;

        float targetWidth = Math.min((float) current / max, 2) * (width - 100);
        continualAnimation.animate(targetWidth, 80);

        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer("Test");
        if(itemSlot != -1) {
            gameFrameBuffer.execute(eventRender2D -> {
                PoseStack poseStack = eventRender2D.getPoseStack();
                poseStack.pushPose();
                RenderSystem.enableDepthTest();
                RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
                Lighting.setupFor3DItems();

                ItemStack armourStack;
                armourStack = mc.player.getInventory().getItem(itemSlot);
                RenderUtils.renderItemStack(poseStack, armourStack, 0, 0);

                poseStack.popPose();
            });
        }


       // SkiaRender.drawCircle(canvasStack, 37, 37, 22, new Color(24, 24, 24, 140).getRGB());
        if(itemSlot != -1) {
            canvasStack.push();
            canvasStack.translate(10,10);
            gameFrameBuffer.draw(canvasStack);
            canvasStack.pop();
        }

        SkiaFontManager.getDefaultFont20().drawText(canvasStack, leftText, width - SkiaFontManager.getDefaultFont20().getWidth(leftText) - 10, 7, AuraSyncHud.getColor(0, 0));
        SkiaFontManager.getDefaultFont18().drawText(canvasStack, rightText, width - SkiaFontManager.getDefaultFont20().getWidth(rightText) - 10, 17, Color.GRAY.getRGB());
        SkiaRender.drawRoundedRect(canvasStack, 36, 17, width - 100, 3, 4, new Color(24, 24, 24, 140).getRGB());
        SkiaRender.drawRoundedRect(canvasStack, 36, 17, targetWidth, 3, 4, AuraSyncHud.getColor(0, 0));

    }

    @Override
    public void right(CanvasStack canvasStack, float x, float y, float width, float height) {}
}
