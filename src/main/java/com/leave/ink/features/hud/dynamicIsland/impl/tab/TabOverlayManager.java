package com.leave.ink.features.hud.dynamicIsland.impl.tab;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventTabOverlay;
import com.leave.ink.features.hud.HudManager;
import lombok.Getter;

@Getter
public class TabOverlayManager {
    private final TabOverlayDynamic tabOverlay;
    private boolean isTabVisible = false;

    public TabOverlayManager() {
        this.tabOverlay = new TabOverlayDynamic("Player", "");
        EventManager.register(this);
    }

    @EventTarget
    public void onTabOverlay(EventTabOverlay eventTabOverlay) {
        if (eventTabOverlay.visible) {
            showTab();
        } else {
            hideTab();
        }
        eventTabOverlay.visible = false;
    }

    private void showTab() {
        if (!isTabVisible) {
            tabOverlay.setKeepAlive(true);
            tabOverlay.sticky = true;
            HudManager.dynamicIsland.addTask(tabOverlay);
            isTabVisible = true;
        }
    }

    private void hideTab() {
        if (isTabVisible) {
            tabOverlay.setKeepAlive(false);
            tabOverlay.sticky = false;
            isTabVisible = false;
        }
    }
}
