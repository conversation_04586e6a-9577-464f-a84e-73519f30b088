package com.leave.ink.features.config.configs;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.leave.ink.Main;
import com.leave.ink.features.config.Config;
import com.leave.ink.features.config.ConfigManager;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;

import java.util.Map;

public class ModuleConfig extends Config {
    public ModuleConfig() {
        super("Modules");
    }

    @Override
    protected boolean onSave() {
        for (Module module : Main.INSTANCE.moduleManager.getModules()) {
            JsonObject jsonModule = new JsonObject();
            jsonModule.addProperty("toggled", module.isEnable());
            jsonModule.addProperty("key", module.getKey());

            if (!SettingManager.getSettings(module).isEmpty()) {
                SettingManager.getSettings(module).stream().filter(Setting::canSaveConfig).forEach(setting ->
                        jsonModule.addProperty(setting.getName(), setting.getConfigValue())
                );
            }
            json.add(module.getNameKey(), jsonModule);
        }
        return true;
    }

    @Override
    protected boolean onLoad(JsonObject jsonObject) throws Exception {
        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            Module module = Main.INSTANCE.moduleManager.getModule(entry.getKey());
            if (module == null) {
                continue;
            }
            JsonObject jsonObjectModule = (JsonObject) entry.getValue();
            boolean ena = jsonObjectModule.get("toggled").getAsBoolean();
            if(module.isEnable() != ena) {
                module.toggle();
            }
            module.setKey(jsonObjectModule.get("key").getAsInt());

            for (Setting<?> setting : SettingManager.getSettings(module)) {
                try {
                    JsonElement settingElement = jsonObjectModule.get(setting.getName());
                    ConfigManager.setValue(setting, settingElement);
                } catch (Exception ignored) {

                }
            }

        }
        return true;
    }
}
