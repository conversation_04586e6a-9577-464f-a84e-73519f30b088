package com.leave.ink.features.config;

import com.external.ui.ExternalUI;
import com.external.ui.UISystem;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.leave.ink.features.config.configs.HudConfig;
import com.leave.ink.features.config.configs.ModuleConfig;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.file.FileUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;

public class ConfigManager {
    public ArrayList<Config> configs = new ArrayList<>();
    public static Gson gsonPretty = new GsonBuilder().setPrettyPrinting().create();
    public static JsonObject json = new JsonObject();

    public ConfigManager() {
        add(new ModuleConfig(), new HudConfig());
    }

    public static ArrayList<String> getConfigs() {
        File dir = new File(Utils.getConfigPath());
        ArrayList<String> directories = new ArrayList<>();
        File[] files = dir.listFiles();
        if (files == null) return directories;
        for (File file : files) {
            if (file.isDirectory())
                continue;
            if (!FileUtils.getFileExtension(file).equals("json")) continue;
            directories.add(file.getName().substring(0, file.getName().lastIndexOf(".")));

        }
        return directories;
    }

    public boolean load() {
        AtomicInteger failedCount = new AtomicInteger();
        configs.forEach(it -> {
            try {
                if (!it.load())
                    failedCount.getAndIncrement();
            } catch (Exception e) {
                e.printStackTrace();
                failedCount.getAndIncrement();
            }

        });
        UISystem.updateModule();
        return failedCount.get() == 0;
    }

    public boolean save() {
        clear();
        gsonPretty = new GsonBuilder().setPrettyPrinting().create();
        json = new JsonObject();
        configs.forEach(it -> {
            it.jsonObject = new JsonObject();
            it.json = new JsonObject();
        });
        AtomicInteger failedCount = new AtomicInteger();

        configs.forEach(it -> {
            if (!it.onSave())
                failedCount.getAndIncrement();
        });
        configs.forEach(it -> json.add(it.getConfigName(), it.json));
        try {
            String text = ConfigManager.gsonPretty.toJson(json);
            byte[] buff = text.getBytes();
            FileOutputStream out = new FileOutputStream(Utils.getConfigPath() + "\\" +
                    ExternalUI.configName, true);
            out.write(buff);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return failedCount.get() == 0;
    }

    public void clear() {
        try {
            BufferedWriter bw = new BufferedWriter(new FileWriter(Utils.getConfigPath() + "\\" +
                    ExternalUI.configName));
            bw.write("");
            bw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static <T> void setValue(Setting<T> setting, JsonElement settingElement) {
        T object = setting.getJson(settingElement);
        if (object != null) {
//            System.out.println(setting.getName());
            setting.setValue(object);
        }
    }

    public void add(Config... configs) {
        this.configs.addAll(Arrays.asList(configs));
    }
}
