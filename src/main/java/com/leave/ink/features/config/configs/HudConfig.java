package com.leave.ink.features.config.configs;

import com.external.ui.ExternalUI;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.leave.ink.Main;
import com.leave.ink.features.config.Config;
import com.leave.ink.features.config.ConfigManager;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.Anchor;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;

import java.util.Map;

import static com.external.ui.ExternalUI.*;
import static com.external.ui.ExternalUI.UI_UpdateColorSettingsState;

public class HudConfig extends Config {
    public HudConfig() {
        super("Hud");
    }

    @Override
    protected boolean onLoad(JsonObject jsonObject) throws Exception {
        Main.INSTANCE.hudManager.clear();
        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {

            String elementName = entry.getKey();
            JsonObject jsonObjectModule = (JsonObject) entry.getValue();
            String className = jsonObjectModule.get("class").getAsString();
            boolean isMain = jsonObjectModule.get("main").getAsBoolean();
            Class<?> clazz = Class.forName(className);

            String anchor = jsonObjectModule.get("anchor").getAsString();
            double x = jsonObjectModule.get("x").getAsDouble();
            double y = jsonObjectModule.get("y").getAsDouble();
            double tempGuiWidth = jsonObjectModule.get("tempGuiWidth").getAsDouble();
            double tempGuiHeight = jsonObjectModule.get("tempGuiHeight").getAsDouble();
            AbsHudElement absHudElement = isMain ? Main.INSTANCE.hudManager.getHudElement(elementName) : Main.INSTANCE.hudManager.addElement(clazz.asSubclass(AbsHudElement.class));
//

            absHudElement.tempWidth = tempGuiWidth;
            absHudElement.tempHeight = tempGuiHeight;
            absHudElement.setAnchor(Anchor.valueOf(anchor));
            absHudElement.setXProportion(x);
            absHudElement.setYProportion(y);
//
            for (Setting<?> setting : SettingManager.getSettings(absHudElement)) {
                try {
                    JsonElement settingElement = jsonObjectModule.get(setting.getName());
                    ConfigManager.setValue(setting, settingElement);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            ExternalUI.execute(() -> {
                for (Setting<?> setting : SettingManager.getSettings(absHudElement)) {
                    if (setting instanceof BooleanSetting) {
                        UI_UpdateBooleanSettingsState(absHudElement.getElementName(), setting.getName(), ((BooleanSetting) setting).getValue());
                    } else if (setting instanceof NumberSetting) {
                        UI_UpdateNumberSettingsState(absHudElement.getElementName(), setting.getName(), ((NumberSetting) setting).getValue().doubleValue());
                    } else if (setting instanceof ModeSetting) {
                        UI_UpdateStringSettingsState(absHudElement.getElementName(), setting.getName(), ((ModeSetting) setting).getValue());
                        //System.out.println("ModeSetting " + setting.getName() + " " + setting.isDisplay());
                    }else if (setting instanceof ColorSetting) {
                        UI_UpdateColorSettingsState(absHudElement.getElementName(), setting.getName(), ((ColorSetting) setting).getValue());
                    }
                }
            });

        }
        return true;
    }

    @Override
    protected boolean onSave() {
        for (AbsHudElement element : Main.INSTANCE.hudManager.getHudElements()) {
            JsonObject jsonModule = new JsonObject();
            jsonModule.addProperty("class", element.getClass().getName());
            jsonModule.addProperty("main", element.isMainElement());
            jsonModule.addProperty("x", element.getXProportion());
            jsonModule.addProperty("y", element.getYProportion());
            jsonModule.addProperty("anchor",  element.getAnchor().name());
            jsonModule.addProperty("tempGuiWidth",  element.tempWidth);
            jsonModule.addProperty("tempGuiHeight",  element.tempHeight);

            if (!SettingManager.getSettings(element).isEmpty()) {
                SettingManager.getSettings(element).stream().filter(Setting::canSaveConfig).forEach(setting ->
                        jsonModule.addProperty(setting.getName(), setting.getConfigValue())
                );
            }
            json.add(element.getElementName(), jsonModule);
        }
        return true;
    }
}
