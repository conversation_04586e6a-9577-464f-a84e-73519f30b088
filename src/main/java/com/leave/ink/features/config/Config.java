package com.leave.ink.features.config;

import com.external.ui.ExternalUI;
import com.google.gson.JsonIOException;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.leave.ink.utils.Utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;


public abstract class Config {
    private String configName;
    public JsonObject json;
    public JsonObject jsonObject;


    public Config(String configName) {
        this.configName = configName;
        json = new JsonObject();
        jsonObject = new JsonObject();
        createFile();
    }


    public JsonObject toJsonByFile() {
        try {
            BufferedReader bufferedReader = new BufferedReader(new FileReader(getFileName()));
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = (JsonObject) jsonParser.parse(bufferedReader);
            bufferedReader.close();
            return jsonObject;
        } catch (IOException | JsonIOException | JsonSyntaxException | ClassCastException e) {
            return null;
        }
    }

    public boolean load() throws Exception {
        JsonObject jsonObj = toJsonByFile();
        if (jsonObj == null) return false;
        return onLoad((JsonObject) jsonObj.get(getConfigName()));
    }

    protected abstract boolean onLoad(JsonObject jsonObject) throws Exception;

    protected abstract boolean onSave();

    public String getFileName() {
        return Utils.getConfigPath() + "\\" + ExternalUI.configName;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public void createFile() {
        try {
            File n = new File(Utils.getConfigPath(),
                    ExternalUI.configName);
            if (!n.exists()) {
                n.createNewFile();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
