package com.leave.ink.features.command;

import com.leave.ink.utils.client.ChatUtils;
import net.minecraft.ChatFormatting;

public abstract class Command implements ICommand {
    protected final String[] args;
    public Command() {
        args = getUsage().split(" ");
    }

    public void preRunCommand(String[] args2) {
        if(args2.length != args.length) {
            ChatUtils.displayAlert(ChatFormatting.RED + "Invalid usage " + getUsage());
            return;
        }
        if (args.length - 1 >= 0) System.arraycopy(args2, 1, args, 1, args.length - 1);
        try {
            exec();
        }catch (Exception e) {
            ChatUtils.displayAlert(ChatFormatting.RED + "Error " + e.getMessage());
        }


    }
    public String getCommandName() {
        return args[0];
    }
    public abstract String getUsage();
    public abstract void exec() throws CommandExecException;
}
