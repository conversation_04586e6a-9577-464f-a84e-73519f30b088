package com.leave.ink.features.command.impl;

import com.leave.ink.Main;
import com.leave.ink.features.command.Command;
import com.leave.ink.features.command.CommandExecException;
import com.leave.ink.features.module.Module;
import org.lwjgl.glfw.GLFW;

public class BindCommand extends Command {
    @Override
    public String getUsage() {
        return PREFIX + "bind <module> <key>";
    }

    @Override
    public void exec() throws CommandExecException {
        String moduleName = args[1];
        String key = args[2];
        Module module = Main.INSTANCE.moduleManager.getModule(moduleName);
        if(module == null) throw new CommandExecException("不存在模块");
        int keycode = getKeyCode(key);
        if(keycode == -1) throw new CommandExecException("不存在按键");
        module.setKey(keycode);

    }
    private int getKeyCode(String keyName) {
        try {
            return (int) GLFW.class.getField("GLFW_KEY_" + keyName.toUpperCase()).get(null);
        } catch (Exception e) {
            return -1;
        }
    }
}
