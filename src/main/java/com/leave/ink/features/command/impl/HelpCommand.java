package com.leave.ink.features.command.impl;

import com.leave.ink.Main;
import com.leave.ink.features.command.Command;
import com.leave.ink.features.command.CommandExecException;
import com.leave.ink.utils.client.ChatUtils;
import net.minecraft.ChatFormatting;

public class HelpCommand extends Command {
    @Override
    public String getUsage() {
        return PREFIX + "help";
    }

    @Override
    public void exec() throws CommandExecException {
        for (String s : Main.INSTANCE.commandManager.commandMap.keySet()) {
            ChatUtils.displayAlert(ChatFormatting.AQUA + Main.INSTANCE.commandManager.commandMap.get(s).getUsage());
        }
    }

}
