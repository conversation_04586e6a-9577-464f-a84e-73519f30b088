package com.leave.ink.features.command.impl;

import com.leave.ink.Main;
import com.leave.ink.features.command.Command;
import com.leave.ink.features.command.CommandExecException;
import com.leave.ink.features.module.Module;

public class ToggleCommand extends Command {

    @Override
    public String getUsage() {
        return PREFIX + "t <module>";
    }

    @Override
    public void exec() throws CommandExecException {
        String moduleName = args[1];
        Module module = Main.INSTANCE.moduleManager.getModule(moduleName);
        if(module == null) throw new CommandExecException("不存在模块");
        module.toggle();
    }
}
