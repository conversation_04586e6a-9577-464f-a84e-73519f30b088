package com.leave.ink.features.command;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventChat;
import com.leave.ink.features.command.impl.BindCommand;
import com.leave.ink.features.command.impl.FriendCommand;
import com.leave.ink.features.command.impl.ToggleCommand;

import java.util.HashMap;
import java.util.Map;

public class CommandManager implements ICommand {
    public final Map<String, Command> commandMap = new HashMap<>();

    public CommandManager() {
        EventManager.register(this);
        addCommand(new BindCommand());
        addCommand(new ToggleCommand());
        addCommand(new FriendCommand());
    }
    public void addCommand(Command command) {
        commandMap.put(command.getCommandName(), command);
    }

    @EventTarget
    public void onChat(EventChat eventChat) {

        for (Command command : commandMap.values()) {
//            System.out.println(command.getCommandName());
            String[] args = eventChat.getMessage().split(" ");
            if (command.getCommandName().equalsIgnoreCase(args
                    [0])) {
                command.preRunCommand(args);
                eventChat.setCancelled(true);
                break;
            }
        }
    }
}
