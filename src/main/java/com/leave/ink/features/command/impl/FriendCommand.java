package com.leave.ink.features.command.impl;

import com.leave.ink.Main;
import com.leave.ink.features.command.Command;
import com.leave.ink.features.command.CommandExecException;
import com.leave.ink.utils.client.ChatUtils;

public class FriendCommand extends Command {
    @Override
    public String getUsage() {
        return PREFIX + "friend <name>";
    }

    @Override
    public void exec() throws CommandExecException {
        String name = args[1];

        if (name == null)
            throw new CommandExecException("不存在字符串");

        if (name.equals("clear")) {
            Main.INSTANCE.friendsManager.removeAll();
            ChatUtils.displayAlert("已清除好友列表");
        } else {
            if (!Main.INSTANCE.friendsManager.friends.contains(name)) {
                Main.INSTANCE.friendsManager.add(name);
                ChatUtils.displayAlert("§a§l" + name + "§c 已添加.");
            } else {
                Main.INSTANCE.friendsManager.remove(name);
                ChatUtils.displayAlert("§a§l" + name + "§c 已删除.");
            }
        }
    }
}
