package com.leave.ink.features.setting.settings;

import com.google.gson.JsonElement;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.attribute.SettingAttribute;

public class BooleanSetting extends Setting<Boolean> {
    public BooleanSetting(Boolean value) {
        super(value);
    }

    @SafeVarargs
    public BooleanSetting(Boolean value, SettingAttribute<Boolean>... settingAttributes) {
        super(value, settingAttributes);
    }

    @Override
    public boolean canSaveConfig() {
        return true;
    }

    @Override
    public Boolean getJson(JsonElement jsonElement) {
        return jsonElement.getAsBoolean();
    }
}
