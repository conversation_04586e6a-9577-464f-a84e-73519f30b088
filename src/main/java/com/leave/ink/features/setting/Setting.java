package com.leave.ink.features.setting;

import com.external.ui.UISystem;
import com.google.gson.JsonElement;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class Setting<T> {
    private Text[] texts;
    protected T value;
    @Setter
    @Getter
    public int level = 0;

    public boolean isHead = false;
    private SettingManager instance = null;
    private Setting<?> parent = null;
    private boolean display = true;
    private final List<Setting<?>> treeSetting = new ArrayList<>();

    public Setting(T value) {
        this.value = value;
    }

    @SafeVarargs
    public Setting(T value, SettingAttribute<T>... settingAttributes) {
        this.value = value;
        Arrays.stream(settingAttributes).forEach(it -> {
            it.setting().setParent(this);
            it.setting().setDisplay(it.get());
            SettingManager.attributeMap.put(it.setting(), it);
            this.treeSetting.add(it.setting());


        });
    }

    public void setInstance(SettingManager instance) {
        this.instance = instance;
    }

    public String getName(Language language) {
        return Language.getLabel(getTexts(), language);
    }

    public String getName() {
        return Language.getLabel(getTexts(), Language.getDefaultLanguage());
    }

    public String getNameKey() {
        return Language.getLabel(getTexts(), Language.getDefaultLanguage());
    }

    public boolean isDisplay() {
        return display;
    }

    public void setDisplay(boolean display) {
        this.display = display;
        //System.out.println(Arrays.toString(getTexts()));
        UISystem.api_setDisplay(this, instance);
    }

    public void setValue(T value) {
        if (onValueChanging()) {
            this.value = value;
            onValueChangedFirst();
        }
    }

    public boolean canSaveConfig() {
        return false;
    }

    public T getValue() {
        return value;
    }

    public String getConfigValue() {
        return value.toString();
    }

    protected boolean onValueChanging() {
        return true;
    }

    private void onValueChangedFirst() {
        SettingManager.updateDisplay(SettingManager.getSettings());
        onValueChanged();

        UISystem.UpdateSetting();

    }
    public int getAncestorCount() {
        int cnt = 0;
        Setting<?> p = this.parent;
        while (p != null) {
            cnt++;
            p = p.parent;
        }
        return cnt;
    }
    protected void onValueChanged() {

    }

    public T getJson(JsonElement jsonElement) {
        return null;
    }

    public Setting<?> getParent() {
        return parent;
    }

    public void setParent(Setting<?> parent) {
        this.parent = parent;
    }

    public List<Setting<?>> getTreeSetting() {
        return treeSetting;
    }

    public void setTexts(Text[] texts) {
        this.texts = texts;
    }

    public Text[] getTexts() {
        return texts;
    }
}
