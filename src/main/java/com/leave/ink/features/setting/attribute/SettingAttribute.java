package com.leave.ink.features.setting.attribute;

import com.leave.ink.features.setting.Setting;

/**
 * @param <T> setting type
 * <AUTHOR>
 */
public record SettingAttribute<T>(Setting<?> setting, T... value) {

    @SafeVarargs
    public SettingAttribute(Setting<?> setting, T... value) {
        this.setting = setting;
        this.value = value;
    }

    public boolean get() {
        for (T t : value)
            if (t.equals(setting.getParent().getValue())) return true;
        return value.equals(setting.getParent().getValue());
    }
}
