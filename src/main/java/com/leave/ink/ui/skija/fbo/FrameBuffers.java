package com.leave.ink.ui.skija.fbo;

import com.leave.ink.Main;
import com.mojang.blaze3d.systems.RenderSystem;

import java.util.HashMap;
import java.util.Map;

public class FrameBuffers {
    private static final Map<String, GameFrameBuffer> gameFrameBufferMap = new HashMap<>();
    public static void register(String name) {
        if(gameFrameBufferMap.containsKey(name))
            return;

        RenderSystem.recordRenderCall(() -> {
            GameFrameBuffer gameFrameBuffer = new GameFrameBuffer(Main.INSTANCE.skiaProcess.skiaUtils.context);
            gameFrameBuffer.init();
            gameFrameBufferMap.put(name, gameFrameBuffer);
        });
    }


    public static GameFrameBuffer getBuffer(String key) {
        return gameFrameBufferMap.get(key);
    }
}
