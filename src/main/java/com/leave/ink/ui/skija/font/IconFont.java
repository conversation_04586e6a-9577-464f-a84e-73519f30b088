package com.leave.ink.ui.skija.font;

import com.leave.ink.ui.skija.CanvasStack;
import net.minecraft.ChatFormatting;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class IconFont {
    private static final Map<String, String> ICON_MAP = new HashMap<>();
    private static final Pattern ICON_PATTERN = Pattern.compile("[✓✗\\u2713\\u2717\\u2714\\u2716\\u2718]");

    static {
        ICON_MAP.put("✓", "A");
        ICON_MAP.put("\u2713", "A");
        ICON_MAP.put("\u2714", "A");
        ICON_MAP.put("✗", "B");
        ICON_MAP.put("\u2717", "B");
        ICON_MAP.put("\u2716", "B");
        ICON_MAP.put("\u2718", "B");
    }

    public static boolean containsIcons(String text) {
        if (text == null || text.isEmpty()) return false;
        return ICON_PATTERN.matcher(text).find();
    }

    public static String getIconChar(String iconChar) {
        return ICON_MAP.getOrDefault(iconChar, iconChar);
    }

    public static void drawTextWithIcons(CanvasStack canvasStack, String text, float x, float y, int defaultColor, boolean shadow) {
        drawTextWithIcons(canvasStack, text, x, y, defaultColor, shadow, 14);
    }

    public static void drawTextWithIcons(CanvasStack canvasStack, String text, float x, float y, int defaultColor, boolean shadow, int fontSize) {
        if (text == null || text.isEmpty()) return;
        
        float currentX = x;
        StringBuilder currentSegment = new StringBuilder();
        ChatFormatting currentColor = null;
        boolean inColorCode = false;
        
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            String charStr = String.valueOf(c);
            if (c == '§' && i + 1 < text.length()) {
                 if (!currentSegment.isEmpty()) {
                     currentX = drawSegment(canvasStack, currentSegment.toString(), currentX, y, 
                                          currentColor != null ? (0xFF000000 | currentColor.getColor()) : defaultColor, 
                                          shadow, false, fontSize);
                     currentSegment.setLength(0);
                 }
                char colorChar = text.charAt(i + 1);
                currentColor = ChatFormatting.getByCode(colorChar);
                i++;
                continue;
            }
            if (ICON_MAP.containsKey(charStr)) {
                 if (!currentSegment.isEmpty()) {
                     currentX = drawSegment(canvasStack, currentSegment.toString(), currentX, y, 
                                          currentColor != null ? (0xFF000000 | currentColor.getColor()) : defaultColor, 
                                          shadow, false, fontSize);
                     currentSegment.setLength(0);
                 }
                 String iconChar = getIconChar(charStr);
                 int iconColor = getIconColor(charStr, currentColor != null ? (0xFF000000 | currentColor.getColor()) : defaultColor);
                 currentX = drawSegment(canvasStack, iconChar, currentX, y, iconColor, shadow, true, fontSize);
            } else {
                currentSegment.append(c);
            }
        }
        if (currentSegment.length() > 0) {
            drawSegment(canvasStack, currentSegment.toString(), currentX, y, 
                       currentColor != null ? (0xFF000000 | currentColor.getColor()) : defaultColor, 
                       shadow, false, fontSize);
        }
    }

    private static float drawSegment(CanvasStack canvasStack, String text, float x, float y, int color, boolean shadow, boolean isIcon, int fontSize) {
        if (text.isEmpty()) return x;
        SkiaFont font;
        if (isIcon) {
            font = fontSize >= 16 ? SkiaFontManager.getIconFont16() : SkiaFontManager.getIconFont14();
        } else {
            font = fontSize >= 16 ? SkiaFontManager.getDefaultFont16() : SkiaFontManager.getDefaultFont14();
        }
        
        font.drawText(canvasStack, text, x, y, color, shadow);
        return x + font.getWidth(text);
    }

    private static int getIconColor(String iconChar, int defaultColor) {
        if ("✓".equals(iconChar) || "\u2713".equals(iconChar) || "\u2714".equals(iconChar) || "A".equals(iconChar)) {
            return new Color(85, 255, 85).getRGB();
        }
        if ("✗".equals(iconChar) || "\u2717".equals(iconChar) || "\u2716".equals(iconChar) || 
            "\u2718".equals(iconChar) || "B".equals(iconChar)) {
            return new Color(255, 85, 85).getRGB();
        }
        
        return defaultColor;
    }

    public static float getTextWidthWithIcons(String text) {
        if (text == null || text.isEmpty()) return 0;
        float totalWidth = 0;
        StringBuilder currentSegment = new StringBuilder();
        
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            String charStr = String.valueOf(c);
            if (c == '§' && i + 1 < text.length()) {
                i++;
                continue;
            }
            if (ICON_MAP.containsKey(charStr)) {
                if (!currentSegment.isEmpty()) {
                    totalWidth += SkiaFontManager.getDefaultFont14().getWidth(currentSegment.toString());
                    currentSegment.setLength(0);
                }
                String iconChar = getIconChar(charStr);
                totalWidth += SkiaFontManager.getIconFont14().getWidth(iconChar);
            } else {
                currentSegment.append(c);
            }
        }
        if (!currentSegment.isEmpty()) {
            totalWidth += SkiaFontManager.getDefaultFont14().getWidth(currentSegment.toString());
        }
        
        return totalWidth;
    }

    public static String stripIcons(String text) {
        if (text == null || text.isEmpty()) return text;
        return ICON_PATTERN.matcher(text).replaceAll("");
    }
} 