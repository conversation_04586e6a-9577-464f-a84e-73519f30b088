package com.leave.ink.ui.skija;

import com.leave.ink.ui.skija.state.GLStateStack;
import com.leave.ink.ui.skija.utils.ImageHelper;
import com.leave.ink.utils.wrapper.IMinecraft;
import io.github.humbleui.skija.*;

import org.lwjgl.opengl.GL11;

public class SkiaUtils implements IMinecraft {

    public Surface surface;
    public BackendRenderTarget renderTarget;
    public Canvas canvas;
    public DirectContext context;

    private int currentSurfaceWidth = 0;
    private int currentSurfaceHeight = 0;

    public void initSkia() {
        try {

            createContext();
            createSurface();
        } catch (Exception e) {
            System.err.println("Skija初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }


    private void createContext() {
        if (context == null) {
            context = DirectContext.makeGL();
        }
    }

    private void createSurface() {

        if (surface != null) {
            surface.close();
            surface = null;
        }
        if (renderTarget != null) {
            renderTarget.close();
            renderTarget = null;
        }

        int width = (mc.getMainRenderTarget().width);
        int height = (mc.getMainRenderTarget().height);

        // 创建BackendRenderTarget
        renderTarget = BackendRenderTarget.makeGL(
                width,
                height,
                0,
                8,
                0,
                FramebufferFormat.GR_GL_RGBA8
        );


        surface = Surface.wrapBackendRenderTarget(
                context,
                renderTarget,
                SurfaceOrigin.BOTTOM_LEFT,
                SurfaceColorFormat.RGBA_8888,
                ColorSpace.getSRGB()
        );

        canvas = surface.getCanvas();

        // 保存当前尺寸
        currentSurfaceWidth = width;
        currentSurfaceHeight = height;
    }


    public void beginFrame() {
        if (context == null || surface == null) {
            throw new IllegalStateException("Skija未正确初始化");
        }

        GLStateStack.push();


        GL11.glDisable(GL11.GL_CULL_FACE);
        GL11.glClearColor(0f, 0f, 0f, 0f);


        resetSelectiveGLStates();
    }

    public void endFrame() {
        try {
            if (surface != null) {
                surface.flushAndSubmit();
            }
        } finally {
            // 恢复OpenGL状态
            try {
                GLStateStack.pop();
            } catch (Exception e) {
                System.err.println("恢复OpenGL状态时出错: " + e.getMessage());
                GLStateStack.clear(); // 清空栈以防止状态污染
            }
        }
    }

    private void resetSelectiveGLStates() {
        if (context != null) {
            GLBackendState[] states = {
                    GLBackendState.BLEND,
                    GLBackendState.VERTEX,
                    GLBackendState.PIXEL_STORE,
                    GLBackendState.TEXTURE_BINDING,
                    GLBackendState.MISC
            };
            context.resetGL(states);
        }
    }

    public static float transformCoord(float coordinate) {
        int guiScale = (int) mc.getWindow().getGuiScale();
        return (float) Math.ceil(coordinate * guiScale);
    }

    public static float transformCoordFromScaled(float scaledCoordinate) {
        int realWidth = mc.getWindow().getWidth();
        int scaledWidth = mc.getWindow().getGuiScaledWidth();
        double ratio = (double) scaledWidth / realWidth;
        return (float) (scaledCoordinate * ratio);
    }

    public void checkAndUpdateSurface() {
        if (surface != null && renderTarget != null) {
            int newWidth = (mc.getMainRenderTarget().width);
            int newHeight = (mc.getMainRenderTarget().height);

            if (currentSurfaceWidth != newWidth || currentSurfaceHeight != newHeight) {
                createSurface();
            }
        }
    }

    public void cleanup() {
        if (surface != null) {
            surface.close();
            surface = null;
        }
        if (renderTarget != null) {
            renderTarget.close();
            renderTarget = null;
        }
        if (context != null) {
            context.close();
            context = null;
        }
        canvas = null;

        ImageHelper.clearCache();
    }


}
