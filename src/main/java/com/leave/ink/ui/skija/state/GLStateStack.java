package com.leave.ink.ui.skija.state;

import java.util.Stack;


public class GLStateStack {
    
    private static final Stack<GLState> stateStack = new Stack<>();

    public static void push() {
        GLState state = new GLState();
        state.push();
        stateStack.push(state);
    }
    

    public static void pop() {
        if (stateStack.isEmpty()) {
            throw new IllegalStateException("没有状态可以恢复");
        }
        GLState state = stateStack.pop();
        state.pop();
    }

    public static boolean isEmpty() {
        return stateStack.isEmpty();
    }

    public static int size() {
        return stateStack.size();
    }

    public static void clear() {
        stateStack.clear();
    }
} 