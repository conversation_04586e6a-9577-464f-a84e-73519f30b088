package com.leave.ink.ui.skija.font;

import com.leave.ink.utils.Utils;
import io.github.humbleui.skija.Data;
import io.github.humbleui.skija.Typeface;

import java.util.concurrent.ConcurrentHashMap;

public class SkiaFontManager {
    private static final ConcurrentHashMap<String, SkiaFont> fontCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Typeface> typefaceCache = new ConcurrentHashMap<>();

    private static final String DEFAULT_FONT = "misans.ttf";
    private static final String ICON_FONT = "icons2.ttf";

    public static SkiaFont getFont(String fontName, int size) {
        String cacheKey = fontName + ":" + size;
        return fontCache.computeIfAbsent(cacheKey, key -> createFont(fontName, size));
    }
    public static SkiaFont getFont(int size) {
        return getFont(DEFAULT_FONT, size);
    }
    public static SkiaFont getIconFont(int size) {
        return getFont(ICON_FONT, size);
    }
    private static SkiaFont createFont(String fontName, int size) {
        try {
            Typeface typeface = typefaceCache.get(fontName);
            if (typeface == null) {
                Data data = Data.makeFromFileName(Utils.getResourcePath() + "\\fonts\\" + fontName);
                typeface = Typeface.makeFromData(data);
                if (typeface != null) {
                    typefaceCache.put(fontName, typeface);
                }
            }
            
            if (typeface != null) {
                return SkiaFont.createSkiaFont(fontName, size);
            }
        } catch (Exception e) {
            System.err.println("创建字体失败: " + fontName + ", 大小: " + size + ", 错误: " + e.getMessage());
        }
        if (!fontName.equals(DEFAULT_FONT)) {
            return SkiaFont.createSkiaFont(size);
        }
        return SkiaFont.createSkiaFont(size);
    }
    public static void clearCache() {
        fontCache.clear();
    }
    public static void reloadAll() {
        fontCache.clear();
        typefaceCache.clear();
        System.out.println("所有字体缓存已清理，将在下次使用时重新加载");
    }
    public static String getCacheStats() {
        return String.format("字体缓存: %d, Typeface缓存: %d", 
                fontCache.size(), typefaceCache.size());
    }
    public static SkiaFont getDefaultFont8() { return getFont(8); }
    public static SkiaFont getDefaultFont10() { return getFont(10); }
    public static SkiaFont getDefaultFont12() { return getFont(12); }
    public static SkiaFont getDefaultFont14() { return getFont(14); }
    public static SkiaFont getDefaultFont16() { return getFont(16); }
    public static SkiaFont getDefaultFont18() { return getFont(18); }
    public static SkiaFont getDefaultFont20() { return getFont(20); }
    public static SkiaFont getDefaultFont24() { return getFont(24); }
    public static SkiaFont getIconFont12() { return getIconFont(12); }
    public static SkiaFont getIconFont14() { return getIconFont(14); }
    public static SkiaFont getIconFont16() { return getIconFont(16); }
    public static SkiaFont getIconFont18() { return getIconFont(18); }
    public static SkiaFont getIconFont20() { return getIconFont(20); }
    public static SkiaFont getClassicFont(int size) { return getFont("classic.ttf", size); }
    public static SkiaFont getBigFont(int size) { return getFont("big.ttf", size); }
    public static SkiaFont getHarmonyFont(int size) { return getFont("harmony.ttf", size); }
    @Deprecated
    public static SkiaFont defaultFont24 = getDefaultFont24();
    @Deprecated 
    public static SkiaFont misans20 = getDefaultFont20();
    @Deprecated
    public static SkiaFont misans10 = getDefaultFont10();
    @Deprecated
    public static SkiaFont misans16 = getDefaultFont16();
    @Deprecated
    public static SkiaFont misans14 = getDefaultFont14();
    @Deprecated
    public static SkiaFont misans12 = getDefaultFont12();
    @Deprecated
    public static SkiaFont misans18 = getDefaultFont18();
    @Deprecated
    public static SkiaFont misans8 = getDefaultFont8();
    @Deprecated
    public static SkiaFont icons18 = getIconFont18();
    @Deprecated
    public static SkiaFont icons16 = getIconFont16();
    @Deprecated
    public static SkiaFont icons14 = getIconFont14();
    @Deprecated
    public static SkiaFont icons12 = getIconFont12();
    @Deprecated
    public static SkiaFont icons20 = getIconFont20();
    @Deprecated
    public static void reloadAllFonts() {
        reloadAll();
    }
}
