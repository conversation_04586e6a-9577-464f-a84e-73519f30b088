package com.leave.ink.ui.notification.impl;

import com.leave.ink.ui.notification.AbsNotification;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import io.github.humbleui.skija.ClipMode;

import java.awt.*;

public class ModuleNotification extends AbsNotification {
    private final boolean enable;
    public ModuleNotification(boolean enable, String content) {
        super(enable ? "icons8_done_52px.png" : "icons8_close_52px.png", "Module", content);
        this.enable = enable;
    }
    public void render(CanvasStack canvasStack) {
//        SkiaFontManager.getDefaultFont18().drawText(canvasStack,"TestTest" + ChatFormatting.GREEN + "GASDASD" + ChatFormatting.RED + "123131f", getX(), getY() + 7,Color.GRAY.getRGB());
//        SkiaFontManager.getDefaultFont18().drawText(canvasStack,"GGGG", getX() + SkiaFontManager.getDefaultFont18().getWidth("TestTest"), getY() + 7,Color.GRAY.getRGB());

        Color color = enable ? new Color(0,255,0,(int)( yAnimation.getOutput() * 168)) : new Color(255,0,0,(int)( yAnimation.getOutput() * 168));
//        RenderUtils.drawBlur(getX(),getY(), getWidth(), getHeight(), event.getPartialTicks());
//        SkiaRender.drawRoundedRect(canvasStack, getX(), getY(), getWidth(), getHeight(), 5, new Color(24,24,24,(int)( yAnimation.getOutput() * 168)).getRGB());
        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, getX(), getY(), getWidth(), getHeight(),5, ClipMode.DIFFERENCE);
        SkiaRender.drawRoundedRectWithShadow(canvasStack, getX(), getY(), getWidth(), getHeight(),2);
        canvasStack.pop();

        canvasStack.push();
        SkiaRender.scissorRoundedRect(canvasStack, getX(), getY(), getWidth(), getHeight(),5, ClipMode.INTERSECT);
        SkiaRender.drawBlurRect(canvasStack, getX(), getY(), getWidth(), getHeight(), 5, 10);
        SkiaRender.drawRoundedRect(canvasStack, getX(), getY(), getWidth(), getHeight(), 5, new Color(0,0,0,90).getRGB());
        float rate = (float) (System.currentTimeMillis() - startTime) / (float) getRetentionTime();
        SkiaRender.drawRoundedRect(canvasStack, getX(), getY() + getHeight() - 2, getWidth() * rate, 2, 5, color.getRGB());

//        RenderUtils.drawRect(poseStack, getX(), getY(), getX() + getWidth() * rate, getY() + getHeight(), color.getRGB());
//
        SkiaRender.drawImage(canvasStack, getIcon(), getX() + 4, getY() + 3, 25, 25);
        SkiaFontManager.getDefaultFont18().drawText(canvasStack, getTitle(), getX() + 32, getY() + 4, -1);
        SkiaFontManager.getDefaultFont16().drawText(canvasStack, getContent(), getX() + 32, getY() + 15, -1);

        canvasStack.pop();
        //        FontRenderers.sf_bold20.drawString(poseStack, getTitle(), getX() + 32, getY() + 8,-1);
//        FontRenderers.sf_bold.drawString(poseStack, getContent(), getX() + 32, getY() + 18,-1);

    }
}
