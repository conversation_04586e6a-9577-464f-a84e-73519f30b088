package com.leave.ink.ui.notification;


import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.ease.EaseBackIn;
import com.leave.ink.utils.animation.impl.ease.EaseInOutQuad;
import com.leave.ink.utils.timer.TimeUtils;

public class AbsNotification implements IMinecraft {
    public NotificationState state = NotificationState.IN;
    private final String title, icon, content;
    public TimeUtils timeUtils = new TimeUtils();
    public Animation inAnimation = new EaseBackIn(300, 1.0d,1f, Direction.FORWARDS);
    public Animation yAnimation = new EaseInOutQuad(300, 1.0d,Direction.FORWARDS);
    private float x=0, y=0, width = 100, height = 30;

    protected long startTime = 0;
    public AbsNotification(String icon, String title, String content) {
        this.title = title;
        this.icon = icon;
        this.content = content;
        timeUtils.reset();
        startTime = System.currentTimeMillis();
        width = SkiaFontManager.getDefaultFont18().getWidth(content) + 30 + 10;
        x = -width;
        y = mc.getWindow().getGuiScaledHeight() - height - 10;
    }
    public void render(CanvasStack canvasStack) {

//        RenderUtils.drawRect(poseStack, x, y, x + width, y + height, new Color(24,24,24,(int)( yAnimation.getOutput() * 168)).getRGB());
//         float rate = (float) (System.currentTimeMillis() - startTime) / (float) getRetentionTime();
//        RenderUtils.drawRect(poseStack, x, y, x + width * rate, y + height, new Color(24,24,24,(int)( yAnimation.getOutput() * 168)).getRGB());
//
//        RenderUtils.drawImage(getIcon(), poseStack, x + 4, y + 3, 25, 25);
//        FontRenderers.sf_bold20.drawString(poseStack, getTitle(), x + 32, y + 8,-1);
//        FontRenderers.sf_bold.drawString(poseStack, getContent(), x + 32, y + 18,-1);

    }

    public float getX() {
        return x;
    }

    public float getY() {
        return y;
    }

    public float getWidth() {
        return width;
    }

    public float getHeight() {
        return height;
    }

    public void setX(float x) {
        this.x = x;
    }

    public void setY(float y) {
        this.y = y;
    }

    public void setWidth(float width) {
        this.width = width;
    }

    public void setHeight(float height) {
        this.height = height;
    }

    public long getRetentionTime() {
        return 1400;
    }
    public String getTitle() {
        return title;
    }

    public String getIcon() {
        return icon;
    }

    public String getContent() {
        return content;
    }
}
