package com.leave.ink.ui.notification;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.hud.EventSkiaProcess;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.animation.Direction;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

public class NotificationManager implements IMinecraft {

    private final List<AbsNotification> notificationList = new ArrayList<>();
    public NotificationManager() {
        EventManager.register(this);
    }
    public void add(AbsNotification notification) {
        notificationList.add(notification);
    }

    @EventTarget
    public void onRender(EventSkiaProcess eventSkiaProcess) {
        ListIterator<AbsNotification> iterator = notificationList.listIterator();
        float y = mc.getWindow().getGuiScaledHeight() -50;
        while (iterator.hasNext()) {
            AbsNotification notification = iterator.next();

            switch (notification.state) {
                case IN -> {
                    notification.inAnimation.setDirection(Direction.FORWARDS);
                    notification.yAnimation.setDirection(Direction.FORWARDS);
                    if (notification.inAnimation.isDone()){
                        notification.state = NotificationState.RETENTION;
                    }
                }
                case RETENTION -> {
                    if(notification.timeUtils.hasTimeElapsed(notification.getRetentionTime(), true)) {
                        notification.state = NotificationState.OUT;
                    }

                }
                case OUT -> {
                    notification.inAnimation.setDirection(Direction.BACKWARDS);
                    notification.yAnimation.setDirection(Direction.BACKWARDS);
                    if (notification.inAnimation.isDone() && notification.timeUtils.hasTimeElapsed(200, true)){
                        iterator.remove();
                    }
                }
            }
            CanvasStack canvasStack = eventSkiaProcess.getCanvasStack();
            canvasStack.push();
            float windowWidth = mc.getWindow().getGuiScaledWidth();
            AbsNotification preNotification = iterator.hasNext() ? notificationList.get(iterator.nextIndex()) : notification;
//            poseStack.translate(windowWidth - (float) notification.inAnimation.getOutput() * notification.getWidth() - 5, y ,0);

            notification.setX(windowWidth - (float) notification.inAnimation.getOutput() * notification.getWidth() - 5);
            notification.setY(y);

//            System.out.println(iterator.hasNext() + " " + preNotification.height);
            y -= notification.yAnimation.getOutput() * preNotification.getHeight() + 3;
            //notification.y -= (int) (notification.inAnimation.getOutput() *  notification.width);
            notification.render(canvasStack);
            canvasStack.pop();
        }
    }

}
