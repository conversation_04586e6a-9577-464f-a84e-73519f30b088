package com.leave.ink.ui.notification.impl;

import com.leave.ink.ui.notification.AbsNotification;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;
import com.leave.ink.utils.timer.TimeUtils;

import java.awt.*;

public class NVIDIANotification extends AbsNotification {
    public Animation inAnimation = new DecelerateAnimation(1, 1.0d, Direction.FORWARDS);
    private final TimeUtils timeUtils = new TimeUtils();
    public Animation maskAnimation = new DecelerateAnimation(1, 1.0d, Direction.FORWARDS);

    public NVIDIANotification(String content) {
        super("nvidia.png", "", content);
        setHeight(45);
        timeUtils.reset();
//        width += 45;
        setWidth(getWidth() + 45);
    }

    @Override
    public void render(CanvasStack canvasStack) {
        Color bg = new Color(0,2,4);
        Color color = new Color(118, 185, 0);
        if(inAnimation.getDuration() == 1) {
            if(timeUtils.hasTimeElapsed(200, true)) {
                inAnimation.setDuration(200);
                inAnimation.setDirection(Direction.BACKWARDS);
            }
        }

        if(inAnimation.isDone() && inAnimation.getDirection() == Direction.BACKWARDS) {
            maskAnimation.setDuration(200);
            maskAnimation.setDirection(Direction.BACKWARDS);
        }
        if(timeUtils.hasTimeElapsed(getRetentionTime() - 200, true)) {

            inAnimation.setDirection(Direction.FORWARDS);
        }
        Color maskBG = new Color(0,2,4, (int) (maskAnimation.getOutput() * 255));
        double anim = inAnimation.getDuration() == 1 ? 1 : inAnimation.getOutput();

        SkiaRender.drawRect(canvasStack, getX(), getY(), getWidth(), getHeight(), bg.getRGB());
//        RenderUtils.drawRect(poseStack, getX(), getY(), getX() + getWidth(), getY() + getHeight(), bg.getRGB());
        SkiaFontManager.getDefaultFont16().drawText(canvasStack,getContent(), getX() + 54, getY() + 16,Color.GRAY.getRGB());
//        FontRenderers.misans16.drawString(poseStack, getContent(), getX() + 54, getY() + 20,Color.GRAY.getRGB());
        SkiaRender.drawImage(canvasStack, getIcon(), getX() + 10, getY() + 5, 36, 36);
//        RenderUtils.drawImage(getIcon(), poseStack, getX() + 10, getY() + 5, 36, 36);
        SkiaRender.drawRect(canvasStack, getX(), getY(), getWidth(), getHeight(), maskBG.getRGB());
//        RenderUtils.drawRect(poseStack, getX(), getY(), getWidth(), getHeight(), maskBG.getRGB());//蒙版

        SkiaRender.drawRect(canvasStack,  getX(), getY(), (float) ((getWidth() * anim) + 3), getHeight(), color.getRGB());

//        RenderUtils.drawRect(poseStack, getX(), getY(), getX() + (getWidth() * anim) + 3, getY() + getHeight(), color.getRGB());


    }
}
