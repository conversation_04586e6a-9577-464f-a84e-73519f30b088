package com.leave.ink.ui.notification.impl;

import com.leave.ink.ui.notification.AbsNotification;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;

import java.awt.*;

public class ModuleNotification2 extends AbsNotification {
    private final boolean enable;
    public ModuleNotification2(boolean enable, String content) {
        super(enable ? "icons8_done_52px.png" : "icons8_close_52px.png", "Module", content);
        this.enable = enable;
        setHeight(20);
    }

    @Override
    public void render(CanvasStack canvasStack) {
        Color color = enable ? new Color(0,255,0,(int)( yAnimation.getOutput() * 255)) : new Color(255,0,0,(int)( yAnimation.getOutput() * 255));
//        RenderUtils.startDrawGlow(poseStack, getX(), getY(), getWidth(), getHeight(),
//                11, new Color(0,0,0,180)
////                                RenderUtils.drawRect(poseStack, (float) (scaledWidth - posX) - 9 - offsetX, finalY + 4, (float) (scaledWidth - posX) - 9 + fw + 3, (float) (finalY + 4 + fontHeight + 1.7f), Color.BLACK.getRGB())
//        );
//        RenderUtils.drawRoundedRect(poseStack, getX(), getY(), getWidth(), getHeight(), 1, color);
//        RenderUtils.endDrawGlow(poseStack, getX(), getY(), getWidth(), getHeight(),
//                11, new Color(0,0,0,180)
////                                RenderUtils.drawRect(poseStack, (float) (scaledWidth - posX) - 9 - offsetX, finalY + 4, (float) (scaledWidth - posX) - 9 + fw + 3, (float) (finalY + 4 + fontHeight + 1.7f), Color.BLACK.getRGB())
//        );
        SkiaRender.drawRoundedRectWithShadow(canvasStack, getX(), getY(), getWidth(), getHeight(),1);
//        SkiaRender.drawBlurRect(canvasStack, getX(), getY(), getWidth(), getHeight(),4, 20);
        SkiaFontManager.getDefaultFont18().drawText(canvasStack,getContent(), getX() + 21, getY() + 4,-1);
        SkiaRender.drawImage(canvasStack, getIcon(), getX() + 4, getY() + 3, 15, 15);
//        SkiaFontManager.icon18.drawText(canvasStack,enable ? "A" : "B", getX() + 4, getY() + 13,-1);
//        FontRenderers.misans18.drawString(poseStack, getContent(), getX() + 21, getY() + 7,-1);
//        RenderSystem.setShaderColor(color.getRed() / 255f, color.getGreen() / 255f, color.getBlue() / 255f, color.getAlpha() / 255f);
//        RenderUtils.drawImage(getIcon(), poseStack, getX() + 4, getY() + 3, 15, 15);

    }
}
