package com.leave.ink.injection.transformers.client;


import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventAttack;
import com.leave.ink.events.EventUseItem;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import net.minecraft.client.multiplayer.MultiPlayerGameMode;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;

import java.util.ListIterator;

@ClassTransformer(MultiPlayerGameMode.class)
public class MultiPlayerGameModeTransformer extends Transformer {
    @PushArgs(index = {1, 2}, opcode = {Opcodes.ALOAD, Opcodes.ALOAD})
    @InjectPoint(methodName = {"send", "m_129512_"})
    @Inject(methodName = {"attack", "m_105223_"}, desc = "(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/entity/Entity;)V")
    public static void attack(Player playerIn, Entity targetEntity) {
        EventManager.call(new EventAttack(targetEntity));
    }
//    @PushArgs(index = 1, opcode = ALOAD)
//    @Inject(methodName = {"useItem","m_233721_"}, desc = "(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;)Lnet/minecraft/world/InteractionResult;")
//    public static void useItem(Player playerIn) {
//        EventManager.call(new EventUseItem(playerIn.getYRot(), playerIn.getXRot()));
//    }
    public static EventUseItem callEvent(Player playerIn) {
        EventUseItem event = new EventUseItem(playerIn.getYRot(), playerIn.getXRot());
        EventManager.call(event);
        return event;
    }
    @ASM(methodName = {"useItem","m_233721_"}, desc = "(Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;)Lnet/minecraft/world/InteractionResult;")
    public void inject(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        int stack = methodNode.maxLocals + 1;
        list.add(new VarInsnNode(ALOAD, 1));
        list.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "callEvent", "(Lnet/minecraft/world/entity/player/Player;)Lcom/leave/ink/events/EventUseItem;"));
        list.add(new VarInsnNode(ASTORE, stack));
        methodNode.instructions.insert(methodNode.instructions.getFirst(), list);
//        m_146908_ ()F getYRot
//        m_146909_ ()F getXRot
//        methodNode.instructions.insertBefore(methodNode.instructions.get(methodNode.instructions.size() - 2), list);
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();

            if (instruction.getOpcode() == INVOKEVIRTUAL) {
                MethodInsnNode methodInsnNode = (MethodInsnNode) instruction;
                if (!methodInsnNode.owner.equals(Type.getInternalName(Player.class))) continue;
                if (methodInsnNode.name.equals("getYRot") || methodInsnNode.name.equals("m_146908_")) {
                    iterator.remove();
                    iterator.previous();//ALOAD 1
                    iterator.remove();//ALOAD 1

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventUseItem.class), "getYaw", "()F"));
                }
                if (methodInsnNode.name.equals("getXRot") || methodInsnNode.name.equals("m_146909_")) {
                    iterator.remove();
                    iterator.previous();//ALOAD 1
                    iterator.remove();//ALOAD 1

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventUseItem.class), "getPitch", "()F"));
                }
            }
        }
    }
}
