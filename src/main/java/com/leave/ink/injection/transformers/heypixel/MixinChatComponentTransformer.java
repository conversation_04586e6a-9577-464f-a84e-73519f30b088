package com.leave.ink.injection.transformers.heypixel;

import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassNameTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;

@ClassNameTransformer("com.heypixel.heypixel.mixin.MixinChatComponent")
public class MixinChatComponentTransformer extends Transformer {
    @ASM(methodName = {"m_93780_"}, desc = "(Lcom/mojang/blaze3d/vertex/PoseStack;I)V")
    public void inject(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        if (methodNode == null) return;

        AbstractInsnNode targetNode = null;
        for (AbstractInsnNode instruction : methodNode.instructions) {
            if (instruction.getOpcode() == Opcodes.INVOKEVIRTUAL) {
                MethodInsnNode methodInsnNode = (MethodInsnNode) instruction;
                if (methodInsnNode.name.equals("m_85837_") && methodInsnNode.desc.equals("(DDD)V")) {
                    targetNode = methodInsnNode;
                    break;
                }
            }
        }

        if (targetNode == null) return;
        callBackInfo.setAbstractInsnNode(targetNode);

        InsnList componentInstructions = new InsnList();
        componentInstructions.add(new LdcInsnNode("chat.queue"));
        componentInstructions.add(new InsnNode(Opcodes.DUP));
        componentInstructions.add(new VarInsnNode(Opcodes.ALOAD, 2));
        componentInstructions.add(new MethodInsnNode(
                Opcodes.INVOKESPECIAL,
                "net/minecraft/network/chat/TranslatableComponent",
                "<init>",
                "(Ljava/lang/String;[Ljava/lang/Object;)V",
                false
        ));
        componentInstructions.add(new VarInsnNode(Opcodes.ASTORE, 3));

        InsnList eventTextInstructions = new InsnList();
        eventTextInstructions.add(new TypeInsnNode(Opcodes.NEW, "com/leave/ink/events/EventText"));
        eventTextInstructions.add(new InsnNode(Opcodes.DUP));
        eventTextInstructions.add(new VarInsnNode(Opcodes.ALOAD, 3));
        eventTextInstructions.add(new MethodInsnNode(
                Opcodes.INVOKESPECIAL,
                "com/leave/ink/events/EventText",
                "<init>",
                "(Lnet/minecraft/util/FormattedCharSequence;)V",
                false
        ));

        eventTextInstructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                "com/darkmagician6/eventapi/EventManager",
                "call",
                "(Lcom/darkmagician6/eventapi/events/Event;)Lcom/darkmagician6/eventapi/events/Event;",
                false
        ));

        methodNode.instructions.insert(targetNode, componentInstructions);
        methodNode.instructions.insert(targetNode, eventTextInstructions);
        methodNode.maxStack += 4;
    }
}
