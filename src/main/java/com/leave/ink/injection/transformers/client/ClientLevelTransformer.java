package com.leave.ink.injection.transformers.client;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventEntityJoinWorld;
import com.leave.ink.events.EventWorld;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.asm.tree.*;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.world.entity.Entity;

import java.lang.reflect.Field;
import java.util.ListIterator;

@ClassTransformer(ClientLevel.class)
public class ClientLevelTransformer extends Transformer {
    @PushArgs(index = {0,2}, opcode = {ALOAD, ALOAD})
    @Inject(at = InsertPosition.HEAD, methodName = {"addEntity", "m_104739_"}, desc = "(ILnet/minecraft/world/entity/Entity;)V")
    public static void addEntity(ClientLevel instance, Entity entity) {
        EventManager.call(new EventEntityJoinWorld(instance, entity));
    }
    @PushArgs(index = 0, opcode = ALOAD)
    @Inject(at = InsertPosition.LAST, methodName = "<init>", desc = "(Lnet/minecraft/client/multiplayer/ClientPacketListener;Lnet/minecraft/client/multiplayer/ClientLevel$ClientLevelData;Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/core/Holder;IILjava/util/function/Supplier;Lnet/minecraft/client/renderer/LevelRenderer;ZJ)V")
    public static void init(ClientLevel instance) {
        EventManager.call(new EventWorld(instance));
    }

    public static int skipTicks = 0;

    public static boolean skipTicks(Entity entity) {
        if (skipTicks > 0 && entity == mc.player) {
            skipTicks--;
            return true;
        }
        return false;
    }
    public static int getSkipTicks() {
        return ClientLevelTransformer.skipTicks;
    }

    public static void setSkipTicks(int skipTicks) {
        ClientLevelTransformer.skipTicks = skipTicks;
    }
    @ASM(methodName = {"tickNonPassenger", "m_104639_"}, desc = "(Lnet/minecraft/world/entity/Entity;)V")
    public void tickNonPassenger(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        boolean ok = false;
        LabelNode continueLabel = new LabelNode();
        while (iterator.hasNext()) {

            AbstractInsnNode instruction = iterator.next();
            //handler$zbd000$tickEntity
            if(instruction.getOpcode() == INVOKEVIRTUAL && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("handler$zbd000$tickEntity") && methodInsnNode.owner.equals("net/minecraft/client/multiplayer/ClientLevel")) {
                    iterator.remove();
                    iterator.add(new MethodInsnNode(
                            INVOKESTATIC,
                            Type.getInternalName(getClass()),
                            "handle",
                            "(Lnet/minecraft/client/multiplayer/ClientLevel;Lnet/minecraft/world/entity/Entity;Ljava/lang/Object;)V"
                    ));

                    break;
                }
            }
            if(instruction.getOpcode() == GETFIELD && instruction instanceof FieldInsnNode fieldInsnNode) {
                //f_19797_
                if (fieldInsnNode.name.equals("f_19797_") || fieldInsnNode.name.equals("tickCount")) {

//                    iterator.add(new VarInsnNode(ALOAD, 1));
//                    iterator.add(new MethodInsnNode(
//                            INVOKESTATIC,
//                            Type.getInternalName(getClass()),
//                           "skipTicks",
//                            "(Lnet/minecraft/world/entity/Entity;)Z"
//                    ));
//                    iterator.add(new JumpInsnNode(IFEQ, continueLabel));
//                    iterator.add(new InsnNode(RETURN));
//                    iterator.add(continueLabel);
                    list.add(new VarInsnNode(ALOAD, 1));
                    list.add(new MethodInsnNode(
                            INVOKESTATIC,
                            Type.getInternalName(getClass()),
                            "skipTicks",
                            "(Lnet/minecraft/world/entity/Entity;)Z",
                            false
                    ));
                    list.add(new JumpInsnNode(IFEQ, continueLabel));
                    list.add(new InsnNode(RETURN));
                    list.add(continueLabel);
                    methodNode.instructions.insert(fieldInsnNode, list);
                    break;
                }
            }
        }
    }
    public static void handle(ClientLevel level, Entity entity, Object info) {
        try {
            Class<?> clazz2 = Class.forName("dev.tr7zw.entityculling.EntityCullingModBase");
            Field instance = clazz2.getDeclaredField("instance");
            Object instObj = instance.get(null);
            Field renderedEntitiesF = clazz2.getField("tickedEntities");
            int renderedEntitiesObj = (int) renderedEntitiesF.get(instObj);
            renderedEntitiesF.set(instObj, renderedEntitiesObj + 1);
        }catch (Exception e) {
            e.printStackTrace();
        }


    }
}

