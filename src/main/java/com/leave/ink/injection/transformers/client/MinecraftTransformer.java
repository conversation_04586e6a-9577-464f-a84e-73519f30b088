package com.leave.ink.injection.transformers.client;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventPlaceBlock;
import com.leave.ink.events.EventTick;
import com.leave.ink.events.EventType;
import com.leave.ink.features.module.modules.other.ClayGuns;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.InjectPoint;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.mojang.blaze3d.platform.Window;
import net.minecraft.client.Minecraft;
import net.minecraftforge.event.ForgeEventFactory;
import java.util.ListIterator;

@ClassTransformer(Minecraft.class)
public class MinecraftTransformer extends Transformer {
    private static long lastFrameTime = System.nanoTime();
    private static double frameIntervalMs;

    @ASM(methodName = {"tick", "m_91398_"}, desc = "()V")
    public void tick(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        boolean removing = false;
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if (removing)
                iterator.remove();

            if (instruction.getOpcode() == INVOKESTATIC && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("onPreClientTick") && methodInsnNode.owner.equals("net/minecraftforge/event/ForgeEventFactory")) {
                    removing = true;
                    iterator.remove();
                    iterator.add(new VarInsnNode(ALOAD, 0));
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "onTick", "(Lnet/minecraft/client/Minecraft;)V"));
                }
            }

            if (instruction.getOpcode() == INVOKEINTERFACE && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.owner.equals("net/minecraft/util/profiling/ProfilerFiller") && (methodInsnNode.name.equals("popPush") || methodInsnNode.name.equals("m_6182_"))) {
                    break;
                }
            }
        }
    }

    public static void onTick(Minecraft instance) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");

        if (clayGuns.isEnable() && clayGuns.fastFire.getValue()) {
            clayGuns.onFastFire();
        } else {
            ForgeEventFactory.onPreClientTick();
            instance.getProfiler().push("gui");
            instance.getChatListener().tick();
            instance.gui.tick(instance.isPaused());
            instance.getProfiler().pop();
            instance.gameRenderer.pick(1.0F);
            instance.getTutorial().onLookAt(instance.level, instance.hitResult);
            instance.getProfiler().push("gameMode");
            if (!instance.isPaused() && instance.level != null) {
                instance.gameMode.tick();
            }
            instance.getProfiler().popPush("textures");
        }
    }

    public static void onFrame() {
        long currentFrameTime = System.nanoTime();
        long frameInterval = currentFrameTime - lastFrameTime;
        frameIntervalMs = frameInterval / 1_000_000.0;
        lastFrameTime = currentFrameTime;
    }

    public static double frameTime;
    private static boolean g = true;
    public static double lowFPS;

    public static void startGettingFrameTime() {
        new Thread(() -> {
            g = true;
            while (g) {

                try {
                    frameTime = frameIntervalMs;
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    public static void stopGettingFrameTime() {
        g = false;
    }

    public static void startGettingLowFPS() {
        new Thread(() -> {

        }).start();
    }

    @Inject(at = InsertPosition.HEAD, methodName = {"continueAttack", "m_91386_"}, desc = "(Z)V")
    public static void continueAttack() {
        EventManager.call(new EventPlaceBlock(EventType.POST));
    }

    @InjectPoint(methodName = {"isUsingItem", "m_6117_"}, desc = "()Z")
    @Inject(methodName = {"handleKeybinds", "m_91279_"}, desc = "()V")
    public static void handleKeybinds() {
        EventManager.call(new EventPlaceBlock(EventType.PRE));
    }

    @InjectPoint(methodName = {"bindWrite", "m_83947_"}, desc = "(Z)V")
    @Inject(methodName = {"runTick", "m_91383_"}, desc = "(Z)V")
    public static void runTickS() {
        EventManager.call(new EventTick(EventType.MIDDLE));
    }

    @Inject(at = InsertPosition.HEAD, methodName = {"runTick", "m_91383_"}, desc = "(Z)V")
    public static void runTickPre() {
        EventManager.call(new EventTick(EventType.PRE));
        checkWindowSizeChange();
        onFrame();
    }

    @Inject(at = InsertPosition.LAST, methodName = {"runTick", "m_91383_"}, desc = "(Z)V")
    public static void runTickPost() {
        EventManager.call(new EventTick(EventType.POST));

    }

    private static int lastWidth = -1;
    private static int lastHeight = -1;

    public static void checkWindowSizeChange() {
        Window window = mc.getWindow();
        int currentWidth = window.getWidth();
        int currentHeight = window.getHeight();

        if (currentWidth != lastWidth || currentHeight != lastHeight) {
            lastWidth = currentWidth;
            lastHeight = currentHeight;
        }
    }
}
