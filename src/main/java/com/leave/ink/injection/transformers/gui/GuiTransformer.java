package com.leave.ink.injection.transformers.gui;

import com.darkmagician6.eventapi.EventManager;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.leave.ink.Main;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.injection.base.util.asm.tree.AbstractInsnNode;
import com.leave.ink.injection.base.util.asm.tree.InsnList;
import com.leave.ink.injection.base.util.asm.tree.MethodInsnNode;
import com.leave.ink.injection.base.util.asm.tree.MethodNode;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.events.EventTabOverlay;
import com.leave.ink.features.module.modules.settings.ClientSetting;
import com.leave.ink.features.module.modules.world.AutoTool;
import com.leave.ink.features.module.modules.world.Scaffold;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.datafixers.util.Pair;
import net.minecraft.ChatFormatting;
import net.minecraft.client.AttackIndicatorStatus;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.Gui;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.scores.Objective;
import net.minecraft.world.scores.PlayerTeam;
import net.minecraft.world.scores.Score;
import net.minecraft.world.scores.Scoreboard;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import java.util.Collection;
import java.util.List;
import java.util.ListIterator;
import java.util.stream.Collectors;

@SuppressWarnings("all")
@ClassTransformer(Gui.class)
public class GuiTransformer extends Transformer {
    protected static final ResourceLocation VIGNETTE_LOCATION = new ResourceLocation("textures/misc/vignette.png");
    protected static final ResourceLocation WIDGETS_LOCATION = new ResourceLocation("textures/gui/widgets.png");
    protected static final ResourceLocation PUMPKIN_BLUR_LOCATION = new ResourceLocation("textures/misc/pumpkinblur.png");
    protected static final ResourceLocation SPYGLASS_SCOPE_LOCATION = new ResourceLocation("textures/misc/spyglass_scope.png");
    protected static final ResourceLocation POWDER_SNOW_OUTLINE_LOCATION = new ResourceLocation("textures/misc/powder_snow_outline.png");
    protected static final ResourceLocation GUI_ICONS_LOCATION = new ResourceLocation("textures/gui/icons.png");

    public static boolean setVisible(boolean v) {
        EventTabOverlay eventTabOverlay = new EventTabOverlay(v);
        EventManager.call(eventTabOverlay);
        return false;
    }

    @ASM(methodName = {"render", "m_281312_"}, desc = "(Lnet/minecraft/client/gui/GuiGraphics;F)V")
    public void render(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();

        int in = 0;

        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if (instruction.getOpcode() == INVOKEVIRTUAL && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("setVisible") || methodInsnNode.name.equals("m_94556_")) {
                    iterator.previous();
                    ++in;
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "setVisible", "(Z)Z", false));
                    iterator.next();
                    continue;
                }
            }
        }
    }

    @Overwrite(methodName = {"renderHotbar", "m_280518_"}, desc = "(FLnet/minecraft/client/gui/GuiGraphics;)V")
    public static void renderHotbar(Gui instance, float p_283031_, GuiGraphics p_282108_) {
        AutoTool autoTool = (AutoTool) Main.INSTANCE.moduleManager.getModule("AutoTool");
        Scaffold scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");
        int screenWidth = ObfuscationReflectionHelper.getPrivateValue(Gui.class, instance, "screenWidth");
        int screenHeight = ObfuscationReflectionHelper.getPrivateValue(Gui.class, instance, "screenHeight");
        Player player = getCameraPlayer();
        if (player != null) {
            int selected = autoTool.isEnable() && autoTool.clicking ? autoTool.fakeItemSlot : scaffold.isEnable() ? scaffold.fakeItemSlot : player.getInventory().selected;
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            RenderSystem.setShader(GameRenderer::getPositionTexShader);
            RenderSystem.setShaderTexture(0, new ResourceLocation("textures/gui/widgets.png"));
            ItemStack itemstack = player.getOffhandItem();
            HumanoidArm humanoidarm = player.getMainArm().getOpposite();
            int i = screenWidth / 2;
            int j = 182;
            p_282108_.pose().pushPose();
            p_282108_.pose().translate(0.0F, 0.0F, -90.0F);
            p_282108_.blit(WIDGETS_LOCATION, i - 91, screenHeight - 22, 0, 0, 182, 22);
            p_282108_.blit(WIDGETS_LOCATION, i - 91 - 1 + selected * 20, screenHeight - 22 - 1, 0, 22, 24, 22);
            if (!itemstack.isEmpty()) {
                if (humanoidarm == HumanoidArm.LEFT) {
                    p_282108_.blit(WIDGETS_LOCATION, i - 91 - 29, screenHeight - 23, 24, 22, 29, 24);
                } else {
                    p_282108_.blit(WIDGETS_LOCATION, i + 91, screenHeight - 23, 53, 22, 29, 24);
                }
            }

            p_282108_.pose().popPose();
            int l = 1;

            for (int i1 = 0; i1 < 9; ++i1) {
                int j1 = i - 90 + i1 * 20 + 2;
                int k1 = screenHeight - 16 - 3;
                renderSlot(p_282108_, j1, k1, p_283031_, player, player.getInventory().items.get(i1), l++);
            }

            if (!itemstack.isEmpty()) {
                int i2 = screenHeight - 16 - 3;
                if (humanoidarm == HumanoidArm.LEFT) {
                    renderSlot(p_282108_, i - 91 - 26, i2, p_283031_, player, itemstack, l++);
                } else {
                    renderSlot(p_282108_, i + 91 + 10, i2, p_283031_, player, itemstack, l++);
                }
            }

            RenderSystem.enableBlend();
            if (mc.options.attackIndicator().get() == AttackIndicatorStatus.HOTBAR) {
                float f = mc.player.getAttackStrengthScale(0.0F);
                if (f < 1.0F) {
                    int j2 = screenHeight - 20;
                    int k2 = i + 91 + 6;
                    if (humanoidarm == HumanoidArm.RIGHT) {
                        k2 = i - 91 - 22;
                    }

                    int l1 = (int) (f * 19.0F);
                    p_282108_.blit(GUI_ICONS_LOCATION, k2, j2, 0, 94, 18, 18);
                    p_282108_.blit(GUI_ICONS_LOCATION, k2, j2 + 18 - l1, 18, 112 - l1, 18, l1);
                }
            }

            RenderSystem.disableBlend();
            EventRender2D eventRender2D = new EventRender2D(p_282108_, p_282108_.pose(), mc.getWindow().getGuiScaledHeight(), mc.getWindow().getGuiScaledWidth());
            EventManager.call(eventRender2D);
        }
    }

    @Overwrite(methodName = {"renderSelectedItemName", "m_280295_"}, desc = "(Lnet/minecraft/client/gui/GuiGraphics;I)V")
    public static void renderSelectedItemName(Gui instance, GuiGraphics p_283501_, int yShift) {
        AutoTool autoTool = (AutoTool) Main.INSTANCE.moduleManager.getModule("AutoTool");
        Scaffold scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");
        int screenWidth = ObfuscationReflectionHelper.getPrivateValue(Gui.class, instance, "screenWidth");
        int screenHeight = ObfuscationReflectionHelper.getPrivateValue(Gui.class, instance, "screenHeight");
        int toolHighlightTimer = ObfuscationReflectionHelper.getPrivateValue(Gui.class, instance, "toolHighlightTimer");
        ItemStack lastToolHighlight = ObfuscationReflectionHelper.getPrivateValue(Gui.class, instance, "lastToolHighlight");
        ItemStack isNeedStack = autoTool.isEnable() && autoTool.clicking ? autoTool.getFakeCurrentItem() : scaffold.isEnable() ? scaffold.getFakeCurrentItem() : lastToolHighlight;

        mc.getProfiler().push("selectedItemName");
        if (toolHighlightTimer > 0 && !isNeedStack.isEmpty()) {
            MutableComponent mutablecomponent = Component.empty().append(isNeedStack.getHoverName()).withStyle(isNeedStack.getRarity().getStyleModifier());
            if (isNeedStack.hasCustomHoverName()) {
                mutablecomponent.withStyle(ChatFormatting.ITALIC);
            }
            Component highlightTip = isNeedStack.getHighlightTip(mutablecomponent);

            int i = mc.font.width(highlightTip);
            int j = (screenWidth - i) / 2;
            int k = screenHeight - Math.max(yShift, 59);
            if (!mc.gameMode.canHurtPlayer()) {
                k += 14;
            }

            int l = (int) ((float) toolHighlightTimer * 256.0F / 10.0F);
            if (l > 255) {
                l = 255;
            }

            if (l > 0) {
                p_283501_.fill(j - 2, k - 2, j + i + 2, k + 9 + 2, mc.options.getBackgroundColor(0));
                Font font = mc.font;//net.minecraftforge.client.extensions.common.IClientItemExtensions.of(isNeedStack).getFont(isNeedStack, net.minecraftforge.client.extensions.common.IClientItemExtensions.FontContext.SELECTED_ITEM_NAME);
                if (font == null) {
                    p_283501_.drawString(mc.font, highlightTip, j, k, 16777215 + (l << 24));
                } else {
                    j = (screenWidth - font.width(highlightTip)) / 2;
                    p_283501_.drawString(font, highlightTip, j, k, 16777215 + (l << 24));
                }
            }
        }

        mc.getProfiler().pop();
    }

    private static Player getCameraPlayer() {
        return !(mc.getCameraEntity() instanceof Player) ? null : (Player) mc.getCameraEntity();
    }

    private static void renderSlot(GuiGraphics p_283283_, int p_283213_, int p_281301_, float p_281885_, Player p_283644_, ItemStack p_283317_, int p_283261_) {
        if (!p_283317_.isEmpty()) {
            float f = (float) p_283317_.getPopTime() - p_281885_;
            if (f > 0.0F) {
                float f1 = 1.0F + f / 5.0F;
                p_283283_.pose().pushPose();
                p_283283_.pose().translate((float) (p_283213_ + 8), (float) (p_281301_ + 12), 0.0F);
                p_283283_.pose().scale(1.0F / f1, (f1 + 1.0F) / 2.0F, 1.0F);
                p_283283_.pose().translate((float) (-(p_283213_ + 8)), (float) (-(p_281301_ + 12)), 0.0F);
            }

            p_283283_.renderItem(p_283644_, p_283317_, p_283213_, p_281301_, p_283261_);
            if (f > 0.0F) {
                p_283283_.pose().popPose();
            }

            p_283283_.renderItemDecorations(mc.font, p_283317_, p_283213_, p_281301_);
        }
    }

    @Overwrite(methodName = {"displayScoreboardSidebar", "m_280030_"}, desc = "(Lnet/minecraft/client/gui/GuiGraphics;Lnet/minecraft/world/scores/Objective;)V")
    public static void displayScoreboardSidebar(Gui instance, GuiGraphics p_282008_, Objective p_283455_) {
        boolean hasCustomScoreboard = Main.INSTANCE.hudManager.getHudElements().stream()
                .anyMatch(element -> element.getClass().getSimpleName().equals("ScoreboardHud"));
        
        if (hasCustomScoreboard) return;

        Scoreboard scoreboard = p_283455_.getScoreboard();
        Collection<Score> collection = scoreboard.getPlayerScores(p_283455_);
        List<Score> list = (List) collection.stream().filter((p_93027_) -> p_93027_.getOwner() != null && !p_93027_.getOwner().startsWith("#")).collect(Collectors.toList());
        if (list.size() > 15) {
            collection = Lists.newArrayList(Iterables.skip(list, collection.size() - 15));
        } else {
            collection = list;
        }

        List<Pair<Score, Component>> list1 = Lists.newArrayListWithCapacity(collection.size());
        Component component = p_283455_.getDisplayName();
        int i = instance.getFont().width(component);
        int j = i;
        int k = instance.getFont().width(": ");
        int screenWidth = mc.getWindow().getGuiScaledWidth();
        int screenHeight = mc.getWindow().getGuiScaledHeight();
        for (Score score : collection) {
            PlayerTeam playerteam = scoreboard.getPlayersTeam(score.getOwner());
            Component component1 = PlayerTeam.formatNameForTeam(playerteam, Component.literal(score.getOwner()));
            list1.add(Pair.of(score, component1));
            j = Math.max(j, instance.getFont().width(component1) + k + instance.getFont().width(Integer.toString(score.getScore())));
        }

        int i2 = collection.size() * 9;
        int j2 = screenHeight / 2 + i2 / 3;
        int k2 = 3;
        int l2 = screenWidth - j - 3;
        int l = 0;
        int i1 = mc.options.getBackgroundColor(0.3F);
        int j1 = mc.options.getBackgroundColor(0.4F);

        for (Pair<Score, Component> pair : list1) {
            ++l;
            Score score1 = (Score) pair.getFirst();
            Component component2 = (Component) pair.getSecond();
            ChatFormatting var10000 = ChatFormatting.RED;
            String s = "" + var10000 + score1.getScore();
            int k1 = j2 - l * 9;
            int l1 = screenWidth - 3 + 2;
            p_282008_.fill(l2 - 2, k1, l1, k1 + 9, i1);
            p_282008_.drawString(instance.getFont(), component2, l2, k1, -1, false);
            p_282008_.drawString(instance.getFont(), s, l1 - instance.getFont().width(s), k1, -1, false);
            if (l == collection.size()) {
                p_282008_.fill(l2 - 2, k1 - 9 - 1, l1, k1 - 1, j1);
                p_282008_.fill(l2 - 2, k1 - 1, l1, k1, i1);
                p_282008_.drawString(instance.getFont(), component, l2 + j / 2 - i / 2, k1 - 9, -1, false);
            }
        }
    }
}
