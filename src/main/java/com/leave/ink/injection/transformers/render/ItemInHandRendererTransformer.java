package com.leave.ink.injection.transformers.render;

import com.darkmagician6.eventapi.EventManager;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import com.leave.ink.Main;
import com.leave.ink.events.EventRenderHand;
import com.leave.ink.features.module.modules.world.AutoTool;
import com.leave.ink.features.module.modules.world.Scaffold;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.client.renderer.ItemInHandRenderer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.*;

import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@ClassTransformer(ItemInHandRenderer.class)
public class ItemInHandRendererTransformer extends Transformer {
    @Overwrite(methodName = {"renderArmWithItem", "m_109371_"}, desc = "(Lnet/minecraft/client/player/AbstractClientPlayer;FFLnet/minecraft/world/InteractionHand;FLnet/minecraft/world/item/ItemStack;FLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderArmWithItem(ItemInHandRenderer instance, AbstractClientPlayer p_109372_, float p_109373_, float p_109374_, InteractionHand p_109375_, float p_109376_, ItemStack p_109377_, float p_109378_, PoseStack p_109379_, MultiBufferSource p_109380_, int p_109381_) {
        AutoTool autoTool = (AutoTool) Main.INSTANCE.moduleManager.getModule("AutoTool");
        Scaffold scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");
        ItemStack isNeedStack = p_109375_ != InteractionHand.MAIN_HAND ? p_109377_ : autoTool.isEnable() && autoTool.clicking ? autoTool.getFakeCurrentItem() : scaffold.isEnable() ? scaffold.getFakeCurrentItem() : p_109377_;

        if (!p_109372_.isScoping()) {
            boolean flag = p_109375_ == InteractionHand.MAIN_HAND;
            HumanoidArm humanoidarm = flag ? p_109372_.getMainArm() : p_109372_.getMainArm().getOpposite();
            p_109379_.pushPose();
            if (isNeedStack.isEmpty()) {
                if (flag && !p_109372_.isInvisible()) {
                    renderPlayerArm(instance, p_109379_, p_109380_, p_109381_, p_109378_, p_109376_, humanoidarm);
                }
            } else if (isNeedStack.is(Items.FILLED_MAP)) {
                ItemStack offHandItem = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "offHandItem");
                if (flag && offHandItem.isEmpty()) {
                    renderTwoHandedMap(instance, p_109379_, p_109380_, p_109381_, p_109374_, p_109378_, p_109376_);
                } else {
                    renderOneHandedMap(instance, p_109379_, p_109380_, p_109381_, p_109378_, humanoidarm, p_109376_, isNeedStack);
                }
            } else if (isNeedStack.getItem() instanceof CrossbowItem) {
                boolean flag1 = CrossbowItem.isCharged(isNeedStack);
                boolean flag2 = humanoidarm == HumanoidArm.RIGHT;
                int i = flag2 ? 1 : -1;
                if (p_109372_.isUsingItem() && p_109372_.getUseItemRemainingTicks() > 0 && p_109372_.getUsedItemHand() == p_109375_) {
                    applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                    p_109379_.translate((float) i * -0.4785682F, -0.094387F, 0.05731531F);
                    p_109379_.mulPose(Axis.XP.rotationDegrees(-11.935F));
                    p_109379_.mulPose(Axis.YP.rotationDegrees((float) i * 65.3F));
                    p_109379_.mulPose(Axis.ZP.rotationDegrees((float) i * -9.785F));
                    float f9 = (float) isNeedStack.getUseDuration() - ((float) mc.player.getUseItemRemainingTicks() - p_109373_ + 1.0F);
                    float f13 = f9 / (float) CrossbowItem.getChargeDuration(isNeedStack);
                    if (f13 > 1.0F) {
                        f13 = 1.0F;
                    }

                    if (f13 > 0.1F) {
                        float f16 = Mth.sin((f9 - 0.1F) * 1.3F);
                        float f3 = f13 - 0.1F;
                        float f4 = f16 * f3;
                        p_109379_.translate(f4 * 0.0F, f4 * 0.004F, f4 * 0.0F);
                    }

                    p_109379_.translate(f13 * 0.0F, f13 * 0.0F, f13 * 0.04F);
                    p_109379_.scale(1.0F, 1.0F, 1.0F + f13 * 0.2F);
                    p_109379_.mulPose(Axis.YN.rotationDegrees((float) i * 45.0F));
                } else {
                    float f = -0.4F * Mth.sin(Mth.sqrt(p_109376_) * (float) Math.PI);
                    float f1 = 0.2F * Mth.sin(Mth.sqrt(p_109376_) * ((float) Math.PI * 2F));
                    float f2 = -0.2F * Mth.sin(p_109376_ * (float) Math.PI);
                    p_109379_.translate((float) i * f, f1, f2);
                    applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                    applyItemArmAttackTransform(p_109379_, humanoidarm, p_109376_);
                    if (flag1 && p_109376_ < 0.001F && flag) {
                        p_109379_.translate((float) i * -0.641864F, 0.0F, 0.0F);
                        p_109379_.mulPose(Axis.YP.rotationDegrees((float) i * 10.0F));
                    }
                }

                instance.renderItem(p_109372_, isNeedStack, flag2 ? ItemDisplayContext.FIRST_PERSON_RIGHT_HAND : ItemDisplayContext.FIRST_PERSON_LEFT_HAND, !flag2, p_109379_, p_109380_, p_109381_);
            } else {
                boolean flag3 = humanoidarm == HumanoidArm.RIGHT;
//                if (!IClientItemExtensions.of(isNeedStack).applyForgeHandTransform(p_109379_, mc.player, humanoidarm, isNeedStack, p_109373_, p_109378_, p_109376_)) {
                    if (p_109372_.isUsingItem() && p_109372_.getUseItemRemainingTicks() > 0 && p_109372_.getUsedItemHand() == p_109375_) {
                        int k = flag3 ? 1 : -1;
                        switch (isNeedStack.getUseAnimation()) {
                            case NONE, BLOCK:
                                applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                                break;
                            case EAT:
                            case DRINK:
                                applyEatTransform(p_109379_, p_109373_, humanoidarm, isNeedStack);
                                applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                                break;
                            case BOW:
                                applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                                p_109379_.translate((float) k * -0.2785682F, 0.18344387F, 0.15731531F);
                                p_109379_.mulPose(Axis.XP.rotationDegrees(-13.935F));
                                p_109379_.mulPose(Axis.YP.rotationDegrees((float) k * 35.3F));
                                p_109379_.mulPose(Axis.ZP.rotationDegrees((float) k * -9.785F));
                                float f8 = (float) isNeedStack.getUseDuration() - ((float) mc.player.getUseItemRemainingTicks() - p_109373_ + 1.0F);
                                float f12 = f8 / 20.0F;
                                f12 = (f12 * f12 + f12 * 2.0F) / 3.0F;
                                if (f12 > 1.0F) {
                                    f12 = 1.0F;
                                }

                                if (f12 > 0.1F) {
                                    float f15 = Mth.sin((f8 - 0.1F) * 1.3F);
                                    float f18 = f12 - 0.1F;
                                    float f20 = f15 * f18;
                                    p_109379_.translate(f20 * 0.0F, f20 * 0.004F, f20 * 0.0F);
                                }

                                p_109379_.translate(f12 * 0.0F, f12 * 0.0F, f12 * 0.04F);
                                p_109379_.scale(1.0F, 1.0F, 1.0F + f12 * 0.2F);
                                p_109379_.mulPose(Axis.YN.rotationDegrees((float) k * 45.0F));
                                break;
                            case SPEAR:
                                applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                                p_109379_.translate((float) k * -0.5F, 0.7F, 0.1F);
                                p_109379_.mulPose(Axis.XP.rotationDegrees(-55.0F));
                                p_109379_.mulPose(Axis.YP.rotationDegrees((float) k * 35.3F));
                                p_109379_.mulPose(Axis.ZP.rotationDegrees((float) k * -9.785F));
                                float f7 = (float) isNeedStack.getUseDuration() - ((float) mc.player.getUseItemRemainingTicks() - p_109373_ + 1.0F);
                                float f11 = f7 / 10.0F;
                                if (f11 > 1.0F) {
                                    f11 = 1.0F;
                                }

                                if (f11 > 0.1F) {
                                    float f14 = Mth.sin((f7 - 0.1F) * 1.3F);
                                    float f17 = f11 - 0.1F;
                                    float f19 = f14 * f17;
                                    p_109379_.translate(f19 * 0.0F, f19 * 0.004F, f19 * 0.0F);
                                }

                                p_109379_.translate(0.0F, 0.0F, f11 * 0.2F);
                                p_109379_.scale(1.0F, 1.0F, 1.0F + f11 * 0.2F);
                                p_109379_.mulPose(Axis.YN.rotationDegrees((float) k * 45.0F));
                                break;
                            case BRUSH:
                                applyBrushTransform(p_109379_, p_109373_, humanoidarm, p_109378_);
                        }
                    } else if (p_109372_.isAutoSpinAttack()) {
                        applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                        int j = flag3 ? 1 : -1;
                        p_109379_.translate((float) j * -0.4F, 0.8F, 0.3F);
                        p_109379_.mulPose(Axis.YP.rotationDegrees((float) j * 65.0F));
                        p_109379_.mulPose(Axis.ZP.rotationDegrees((float) j * -85.0F));
                    } else {
                        float f5 = -0.4F * Mth.sin(Mth.sqrt(p_109376_) * (float) Math.PI);
                        float f6 = 0.2F * Mth.sin(Mth.sqrt(p_109376_) * ((float) Math.PI * 2F));
                        float f10 = -0.2F * Mth.sin(p_109376_ * (float) Math.PI);
                        int l = flag3 ? 1 : -1;
                        p_109379_.translate((float) l * f5, f6, f10);
                        applyItemArmTransform(p_109379_, humanoidarm, p_109378_);
                        applyItemArmAttackTransform(p_109379_, humanoidarm, p_109376_);
                    }
//                }

                instance.renderItem(p_109372_, isNeedStack, flag3 ? ItemDisplayContext.FIRST_PERSON_RIGHT_HAND : ItemDisplayContext.FIRST_PERSON_LEFT_HAND, !flag3, p_109379_, p_109380_, p_109381_);
            }

            p_109379_.popPose();
        }
    }

    @Overwrite(methodName = {"renderItem", "m_269530_"}, desc = "(Lnet/minecraft/world/entity/LivingEntity;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/item/ItemDisplayContext;ZLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderItem(ItemInHandRenderer instance, LivingEntity p_109323_, ItemStack p_109324_, ItemDisplayContext p_109325_, boolean p_109326_, PoseStack p_109327_, MultiBufferSource p_109328_, int p_109329_) {
        AutoTool autoTool = (AutoTool) Main.INSTANCE.moduleManager.getModule("AutoTool");
        Scaffold scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");

        if (p_109323_ == mc.player) {
            if (autoTool.isEnable() && autoTool.clicking) {
                if (!autoTool.getFakeCurrentItem().isEmpty()) {
                    if (p_109323_.getMainHandItem() == p_109324_) {
                        mc.getItemRenderer().renderStatic(p_109323_, autoTool.getFakeCurrentItem(), p_109325_, p_109326_, p_109327_, p_109328_, p_109323_.level(), p_109329_, OverlayTexture.NO_OVERLAY, p_109323_.getId() + p_109325_.ordinal());
                    } else {
                        mc.getItemRenderer().renderStatic(p_109323_, p_109324_, p_109325_, p_109326_, p_109327_, p_109328_, p_109323_.level(), p_109329_, OverlayTexture.NO_OVERLAY, p_109323_.getId() + p_109325_.ordinal());
                    }
                }
            } else if (scaffold.isEnable()) {
                if (!scaffold.getFakeCurrentItem().isEmpty()) {
                    if (p_109323_.getMainHandItem() == p_109324_) {
                        mc.getItemRenderer().renderStatic(p_109323_, scaffold.getFakeCurrentItem(), p_109325_, p_109326_, p_109327_, p_109328_, p_109323_.level(), p_109329_, OverlayTexture.NO_OVERLAY, p_109323_.getId() + p_109325_.ordinal());
                    } else {
                        mc.getItemRenderer().renderStatic(p_109323_, p_109324_, p_109325_, p_109326_, p_109327_, p_109328_, p_109323_.level(), p_109329_, OverlayTexture.NO_OVERLAY, p_109323_.getId() + p_109325_.ordinal());
                    }
                }
            } else {
                if (!p_109324_.isEmpty())
                    mc.getItemRenderer().renderStatic(p_109323_, p_109324_, p_109325_, p_109326_, p_109327_, p_109328_, p_109323_.level(), p_109329_, OverlayTexture.NO_OVERLAY, p_109323_.getId() + p_109325_.ordinal());
            }
        } else {
            if (!p_109324_.isEmpty())
                mc.getItemRenderer().renderStatic(p_109323_, p_109324_, p_109325_, p_109326_, p_109327_, p_109328_, p_109323_.level(), p_109329_, OverlayTexture.NO_OVERLAY, p_109323_.getId() + p_109325_.ordinal());
        }
    }

    @Overwrite(methodName = {"renderHandsWithItems", "m_109314_"}, desc = "(FLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource$BufferSource;Lnet/minecraft/client/player/LocalPlayer;I)V")
    public static void renderHandsWithItems(ItemInHandRenderer instance, float p_109315_, PoseStack p_109316_, MultiBufferSource.BufferSource p_109317_, LocalPlayer p_109318_, int p_109319_) {
        float f = p_109318_.getAttackAnim(p_109315_);
        InteractionHand interactionhand = MoreObjects.firstNonNull(p_109318_.swingingArm, InteractionHand.MAIN_HAND);
        float f1 = Mth.lerp(p_109315_, p_109318_.xRotO, p_109318_.getXRot());

        ItemInHandRendererTransformer.HandRenderSelection iteminhandrenderer$handrenderselection = evaluateWhichHandsToRender(p_109318_);
        float f2 = Mth.lerp(p_109315_, p_109318_.xBobO, p_109318_.xBob);
        float f3 = Mth.lerp(p_109315_, p_109318_.yBobO, p_109318_.yBob);
        p_109316_.mulPose(Axis.XP.rotationDegrees((p_109318_.getViewXRot(p_109315_) - f2) * 0.1F));
        p_109316_.mulPose(Axis.YP.rotationDegrees((p_109318_.getViewYRot(p_109315_) - f3) * 0.1F));
        Method renderArmWithItem = ObfuscationReflectionHelper.findMethod(ItemInHandRenderer.class, "renderArmWithItem",void.class, AbstractClientPlayer.class, float.class, float.class, InteractionHand.class, float.class, ItemStack.class, float.class, PoseStack.class, MultiBufferSource.class, int.class);
        if (iteminhandrenderer$handrenderselection.renderMainHand) {
            float f4 = interactionhand == InteractionHand.MAIN_HAND ? f : 0.0F;//swingProgress
            float oMainHandHeight = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "oMainHandHeight");//mainHandItem
            float mainHandHeight = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "mainHandHeight");//mainHandItem

            float f5 = 1.0F - Mth.lerp(p_109315_, oMainHandHeight, mainHandHeight);

            ItemStack obj = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "mainHandItem");//mainHandItem
            try {
                EventRenderHand eventRenderHand = new EventRenderHand(InteractionHand.MAIN_HAND, p_109316_, p_109317_, p_109319_, p_109315_, f1, f5, f4, obj);
                EventManager.call(eventRenderHand);

                if (!eventRenderHand.isCancelled())
                    renderArmWithItem.invoke(instance, p_109318_, p_109315_, f1, InteractionHand.MAIN_HAND, f4, obj, f5, p_109316_, p_109317_, p_109319_);
            } catch (Exception ignored) {
            }
        }

        if (iteminhandrenderer$handrenderselection.renderOffHand) {
            float oOffHandHeight = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "oOffHandHeight");//mainHandItem
            float offHandHeight = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "offHandHeight");//mainHandItem
            float f6 = interactionhand == InteractionHand.OFF_HAND ? f : 0.0F;
            float f7 = 1.0F - Mth.lerp(p_109315_, oOffHandHeight, offHandHeight);
            ItemStack obj = ObfuscationReflectionHelper.getPrivateValue(ItemInHandRenderer.class, instance, "offHandItem");//offHandItem
            try {
                EventRenderHand eventRenderHand = new EventRenderHand(InteractionHand.OFF_HAND, p_109316_, p_109317_, p_109319_, p_109315_, f1, f7, f6, obj);
                EventManager.call(eventRenderHand);
                if (!eventRenderHand.isCancelled())
                    renderArmWithItem.invoke(instance, p_109318_, p_109315_, f1, InteractionHand.OFF_HAND, f6, obj, f7, p_109316_, p_109317_, p_109319_);


            } catch (Exception ignored) {
            }
        }

        p_109317_.endBatch();
    }

    @VisibleForTesting
    static ItemInHandRendererTransformer.HandRenderSelection evaluateWhichHandsToRender(LocalPlayer p_172915_) {
        ItemStack itemstack = p_172915_.getMainHandItem();
        ItemStack itemstack1 = p_172915_.getOffhandItem();
        boolean flag = itemstack.is(Items.BOW) || itemstack1.is(Items.BOW);
        boolean flag1 = itemstack.is(Items.CROSSBOW) || itemstack1.is(Items.CROSSBOW);
        if (!flag && !flag1) {
            return ItemInHandRendererTransformer.HandRenderSelection.RENDER_BOTH_HANDS;
        } else if (p_172915_.isUsingItem()) {
            return selectionUsingItemWhileHoldingBowLike(p_172915_);
        } else {
            return isChargedCrossbow(itemstack) ? ItemInHandRendererTransformer.HandRenderSelection.RENDER_MAIN_HAND_ONLY : ItemInHandRendererTransformer.HandRenderSelection.RENDER_BOTH_HANDS;
        }
    }

    private static ItemInHandRendererTransformer.HandRenderSelection selectionUsingItemWhileHoldingBowLike(LocalPlayer p_172917_) {
        ItemStack itemstack = p_172917_.getUseItem();
        InteractionHand interactionhand = p_172917_.getUsedItemHand();
        if (!itemstack.is(Items.BOW) && !itemstack.is(Items.CROSSBOW)) {
            return interactionhand == InteractionHand.MAIN_HAND && isChargedCrossbow(p_172917_.getOffhandItem()) ? ItemInHandRendererTransformer.HandRenderSelection.RENDER_MAIN_HAND_ONLY : ItemInHandRendererTransformer.HandRenderSelection.RENDER_BOTH_HANDS;
        } else {
            return ItemInHandRendererTransformer.HandRenderSelection.onlyForHand(interactionhand);
        }
    }

    private static boolean isChargedCrossbow(ItemStack p_172913_) {
        return p_172913_.is(Items.CROSSBOW) && CrossbowItem.isCharged(p_172913_);
    }


    @VisibleForTesting
    enum HandRenderSelection {
        RENDER_BOTH_HANDS(true, true),
        RENDER_MAIN_HAND_ONLY(true, false),
        RENDER_OFF_HAND_ONLY(false, true);

        final boolean renderMainHand;
        final boolean renderOffHand;

        HandRenderSelection(boolean p_172928_, boolean p_172929_) {
            this.renderMainHand = p_172928_;
            this.renderOffHand = p_172929_;
        }

        public static HandRenderSelection onlyForHand(InteractionHand p_172932_) {
            return p_172932_ == InteractionHand.MAIN_HAND ? RENDER_MAIN_HAND_ONLY : RENDER_OFF_HAND_ONLY;
        }
    }

    private static void renderPlayerArm(ItemInHandRenderer instance, PoseStack p_109347_, MultiBufferSource p_109348_, int p_109349_, float p_109350_, float p_109351_, HumanoidArm p_109352_) {
        try {
            Method method = ObfuscationReflectionHelper.findMethod(ItemInHandRenderer.class, "renderPlayerArm",void.class, PoseStack.class, MultiBufferSource.class, int.class, float.class, float.class, HumanoidArm.class);
            method.setAccessible(true);
            method.invoke(instance, p_109347_, p_109348_, p_109349_, p_109350_, p_109351_, p_109352_);
        } catch (IllegalAccessException | InvocationTargetException ignored) {
        }
    }

    private static void renderTwoHandedMap(ItemInHandRenderer instance, PoseStack p_109340_, MultiBufferSource p_109341_, int p_109342_, float p_109343_, float p_109344_, float p_109345_) {
        try {
            Method method = ObfuscationReflectionHelper.findMethod(ItemInHandRenderer.class, "renderTwoHandedMap",void.class, PoseStack.class, MultiBufferSource.class, int.class, float.class, float.class, float.class);
            method.setAccessible(true);
            method.invoke(instance, p_109340_, p_109341_, p_109342_, p_109343_, p_109344_, p_109345_);
        } catch (IllegalAccessException | InvocationTargetException ignored) {
        }
    }

    private static void renderOneHandedMap(ItemInHandRenderer instance, PoseStack p_109354_, MultiBufferSource p_109355_, int p_109356_, float p_109357_, HumanoidArm p_109358_, float p_109359_, ItemStack p_109360_) {
        try {
            Method method = ObfuscationReflectionHelper.findMethod(ItemInHandRenderer.class, "renderOneHandedMap",void.class, PoseStack.class, MultiBufferSource.class, int.class, float.class, HumanoidArm.class, float.class, ItemStack.class);
            method.setAccessible(true);
            method.invoke(instance, p_109354_, p_109355_, p_109356_, p_109357_, p_109358_, p_109359_, p_109360_);
        } catch (IllegalAccessException | InvocationTargetException ignored) {
        }
    }

    private static void applyItemArmTransform(PoseStack p_109383_, HumanoidArm p_109384_, float p_109385_) {
        int i = p_109384_ == HumanoidArm.RIGHT ? 1 : -1;
        p_109383_.translate((float) i * 0.56F, -0.52F + p_109385_ * -0.6F, -0.72F);
    }

    private static void applyItemArmAttackTransform(PoseStack p_109336_, HumanoidArm p_109337_, float p_109338_) {
        int i = p_109337_ == HumanoidArm.RIGHT ? 1 : -1;
        float f = Mth.sin(p_109338_ * p_109338_ * (float) Math.PI);
        p_109336_.mulPose(Axis.YP.rotationDegrees((float) i * (45.0F + f * -20.0F)));
        float f1 = Mth.sin(Mth.sqrt(p_109338_) * (float) Math.PI);
        p_109336_.mulPose(Axis.ZP.rotationDegrees((float) i * f1 * -20.0F));
        p_109336_.mulPose(Axis.XP.rotationDegrees(f1 * -80.0F));
        p_109336_.mulPose(Axis.YP.rotationDegrees((float) i * -45.0F));
    }

    private static void applyBrushTransform(PoseStack p_273513_, float p_273245_, HumanoidArm p_273726_, float p_273333_) {
        applyItemArmTransform(p_273513_, p_273726_, p_273333_);
        float f = (float) (mc.player.getUseItemRemainingTicks() % 10);
        float f1 = f - p_273245_ + 1.0F;
        float f2 = 1.0F - f1 / 10.0F;
        float f7 = -15.0F + 75.0F * Mth.cos(f2 * 2.0F * (float) Math.PI);
        if (p_273726_ != HumanoidArm.RIGHT) {
            p_273513_.translate(0.1, 0.83, 0.35);
            p_273513_.mulPose(Axis.XP.rotationDegrees(-80.0F));
            p_273513_.mulPose(Axis.YP.rotationDegrees(-90.0F));
            p_273513_.mulPose(Axis.XP.rotationDegrees(f7));
            p_273513_.translate(-0.3, 0.22, 0.35);
        } else {
            p_273513_.translate(-0.25F, 0.22, 0.35);
            p_273513_.mulPose(Axis.XP.rotationDegrees(-80.0F));
            p_273513_.mulPose(Axis.YP.rotationDegrees(90.0F));
            p_273513_.mulPose(Axis.ZP.rotationDegrees(0.0F));
            p_273513_.mulPose(Axis.XP.rotationDegrees(f7));
        }
    }

    private static void applyEatTransform(PoseStack p_109331_, float p_109332_, HumanoidArm p_109333_, ItemStack p_109334_) {
        float f = (float) mc.player.getUseItemRemainingTicks() - p_109332_ + 1.0F;
        float f1 = f / (float) p_109334_.getUseDuration();
        if (f1 < 0.8F) {
            float f2 = Mth.abs(Mth.cos(f / 4.0F * (float) Math.PI) * 0.1F);
            p_109331_.translate(0.0F, f2, 0.0F);
        }

        float f3 = 1.0F - (float) Math.pow(f1, 27.0F);
        int i = p_109333_ == HumanoidArm.RIGHT ? 1 : -1;
        p_109331_.translate(f3 * 0.6F * (float) i, f3 * -0.5F, f3 * 0.0F);
        p_109331_.mulPose(Axis.YP.rotationDegrees((float) i * f3 * 90.0F));
        p_109331_.mulPose(Axis.XP.rotationDegrees(f3 * 10.0F));
        p_109331_.mulPose(Axis.ZP.rotationDegrees((float) i * f3 * 30.0F));
    }
}
