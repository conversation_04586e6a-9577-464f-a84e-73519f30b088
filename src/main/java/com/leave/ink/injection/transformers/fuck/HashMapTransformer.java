package com.leave.ink.injection.transformers.fuck;

import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;

import java.util.HashMap;

@ClassTransformer(HashMap.class)
public class HashMapTransformer extends Transformer {
    @ASM(methodName = "put", desc = "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;")
    public void put(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        LabelNode skipLabel = new LabelNode();
        LabelNode returnLabel = new LabelNode();

        String[] forbiddenStrings = {
                "com.leave",
                "YeQing",
                "Leave",
                "com.example",
                "com.darkmagician6",
                "com.external",
                "io.github.humbleui",
                "\\bin\\version.dll",
                "skija.dll",
                "libcrypto-3-x64.dll",
                "Forge.dll",
                "Core.dll",
                "net.minecraftforge.eventbus",
                "java.lang.ProcessBuilder",
                "java.io.BufferedReader",
                "java.io.Reader"
        };

        // if (!(arg instanceof String)) goto skip;
        list.add(new VarInsnNode(ALOAD, 2));
        list.add(new TypeInsnNode(INSTANCEOF, "java/lang/String"));
        list.add(new JumpInsnNode(IFEQ, skipLabel));

        // String str = (String) arg;
        list.add(new VarInsnNode(ALOAD, 2));
        list.add(new TypeInsnNode(CHECKCAST, "java/lang/String"));
        list.add(new VarInsnNode(ASTORE, 3)); // 存入本地变量 2

        // 遍历关键字，判断 contains，任意一个匹配跳转到 returnLabel
        for (String keyword : forbiddenStrings) {
            list.add(new VarInsnNode(ALOAD, 3)); // 加载字符串
            list.add(new LdcInsnNode(keyword));  // 加载关键字
            list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/lang/String", "contains", "(Ljava/lang/CharSequence;)Z", false));
            list.add(new JumpInsnNode(IFNE, returnLabel)); // 匹配 → 跳转
        }

        list.add(new JumpInsnNode(GOTO, skipLabel)); // 不匹配 → 正常执行

        // returnLabel: 打印 + return true
        list.add(returnLabel);

        list.add(new InsnNode(ACONST_NULL));
        list.add(new InsnNode(ARETURN));

        // skip:
        list.add(skipLabel);

        // 插入
        methodNode.instructions.insert(list);
    }

}
