package com.leave.ink.injection.transformers.player;

import com.leave.ink.Main;
import com.leave.ink.features.module.modules.world.NoSlowBreak;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import net.minecraft.core.BlockPos;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.effect.MobEffectUtil;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraftforge.event.ForgeEventFactory;
import javax.annotation.Nullable;

@ClassTransformer(Player.class)
public class PlayerTransformer extends Transformer {
    @Overwrite(methodName = {"getDigSpeed"}, desc = "(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/core/BlockPos;)F")
    public static float getDigSpeed(Player instance, BlockState p_36282_, @Nullable BlockPos pos) {
        var noSlowBreak = (NoSlowBreak) Main.INSTANCE.moduleManager.getModule("NoSlowBreak");

        float f = instance.getInventory().getDestroySpeed(p_36282_);
        if (f > 1.0F) {
            int i = EnchantmentHelper.getBlockEfficiency(instance);
            ItemStack itemstack = instance.getMainHandItem();
            if (i > 0 && !itemstack.isEmpty()) {
                f += (float) (i * i + 1);
            }
        }

        if (MobEffectUtil.hasDigSpeed(instance)) {
            f *= 1.0F + (float) (MobEffectUtil.getDigSpeedAmplification(instance) + 1) * 0.2F;
        }

        if (instance.hasEffect(MobEffects.DIG_SLOWDOWN)) {
            float f1 = switch (instance.getEffect(MobEffects.DIG_SLOWDOWN).getAmplifier()) {
                case 0 -> 0.3F;
                case 1 -> 0.09F;
                case 2 -> 0.0027F;
                default -> 8.1E-4F;
            };

            f *= f1;
        }

        if (instance.isEyeInFluid(FluidTags.WATER) && !EnchantmentHelper.hasAquaAffinity(instance)) {
            f /= noSlowBreak.isEnable() && noSlowBreak.water.getValue() ? 1.0F : 5.0F;
        }

        if (!instance.onGround()) {
            f /= noSlowBreak.isEnable() && noSlowBreak.air.getValue() ? 1.0F : 5.0F;
        }

        f = ForgeEventFactory.getBreakSpeed(instance, p_36282_, f, pos);
        return f;
    }
}
