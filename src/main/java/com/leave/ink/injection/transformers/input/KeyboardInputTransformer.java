package com.leave.ink.injection.transformers.input;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.EventMoveInput;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import net.minecraft.client.Options;
import net.minecraft.client.gui.screens.ChatScreen;
import net.minecraft.client.player.KeyboardInput;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import org.lwjgl.glfw.GLFW;

@ClassTransformer(KeyboardInput.class)
public class KeyboardInputTransformer extends Transformer {
    @Overwrite(methodName = {"tick", "m_214106_"}, desc = "(ZF)V")
    public static void tick(KeyboardInput instance, boolean p_234118_, float p_234119_) {
        Options options = ObfuscationReflectionHelper.getPrivateValue(KeyboardInput.class, instance, "options");
        if (Main.INSTANCE.moduleManager.getModule("InvMove").isEnable() && (mc.screen != null && !(mc.screen instanceof ChatScreen))) {
            instance.up = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyUp.getKey().getValue()) == GLFW.GLFW_PRESS;
            instance.down = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyDown.getKey().getValue()) == GLFW.GLFW_PRESS;
            instance.left = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyLeft.getKey().getValue()) == GLFW.GLFW_PRESS;
            instance.right = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyRight.getKey().getValue()) == GLFW.GLFW_PRESS;
            instance.jumping = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyJump.getKey().getValue()) == GLFW.GLFW_PRESS;
            instance.shiftKeyDown = GLFW.glfwGetKey(mc.getWindow().getWindow(), mc.options.keyShift.getKey().getValue()) == GLFW.GLFW_PRESS;
        } else {
            instance.up = options.keyUp.isDown();
            instance.down = options.keyDown.isDown();
            instance.left = options.keyLeft.isDown();
            instance.right = options.keyRight.isDown();
            instance.jumping = options.keyJump.isDown();
            instance.shiftKeyDown = options.keyShift.isDown();
        }

        instance.forwardImpulse = instance.up == instance.down ? 0.0F : (instance.up ? 1.0F : -1.0F);
        instance.leftImpulse = instance.left == instance.right ? 0.0F : (instance.left ? 1.0F : -1.0F);
        EventMoveInput moveInputEvent = new EventMoveInput(instance.forwardImpulse, instance.leftImpulse, 0.3D, instance);
        EventManager.call(moveInputEvent);

        instance.forwardImpulse = moveInputEvent.getForward();
        instance.leftImpulse = moveInputEvent.getStrafe();

        if (p_234118_) {
            instance.leftImpulse = (float) ((double) instance.leftImpulse * moveInputEvent.getSneakSlowDownMultiplier());
            instance.forwardImpulse = (float) ((double) instance.forwardImpulse * moveInputEvent.getSneakSlowDownMultiplier());
        }
    }
}
