package com.leave.ink.injection.transformers.fuck;

import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;

import java.io.Reader;
import java.nio.CharBuffer;

@ClassTransformer(Reader.class)
public class ReaderTransformer extends Transformer {

    @ASM(methodName = "read", desc = "(Ljava/nio/<PERSON>r<PERSON><PERSON>er;)I")
    public void read(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        InsnList instructions = methodNode.instructions;

        // Find the two locations where we need to insert our filtering code
        AbstractInsnNode[] targets = findTargetLocations(instructions);
        if (targets[0] != null && targets[1] != null) {
            // Insert our filtering code at both locations
            insertFilterCode(methodNode, instructions, targets[0], true);  // For hasArray branch
            insertFilterCode(methodNode, instructions, targets[1], false); // For !hasArray branch
        }
    }
    private AbstractInsnNode[] findTargetLocations(InsnList instructions) {
        AbstractInsnNode[] targets = new AbstractInsnNode[2];
        AbstractInsnNode current = instructions.getFirst();

        // Position tracking
        boolean inHasArrayBlock = false;
        boolean inElseBlock = false;

        while (current != null) {
            // Looking for the pattern that matches "target.position(pos + nread);"
            if (current.getOpcode() ==INVOKEVIRTUAL &&
                    ((MethodInsnNode)current).name.equals("position") &&
                    inHasArrayBlock) {
                targets[0] = current;
            }

            // Looking for the pattern that matches "target.put(cbuf, 0, nread);"
            if (current.getOpcode() == INVOKEVIRTUAL &&
                    ((MethodInsnNode)current).name.equals("put") &&
                    inElseBlock) {
                targets[1] = current;
            }

            // Track which block we're in
            if (current.getOpcode() == INVOKEVIRTUAL &&
                    ((MethodInsnNode)current).name.equals("hasArray")) {
                inHasArrayBlock = true;
            } else if (current.getOpcode() == GOTO && inHasArrayBlock) {
                inHasArrayBlock = false;
                inElseBlock = true;
            }

            current = current.getNext();
        }

        return targets;
    }
    private void insertFilterCode(MethodNode methodNode, InsnList instructions, AbstractInsnNode target, boolean isHasArray) {
        InsnList toInsert = new InsnList();

        // No need to check if nread > 0 again, we're already inside that block

        // Now add the filter code directly
        toInsert.add(new VarInsnNode(ALOAD, 1)); // Load target

        if (isHasArray) {
            // For hasArray branch: filterBuffer(target, pos, pos + nread)
            toInsert.add(new VarInsnNode(ILOAD, 4)); // Load pos
            System.out.println("isHasArrayisHasArrayisHasArray");
            toInsert.add(new VarInsnNode(ILOAD, 4)); // Load pos again
            toInsert.add(new VarInsnNode(ILOAD, 2)); // Load nread
            toInsert.add(new InsnNode(IADD)); // Add pos + read
        } else {
            // For !hasArray branch: filterBuffer(target, 0, nread)
            System.out.println("filterBufferfilterBufferfilterBuffer");
            toInsert.add(new InsnNode(ICONST_0)); // Push 0
            toInsert.add(new VarInsnNode(ILOAD, 2)); // Load nread
        }

        // Call the filterBuffer method
        toInsert.add(new MethodInsnNode(
                INVOKESTATIC,
                "com/leave/ink/natives/TestClass",
                "filterBuffer",
                "(Ljava/nio/CharBuffer;II)I",
                false
        ));



//        toInsert.add(insnList);

        // Store the result back to nread
        toInsert.add(new VarInsnNode(ISTORE, 2));

        // Insert our instructions right after the target instruction
        instructions.insert(target, toInsert);
    }
    public static int filterBuffer(CharBuffer target, int start, int end) {
        String[] forbiddenStrings = {
                "com.leave",
                "YeQing",
                "com.example",
                "com.darkmagician6",
                "com.external",
                "net.minecraftforge.eventbus",
                "java.lang.ProcessBuilder",
                "java.io.Reader"
        };

        String content = target.toString();
        System.out.println("*************************************");
        for (String forbidden : forbiddenStrings) {
            int index = content.indexOf(forbidden);
            if (index != -1) {
                // 从缓冲区中移除该字符串
                target.position(start);
                target.limit(start + index);
                target.compact();
                // 调整读取的字符数
                return index;
            }
        }

        return end - start;
    }

}
