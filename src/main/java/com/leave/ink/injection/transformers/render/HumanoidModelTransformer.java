package com.leave.ink.injection.transformers.render;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.PitchRenderEvent;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.world.entity.LivingEntity;
import org.objectweb.asm.Opcodes;
import org.objectweb.asm.Type;

@ClassTransformer(HumanoidModel.class)
public class HumanoidModelTransformer extends Transformer {
    public static PitchRenderEvent onPitchRender(LivingEntity entity, float pitch) {
        return Main.INSTANCE != null && entity == mc.player && mc.level != null
                ? (PitchRenderEvent) EventManager.call(new PitchRenderEvent(pitch))
                : new PitchRenderEvent(pitch);
    }

    @ASM(methodName = {"setupAnim", "m_6973_"}, desc = "(Lnet/minecraft/world/entity/LivingEntity;FFFFF)V")
    public void setupAnim(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        int j = 7;

        list.add(new VarInsnNode(Opcodes.ALOAD, 1));
        list.add(new VarInsnNode(Opcodes.FLOAD, 6));
        list.add(new MethodInsnNode(Opcodes.INVOKESTATIC, Type.getInternalName(HumanoidModelTransformer.class), "onPitchRender", "(Lnet/minecraft/world/entity/LivingEntity;F)L" + PitchRenderEvent.class.getName().replace(".", "/") + ";",false));
        list.add(new VarInsnNode(Opcodes.ASTORE, 7));

        for (int i = 0; i < methodNode.instructions.size(); ++i) {
            AbstractInsnNode node = methodNode.instructions.get(i);
            if (node instanceof VarInsnNode && ((VarInsnNode) node).var >= j) {
                ((VarInsnNode) node).var += j;
            }

            if (node instanceof VarInsnNode && ((VarInsnNode) node).var == 6) {
                InsnList insnNode = new InsnList();
                insnNode.add(new VarInsnNode(Opcodes.ALOAD, 7));
                insnNode.add(new MethodInsnNode(Opcodes.INVOKEVIRTUAL, PitchRenderEvent.class.getName().replace(".", "/"), "getPitch", "()F"));

                methodNode.instructions.insert(node, insnNode);
                methodNode.instructions.remove(node);
            }
        }

        methodNode.instructions.insert(list);
    }
}