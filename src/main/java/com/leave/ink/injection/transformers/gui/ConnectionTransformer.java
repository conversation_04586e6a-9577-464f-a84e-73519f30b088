package com.leave.ink.injection.transformers.gui;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventHandleReceivePacket;
import com.leave.ink.events.EventPacket;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.utils.network.PacketUtil;
import com.leave.ink.utils.manager.BlinkManager;
import com.leave.ink.utils.network.PacketUtils;
import net.minecraft.network.Connection;
import net.minecraft.network.protocol.Packet;

@ClassTransformer(Connection.class)
public class ConnectionTransformer extends Transformer {
    public static boolean onPacketReceive(Packet<?> packet) {
        if (mc.level == null || mc.player == null || packet == null) return false;

        EventPacket event = new EventPacket(packet, EventPacket.PacketType.Server);

        if (!PacketUtils.handleSendPacket(event.getPacket())) {
            EventManager.call(event);
        }

        return event.isCancelled();
    }

    public static boolean onPacketSend(Packet<?> packet) {
        if (mc.level == null || mc.player == null || packet == null) return false;
        if (PacketUtil.handleSendPacket(packet)) {
            return false;
        }

        if (BlinkManager.INSTANCE.blinking && !BlinkManager.INSTANCE.pass) {
            if (BlinkManager.INSTANCE.add(packet)) {
                return true;
            }
        }

        EventPacket event = new EventPacket(packet, EventPacket.PacketType.Client);

        if (!PacketUtils.handleSendPacket(event.getPacket())) {
            EventManager.call(event);
        }

        return event.isCancelled();
    }

    public static boolean onPacketHandle(Packet<?> packet) {
        if (mc.level == null || mc.player == null || packet == null) return false;

        EventHandleReceivePacket event = new EventHandleReceivePacket(packet);

        EventManager.call(event);

        return event.isCancelled();
    }

    @ASM(methodName = {"channelRead0"}, desc = "(Lio/netty/channel/ChannelHandlerContext;Lnet/minecraft/network/protocol/Packet;)V")
    public static void channelRead0(InsnList instructions, MethodNode node, CallBackInfo callBackInfo) {
        instructions.add(new VarInsnNode(Opcodes.ALOAD, 2));
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(ConnectionTransformer.class),
                "onPacketReceive",
                "(Lnet/minecraft/network/protocol/Packet;)Z",
                false
        ));

        LabelNode continueLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        instructions.add(new InsnNode(Opcodes.RETURN));
        instructions.add(continueLabel);

        node.instructions.insert(instructions);
    }

    @ASM(methodName = {"sendPacket", "m_129520_"}, desc = "(Lnet/minecraft/network/protocol/Packet;Lnet/minecraft/network/PacketSendListener;)V")
    public static void sendPacket(InsnList instructions, MethodNode node, CallBackInfo callBackInfo) {
        instructions.add(new VarInsnNode(Opcodes.ALOAD, 1));
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(ConnectionTransformer.class),
                "onPacketSend",
                "(Lnet/minecraft/network/protocol/Packet;)Z",
                false
        ));

        LabelNode continueLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        instructions.add(new InsnNode(Opcodes.RETURN));
        instructions.add(continueLabel);

        node.instructions.insert(instructions);
    }

    @ASM(methodName = {"genericsFtw", "m_129517_"}, desc = "(Lnet/minecraft/network/protocol/Packet;Lnet/minecraft/network/PacketListener;)V")
    public static void genericsFtw(InsnList instructions, MethodNode node, CallBackInfo callBackInfo) {
        instructions.add(new VarInsnNode(Opcodes.ALOAD, 0));
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(ConnectionTransformer.class),
                "onPacketHandle",
                "(Lnet/minecraft/network/protocol/Packet;)Z",
                false
        ));

        LabelNode continueLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        instructions.add(new InsnNode(Opcodes.RETURN));
        instructions.add(continueLabel);

        node.instructions.insert(instructions);
    }
}