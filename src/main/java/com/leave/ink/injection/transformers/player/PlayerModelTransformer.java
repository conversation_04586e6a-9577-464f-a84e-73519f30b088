package com.leave.ink.injection.transformers.player;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventAnimationSetup;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.utils.rotation.RotationUtils;
import net.minecraft.client.model.PlayerModel;

@ClassTransformer(PlayerModel.class)
public class PlayerModelTransformer extends Transformer {
    @ASM(methodName = {"setupAnim", "m_6973_"}, desc = "(Lnet/minecraft/world/entity/LivingEntity;FFFFF)V")
    public void setupAnim(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        AbstractInsnNode node = methodNode.instructions.get(methodNode.instructions.size() - 1);
        for (int i = 0; i < methodNode.instructions.size(); ++i) {
            AbstractInsnNode n = methodNode.instructions.get(i);
            if (n.getOpcode() == 177) {
                node = n;
            }
        }

        list.add(new VarInsnNode(25, 0));
        list.add(new VarInsnNode(25, 1));
        list.add(new MethodInsnNode(184, Type.getInternalName(PlayerModelTransformer.class), "onSetupAnim", "(Ljava/lang/Object;Ljava/lang/Object;)V"));
        methodNode.instructions.insertBefore(node, list);
    }

    public static void onSetupAnim(Object model, Object entity) {
        if (RotationUtils.rotations != null && entity == mc.player && model instanceof PlayerModel<?> playerModel) {
//            playerModel.head.xRot = (float) ((double) RotationUtils.serverRotation.getPitch() / 57.29577951308232);
            EventAnimationSetup eventAnimationSetup = new EventAnimationSetup(playerModel);
            EventManager.call(eventAnimationSetup);
        }
    }
}
