package com.leave.ink.injection.transformers.gui;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventTabOverlay;
import com.leave.ink.features.hud.dynamicIsland.impl.tab.TabOverlayDynamic;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.ui.skija.fbo.FrameBuffers;
import com.leave.ink.ui.skija.fbo.GameFrameBuffer;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraftforge.client.gui.overlay.ForgeGui;

import java.util.ListIterator;

@ClassTransformer(ForgeGui.class)
public class ForgeGuiTransformer extends Transformer {
    public static boolean setVisible(boolean v) {
        EventTabOverlay eventTabOverlay = new EventTabOverlay(v);
        EventManager.call(eventTabOverlay);
        return eventTabOverlay.visible;
    }

    //renderPlayerList
    @ASM(methodName = {"renderPlayerList"}, desc = "(IILnet/minecraft/client/gui/GuiGraphics;)V")
    public void render(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        //setVisible
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        int in = 0;
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if(instruction.getOpcode() == INVOKEVIRTUAL && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("setVisible") || methodInsnNode.name.equals("m_94556_")) {
                    iterator.previous();
                    ++in;
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "setVisible", "(Z)Z", false));
                    iterator.next();
                }
            }
        }
    }

    public static void pre(GuiGraphics guiGraphics) {
        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer("TabOverlay");
        guiGraphics.pose().pushPose();
        guiGraphics.pose().translate(-mc.getWindow().getGuiScaledWidth() / 2f + TabOverlayDynamic.listWidth / 2f,0,0);
        gameFrameBuffer.bind();
    }

    public static void post(GuiGraphics guiGraphics) {
        GameFrameBuffer gameFrameBuffer = FrameBuffers.getBuffer("TabOverlay");
        guiGraphics.pose().popPose();
        gameFrameBuffer.end();
    }

    @ASM(methodName = {"renderPlayerList"}, desc = "(IILnet/minecraft/client/gui/GuiGraphics;)V")
    public void render2(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if(instruction.getOpcode() == INVOKEVIRTUAL && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("render") || methodInsnNode.name.equals("m_280406_")) {
                    iterator.previous();
                    iterator.add(new VarInsnNode(ALOAD, 3));
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()),
                            "pre", "(Lnet/minecraft/client/gui/GuiGraphics;)V", false));
                    iterator.next();
                    iterator.add(new VarInsnNode(ALOAD, 3));
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()),
                            "post", "(Lnet/minecraft/client/gui/GuiGraphics;)V",false));
                }
            }
        }
    }
}
