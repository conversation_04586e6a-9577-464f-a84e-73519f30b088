package com.leave.ink.injection.transformers.entity;

import com.leave.ink.Main;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.utils.Utils;
import net.minecraft.client.renderer.entity.EntityRenderer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import org.lwjgl.opengl.GL20;

@ClassTransformer(EntityRenderer.class)
public class EntityRendererTransformer extends Transformer {
    @PushArgs(index = 1, opcode = ALOAD)
    @Inject(at = InsertPosition.HEAD, methodName = {"render", "m_114384_"}, desc = "(Lnet/minecraft/world/entity/Entity;DDDFFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderPre(Entity entity) {
        if (!(entity instanceof LivingEntity)) return;
        if (Utils.isValidEntity(((LivingEntity) entity))) {
            if (Main.INSTANCE.moduleManager.getModule("Chams").isEnable()) {
                GL20.glEnable(GL20.GL_POLYGON_OFFSET_FILL);
                GL20.glPolygonOffset(1.0F, -1000000F);
            }
        }
    }

    @PushArgs(index = 1, opcode = ALOAD)
    @Inject(at = InsertPosition.LAST, methodName = {"render", "m_114384_"}, desc = "(Lnet/minecraft/world/entity/Entity;DDDFFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderPost(Entity entity) {
        if (!(entity instanceof LivingEntity)) return;
        if (Utils.isValidEntity(((LivingEntity) entity))) {
            if (Main.INSTANCE.moduleManager.getModule("Chams").isEnable()) {
                GL20.glPolygonOffset(1.0F, 1000000F);
                GL20.glDisable(GL20.GL_POLYGON_OFFSET_FILL);
            }
        }
    }
}
