package com.leave.ink.injection.transformers.client;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.events.EventChat;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.CallbackInfo;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import net.minecraft.client.multiplayer.ClientPacketListener;

@ClassTransformer(ClientPacketListener.class)
public class ClientPacketListenerTransformer extends Transformer {
    @Inject(
            callback = @CallbackInfo(callback = true),
            methodName = {"sendChat", "m_246175_"},
            desc = "(Ljava/lang/String;)V",
            at = InsertPosition.HEAD
    )
    @PushArgs(index = {1}, opcode = {ALOAD})
    public static CallBackInfo sendChat(CallBackInfo callBackInfo, String message) {

        EventChat eventChat = new EventChat(message);
        EventManager.call(eventChat);
        callBackInfo.setBack(eventChat.isCancelled());
        return callBackInfo;
    }
}
