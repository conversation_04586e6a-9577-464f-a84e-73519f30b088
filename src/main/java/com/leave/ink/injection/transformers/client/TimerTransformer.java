package com.leave.ink.injection.transformers.client;

import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import com.leave.ink.utils.timer.MinecraftTimer;
import net.minecraft.client.Timer;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;

@ClassTransformer(Timer.class)
public class TimerTransformer extends Transformer {
    @Overwrite(methodName = {"advanceTime", "m_92525_"}, desc = "(J)I")
    public static int advanceTime(Timer instance, long p_92526_) {
        instance.tickDelta = (float) (p_92526_ - (long) ObfuscationReflectionHelper.getPrivateValue(Timer.class, instance, "lastMs")) / (float) ObfuscationReflectionHelper.getPrivateValue(Timer.class, instance, "msPerTick") * MinecraftTimer.getTimerSpeed();
        ObfuscationReflectionHelper.setPrivateValue(Timer.class, instance, p_92526_, "lastMs");
        instance.partialTick += instance.tickDelta;
        int i = (int) instance.partialTick;
        instance.partialTick -= (float) i;
        return i;
    }
}
