package com.leave.ink.injection.transformers.player;

import com.leave.ink.features.hud.dynamicIsland.impl.tab.TabOverlayDynamic;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.InsertPosition;
import net.minecraft.client.gui.components.PlayerTabOverlay;

@ClassTransformer(PlayerTabOverlay.class)
public class PlayerTabOverlayTransformer extends Transformer {

    @PushArgs(index = {16, 15}, opcode = {ILOAD, ILOAD})
    @Inject(methodName = {"render", "m_280406_"}, desc = "(Lnet/minecraft/client/gui/GuiGraphics;ILnet/minecraft/world/scores/Scoreboard;Lnet/minecraft/world/scores/Objective;)V", at = InsertPosition.LAST)
    public static void a(int l1,int j3) {
        TabOverlayDynamic.listHeight = j3;
        TabOverlayDynamic.listWidth = l1;
    }
}
