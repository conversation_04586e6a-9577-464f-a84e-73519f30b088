package com.leave.ink.injection.transformers.client;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.features.module.modules.combat.Velocity;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventSyncHandleReceivePacket;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.utils.client.mapping.Mapping;
import net.minecraft.network.PacketListener;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.PacketUtils;
import net.minecraft.network.protocol.game.ClientGamePacketListener;
import net.minecraft.network.protocol.game.ClientboundLoginPacket;
import net.minecraft.network.protocol.game.ClientboundPingPacket;
import net.minecraft.server.RunningOnDifferentThreadException;
import net.minecraft.util.thread.BlockableEventLoop;
import org.slf4j.Logger;

@ClassTransformer(PacketUtils.class)
public class PacketUtilsTransformer extends Transformer {
    public static <T extends PacketListener> void ensureRunningOnSameThread(Logger LOGGER, Packet<T> packet, T listener, BlockableEventLoop<?> executor) throws RunningOnDifferentThreadException {
        if (!executor.isSameThread()) {
            executor.executeIfPossible(() -> {
                if (listener.isAcceptingMessages()) {
                    try {
                        EventSyncHandleReceivePacket event = new EventSyncHandleReceivePacket((Packet<ClientGamePacketListener>) packet);
                        if (Main.INSTANCE != null) {
                            var velocity = (Velocity) Main.INSTANCE.moduleManager.getModule("Velocity");
                            if (packet instanceof ClientboundLoginPacket) {
                                if (velocity != null && velocity.isEnable()) {
                                    velocity.inGameTick = 0;
                                }
                            }

                            if (packet instanceof ClientboundPingPacket) {
                                if (velocity != null && velocity.isEnable() && velocity.inGameTick < 60) {
                                    velocity.inGameTick++;
                                }
                            }

                            EventManager.call(event);
                        }

                        if (event.isCancelled()) {
                            return;
                        }

                        packet.handle(listener);
                    } catch (Exception exception) {
                        if (listener.shouldPropagateHandlingExceptions()) {
                            throw exception;
                        }

                        LOGGER.error("Failed to handle packet {}, suppressing error", packet, exception);
                    }
                } else {
                    LOGGER.debug("Ignoring packet due to disconnection: {}", packet);
                }

            });
            throw RunningOnDifferentThreadException.RUNNING_ON_DIFFERENT_THREAD;
        }
    }

    @ASM(methodName = {"ensureRunningOnSameThread", "m_131363_"}, desc = "(Lnet/minecraft/network/protocol/Packet;Lnet/minecraft/network/PacketListener;Lnet/minecraft/util/thread/BlockableEventLoop;)V")
    public static void ensureRunningOnSameThread(InsnList insnList, MethodNode methodNode, CallBackInfo callBackInfo) {
        // 清空原方法中的所有指令
        methodNode.instructions.clear();
        // 1. 获取静态字段 LOGGER
        insnList.add(new FieldInsnNode(GETSTATIC,
                "net/minecraft/network/protocol/PacketUtils",
                Mapping.get(PacketUtils.class, "LOGGER", null),
                "Lorg/slf4j/Logger;"));

        // 2. 加载方法参数 - Packet
        insnList.add(new VarInsnNode(ALOAD, 0));

        // 3. 加载方法参数 - PacketListener
        insnList.add(new VarInsnNode(ALOAD, 1));

        // 4. 加载方法参数 - BlockableEventLoop
        insnList.add(new VarInsnNode(ALOAD, 2));

        // 5. 调用我们的静态方法
        insnList.add(new MethodInsnNode(
                INVOKESTATIC,
                Type.getInternalName(PacketUtilsTransformer.class),
                "ensureRunningOnSameThread",
                "(Lorg/slf4j/Logger;Lnet/minecraft/network/protocol/Packet;Lnet/minecraft/network/PacketListener;Lnet/minecraft/util/thread/BlockableEventLoop;)V",
                false
        ));

        // 6. 返回
        insnList.add(new InsnNode(RETURN));

        // 添加指令列表到方法中
        methodNode.instructions.add(insnList);
    }
}