package com.leave.ink.injection.transformers.heypixel;

import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.injection.base.util.asm.tree.AbstractInsnNode;
import com.leave.ink.injection.base.util.asm.tree.InsnList;
import com.leave.ink.injection.base.util.asm.tree.MethodInsnNode;
import com.leave.ink.injection.base.util.asm.tree.MethodNode;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassNameTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;

import java.util.ListIterator;

@ClassNameTransformer("com.heypixel.heypixel.mixin.MixinGameRenderer")
public class MixinGameRendererTransformer extends Transformer {
    public static double getFovModify() {
        return 1.15d;
    }
    @ASM(methodName = "m_109141_", desc = "(Lnet/minecraft/client/Camera;FZ)D")
    public void getFov(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
//            System.out.println();
            if (instruction.getOpcode() == Opcodes.GETFIELD && instruction instanceof MethodInsnNode methodInsnNode) {
                //func_175063_a
                if (methodInsnNode.owner.equals("com/heypixel/heypixel/mixin/MixinGameRenderer")
                        && (methodInsnNode.name.equals("f_109066_"))) {

                    iterator.remove();
                    iterator.previous();
                    iterator.remove();//ALOAD 0
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getFovModify", "()D"));
                    break;
                }
            }
        }
        //        list.add(new InsnNode(RETURN));
//        methodNode.instructions.insert(methodNode.instructions.getFirst(), list);
    }
}
