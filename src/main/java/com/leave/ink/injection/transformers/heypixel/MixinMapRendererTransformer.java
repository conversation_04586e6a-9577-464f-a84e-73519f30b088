package com.leave.ink.injection.transformers.heypixel;

import com.leave.ink.injection.base.util.asm.tree.InsnList;
import com.leave.ink.injection.base.util.asm.tree.InsnNode;
import com.leave.ink.injection.base.util.asm.tree.MethodNode;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassNameTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;

@ClassNameTransformer("net.raphimc.immediatelyfast.injection.mixins.map_atlas_generation.MixinMapRenderer")
public class MixinMapRendererTransformer extends Transformer {

    @ASM(methodName ="createMapAtlasTexture", desc = "(ILnet/minecraft/world/level/saveddata/maps/MapItemSavedData;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V")
    public void createMapAtlasTexture(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        list.add(new InsnNode(RETURN));
        methodNode.instructions.insert(methodNode.instructions.getFirst(), list);
    }
}
