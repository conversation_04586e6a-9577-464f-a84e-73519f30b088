package com.leave.ink.injection.transformers.entity;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventLook;
import com.leave.ink.events.EventStrafe;
import com.leave.ink.features.module.modules.other.ClayGuns;
import com.leave.ink.features.module.modules.render.ESP;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.Overwrite;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.rotation.RotationUtils;
import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.Util;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.phys.Vec3;

@ClassTransformer(Entity.class)
public class EntityTransformer extends Transformer {
    @Overwrite(methodName = {"setXRot", "m_146926_"}, desc = "(F)V")
    public static void setXRot(Entity instance, float p_146927_) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");

        if (clayGuns.isEnable()) {
            clayGuns.setXRot(instance, p_146927_);
        } else {
            if (!Float.isFinite(p_146927_)) {
                Util.logAndPauseIfInIde("Invalid entity rotation: " + p_146927_ + ", discarding.");
            } else {
                ObfuscationReflectionHelper.setPrivateValue(Entity.class, instance, p_146927_, "xRot");
            }
        }
    }

    @Overwrite(methodName = {"setYRot", "m_146922_"}, desc = "(F)V")
    public static void setYRot(Entity instance,float p_146923_) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");

        if (clayGuns.isEnable()) {
            clayGuns.setYRot(instance, p_146923_);
        } else {
            if (!Float.isFinite(p_146923_)) {
                Util.logAndPauseIfInIde("Invalid entity rotation: " + p_146923_ + ", discarding.");
            } else {
                ObfuscationReflectionHelper.setPrivateValue(Entity.class, instance, p_146923_, "yRot");
            }
        }
    }

    @Overwrite(methodName = {"getYRot", "m_146908_"}, desc = "()F")
    public static float getYRot(Entity instance) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");

        if (clayGuns.isEnable() && instance == mc.player) {
            if (clayGuns.aim.getValue() && clayGuns.rotation.getValue().equals("Silent")) {
                if (clayGuns.canAttackTarget() && clayGuns.clipRotation.getRotation() != null) {
                    if (clayGuns.isCalledByClayPackage()) {
                        return clayGuns.clipRotation.getRotation().getYaw();
                    }
                }
            }
        }

        return ObfuscationReflectionHelper.getPrivateValue(Entity.class, instance, "yRot");
    }

    @Overwrite(methodName = {"getXRot", "m_146909_"}, desc = "()F")
    public static float getXRot(Entity instance) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");

        if (clayGuns.isEnable() && instance == mc.player) {
            if (clayGuns.aim.getValue() && clayGuns.rotation.getValue().equals("Silent")) {
                if (clayGuns.canAttackTarget() && clayGuns.clipRotation.getRotation() != null) {
                    if (clayGuns.isCalledByClayPackage()) {
                        return clayGuns.clipRotation.getRotation().getPitch();
                    }
                }
            }
        }

        return ObfuscationReflectionHelper.getPrivateValue(Entity.class, instance, "xRot");
    }

    @Overwrite(methodName = {"moveRelative", "m_19920_"}, desc = "(FLnet/minecraft/world/phys/Vec3;)V")
    public static void moveRelative(Entity instance, float p_19921_, Vec3 p_19922_) {
        if (mc.player != null && instance.getId() == mc.player.getId()) {
            EventStrafe eventStrafe = new EventStrafe(p_19922_, p_19921_, instance.getYRot());
            EventManager.call(eventStrafe);

            Vec3 vec3 = getInputVector(eventStrafe.getVelocity(), eventStrafe.getSpeed(), eventStrafe.getYaw());

            if (eventStrafe.isCancelled())
                return;

            instance.setDeltaMovement(instance.getDeltaMovement().add(vec3));
        } else {
            instance.setDeltaMovement(instance.getDeltaMovement().add(getInputVector(p_19922_, p_19921_, instance.getYRot())));
        }
    }

    @ASM(methodName = {"isCurrentlyGlowing", "m_142038_"}, desc = "()Z")
    public void isCurrentlyGlowing(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        LabelNode l = new LabelNode();
        list.add(new VarInsnNode(ALOAD, 0));
        list.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(EntityTransformer.class), "onGlowing", "(Lnet/minecraft/world/entity/Entity;)Z"));
        list.add(new JumpInsnNode(IFEQ, l));
        list.add(new LdcInsnNode(true));
        list.add(new InsnNode(IRETURN));
        list.add(l);

        methodNode.instructions.insert(list);
    }
    @PushArgs(index = 0, opcode = ALOAD)
    @Inject(at = InsertPosition.HEAD, methodName = {"isCurrentlyGlowing", "m_142038_"}, desc = "()Z")
    public static void isCurrentlyGlowingPre(Entity entity) {
        try {
            if (entity != null && onGlowing(entity)) {
                int color = ESP.getESPColor();
                float a = (color >> 24 & 255) / 255.0F;
                float r = (color >> 16 & 255) / 255.0F;
                float g = (color >> 8 & 255) / 255.0F;
                float b = (color & 255) / 255.0F;
                RenderSystem.setShaderColor(r, g, b, a);
            }
        } catch (Exception e) {
        }
    }
    @PushArgs(index = 0, opcode = ALOAD)
    @Inject(at = InsertPosition.LAST, methodName = {"isCurrentlyGlowing", "m_142038_"}, desc = "()Z")
    public static void isCurrentlyGlowingPost(Entity entity) {
        try {
            if (entity != null && onGlowing(entity)) {
                RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            }
        } catch (Exception e) {
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
        }
    }
    //    @ASM(methodName = {"getViewVector", "m_20252_"}, desc = "(F)Lnet/minecraft/world/phys/Vec3;")
    public void getViewVector(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        for(int i = 0; i < methodNode.instructions.size(); ++i) {
            AbstractInsnNode node = methodNode.instructions.get(i);
            if (node instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("m_5675_") || methodInsnNode.name.equals("getViewYRot") && methodInsnNode.desc.equals("(F)F")) {
                    methodNode.instructions.insertBefore(node, new MethodInsnNode(184, Type.getInternalName(EntityTransformer.class), "yaw", "(Lnet/minecraft/world/entity/Entity;F)F"));
                    methodNode.instructions.remove(node);
                }

                if (methodInsnNode.name.equals("m_5686_") || methodInsnNode.name.equals("getViewXRot") && methodInsnNode.desc.equals("(F)F")) {
                    methodNode.instructions.insertBefore(node, new MethodInsnNode(184, Type.getInternalName(EntityTransformer.class), "pitch", "(Lnet/minecraft/world/entity/Entity;F)F"));
                    methodNode.instructions.remove(node);
                }
            }
        }
    }
    @ASM(methodName = {"getViewVector", "m_20252_"}, desc = "(F)Lnet/minecraft/world/phys/Vec3;")
    public void getViewVector2(InsnList insnList, MethodNode methodNode, CallBackInfo callBackInfo) {
        methodNode.instructions.clear();
        // 加载this引用
        insnList.add(new VarInsnNode(ALOAD, 0));

        // 调用我们的静态方法
        insnList.add(new MethodInsnNode(
                INVOKESTATIC,
                Type.getInternalName(getClass()),
                "getViewVector",
                "(Lnet/minecraft/world/entity/Entity;)Lnet/minecraft/world/phys/Vec3;",
                false
        ));

        // 返回结果
        insnList.add(new InsnNode(ARETURN));

        // 将新指令添加到方法中
        methodNode.instructions.add(insnList);


    }
    public static Vec3 getViewVector(Entity entity) {
        float yaw = entity.getYRot();
        float pitch = entity.getXRot();

        EventLook lookEvent = new EventLook(yaw, pitch);
        EventManager.call(lookEvent);

        yaw = lookEvent.getYaw();
        pitch = lookEvent.getPitch();

        return calculateViewVector(entity, pitch, yaw);
    }
    @ASM(methodName = {"m_20171_", "calculateViewVector"}, desc = "(FF)Lnet/minecraft/world/phys/Vec3;")
    public void calculateViewVector(InsnList insnList, MethodNode methodNode, CallBackInfo callBackInfo) {
        // 清空原方法指令
        methodNode.instructions.clear();
        // 加载this引用
        insnList.add(new VarInsnNode(ALOAD, 0));

        // 加载原有的float参数 p_getVectorForRotation_1_
        insnList.add(new VarInsnNode(FLOAD, 1));

        // 加载原有的float参数 p_getVectorForRotation_2_
        insnList.add(new VarInsnNode(FLOAD, 2));

        // 调用我们的静态方法
        insnList.add(new MethodInsnNode(
                INVOKESTATIC,
                Type.getInternalName(getClass()),
                "calculateViewVector",
                "(Lnet/minecraft/world/entity/Entity;FF)Lnet/minecraft/world/phys/Vec3;",
                false
        ));

        // 返回结果
        insnList.add(new InsnNode(ARETURN));

        // 将新指令添加到方法中
        methodNode.instructions.add(insnList);
    }
    public static Vec3 calculateViewVector(Entity entity, float p_getVectorForRotation_1_, float p_getVectorForRotation_2_) {
        if (p_getVectorForRotation_2_ == entity.getYRot() || p_getVectorForRotation_1_ == entity.getX()) {
            EventLook lookEvent = new EventLook(p_getVectorForRotation_2_, p_getVectorForRotation_1_);

            EventManager.call(lookEvent);

            p_getVectorForRotation_2_ = lookEvent.getYaw();
            p_getVectorForRotation_1_ = lookEvent.getPitch();
        }
        float f = Mth.cos(-p_getVectorForRotation_2_ * 0.017453292F - 3.1415927F);
        float f1 = Mth.sin(-p_getVectorForRotation_2_ * 0.017453292F - 3.1415927F);
        float f2 = -Mth.cos(-p_getVectorForRotation_1_ * 0.017453292F);
        float f3 = Mth.sin(-p_getVectorForRotation_1_ * 0.017453292F);
        return new Vec3(f1 * f2, f3, f * f2);
    }

    private static Vec3 getInputVector(Vec3 p_20016_, float p_20017_, float p_20018_) {
        double d0 = p_20016_.lengthSqr();
        if (d0 < 1.0E-7D) {
            return Vec3.ZERO;
        } else {
            Vec3 vec3 = (d0 > 1.0D ? p_20016_.normalize() : p_20016_).scale((double)p_20017_);
            float f = Mth.sin(p_20018_ * ((float)Math.PI / 180F));
            float f1 = Mth.cos(p_20018_ * ((float)Math.PI / 180F));
            return new Vec3(vec3.x * (double)f1 - vec3.z * (double)f, vec3.y, vec3.z * (double)f1 + vec3.x * (double)f);
        }
    }

    public static boolean onGlowing(Entity entity) {
        try {
            ESP esp = (ESP) Main.INSTANCE.moduleManager.getModule("ESP");
            return esp != null && entity instanceof LivingEntity livingEntity && esp.isEnable() && esp.modeValue.getValue().equals("Glow") && Utils.isValidEntity(livingEntity) && livingEntity != mc.player;
        } catch (Exception a) {
            return false;
        }
    }
    public static EventLook onLook(Entity this1, float yaw, float pitch) {

        if (mc == null || mc.player == null || mc.level == null) {
            return new EventLook(0, 0);
        }

        return this1 == mc.player ? (EventLook) EventManager.call(new EventLook(yaw, pitch)) : new EventLook(yaw, pitch);
    }
    public static float yaw(Entity entity, float f) {

        if (entity == mc.player && RotationUtils.active && RotationUtils.rotations != null) {
            return RotationUtils.rotations.getYaw();
        }
        if (entity != null) {
            return entity.getViewYRot(f);
        } else {
            return 0;
        }
    }

    public static float pitch(Entity entity, float f) {
        if (entity == mc.player  && RotationUtils.active && RotationUtils.rotations != null) {
            return RotationUtils.rotations.getPitch();
        }
        if (entity != null) {
            return entity.getViewXRot(f);
        } else {
            return 0;
        }
    }
}
