package com.leave.ink.injection.transformers.player;

import com.leave.ink.Main;
import com.leave.ink.features.module.modules.render.NameTags;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.CallbackInfo;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.utils.Utils;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.client.renderer.entity.player.PlayerRenderer;
import net.minecraft.world.effect.MobEffects;
import org.lwjgl.opengl.GL20;

@ClassTransformer(PlayerRenderer.class)
public class PlayerRendererTransformer extends Transformer {
    @PushArgs(index = 1, opcode = ALOAD)
    @Inject(at = InsertPosition.HEAD, methodName = {"render", "m_7392_"}, desc = "(Lnet/minecraft/client/player/AbstractClientPlayer;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderPre(AbstractClientPlayer entity) {
        if (Utils.isValidEntity(entity)) {
            if (Main.INSTANCE.moduleManager.getModule("Chams").isEnable()) {
                GL20.glEnable(GL20.GL_POLYGON_OFFSET_FILL);
                GL20.glPolygonOffset(1.0F, -1000000F);
            }
        }
    }

    @PushArgs(index = 1, opcode = ALOAD)
    @Inject(at = InsertPosition.LAST, methodName = {"render", "m_7392_"}, desc = "(Lnet/minecraft/client/player/AbstractClientPlayer;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderPost(AbstractClientPlayer entity) {
        if (Utils.isValidEntity(entity)) {
            if (Main.INSTANCE.moduleManager.getModule("Chams").isEnable()) {
                GL20.glPolygonOffset(1.0F, 1000000F);
                GL20.glDisable(GL20.GL_POLYGON_OFFSET_FILL);
            }
        }
    }

    @Inject(methodName = {"renderNameTag", "m_7649_"}, desc = "(Lnet/minecraft/client/player/AbstractClientPlayer;Lnet/minecraft/network/chat/Component;Lcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V", at = InsertPosition.HEAD, callback = @CallbackInfo(callback = true))
    public static CallBackInfo renderNameTag(CallBackInfo callBackInfo) {
        var nameTags = (NameTags) Main.INSTANCE.moduleManager.getModule("NameTags");
        if (nameTags.isEnable() && nameTags.isDisableVanilla()) {
            callBackInfo.setBack(true);
        } else {
            callBackInfo.setBack(false);
        }
        return callBackInfo;
    }
}