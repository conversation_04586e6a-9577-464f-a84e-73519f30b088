package com.leave.ink.injection.transformers.player;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.asm.tree.*;
import net.minecraft.client.player.LocalPlayer;

import java.util.ListIterator;

@ClassTransformer(LocalPlayer.class)
public class LocalPlayerTransformer extends Transformer {
    @Overwrite(methodName = {"hasEnoughImpulseToStartSprinting", "m_108733_"}, desc = "()Z")
    public static boolean hasEnoughImpulseToStartSprinting(LocalPlayer localPlayer) {
        return localPlayer.isUnderWater() ? localPlayer.input.hasForwardImpulse() : Main.INSTANCE.sprintManager.shouldSprint && (double) localPlayer.input.forwardImpulse >= 0.8D;
    }

    @ASM(methodName = {"sendPosition", "m_108640_"}, desc = "()V")
    public void inject(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        int stack = methodNode.maxLocals + 1;
        list.add(new VarInsnNode(ALOAD, 0));
        list.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "callEventMotionPre","(Lnet/minecraft/client/player/LocalPlayer;)Lcom/leave/ink/events/EventMotion;"));
        list.add(new VarInsnNode(ASTORE, stack));
        methodNode.instructions.insert(methodNode.instructions.getFirst(),list);
        list.clear();
        list.add(new VarInsnNode(ALOAD, 0));
        list.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "callEventMotionPost","(Lnet/minecraft/client/player/LocalPlayer;)V"));
        methodNode.instructions.insertBefore(methodNode.instructions.get(methodNode.instructions.size() - 2),list);
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();

            if(instruction.getOpcode() == INVOKEVIRTUAL) {
                MethodInsnNode methodInsnNode = (MethodInsnNode) instruction;
                if(!methodInsnNode.owner.equals(Type.getInternalName(LocalPlayer.class))) continue;
                if (methodInsnNode.name.equals("onGround") || methodInsnNode.name.equals("m_20096_")) {
                    iterator.remove();
                    iterator.previous();
                    iterator.remove();

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventMotion.class), "isOnGround","()Z"));
                }
                if (methodInsnNode.name.equals("getX") || methodInsnNode.name.equals("m_20185_")) {
                    iterator.remove();
                    iterator.previous();
                    iterator.remove();

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventMotion.class), "getX","()D"));
                }
                if (methodInsnNode.name.equals("getY") || methodInsnNode.name.equals("m_20186_")) {
                    iterator.remove();
                    iterator.previous();
                    iterator.remove();

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventMotion.class), "getY","()D"));
                }
                if (methodInsnNode.name.equals("getZ") || methodInsnNode.name.equals("m_20189_")) {
                    iterator.remove();
                    iterator.previous();
                    iterator.remove();

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventMotion.class), "getZ","()D"));
                }
                if (methodInsnNode.name.equals("getYRot") || methodInsnNode.name.equals("m_146908_")) {
                    iterator.remove();
                    iterator.previous();
                    iterator.remove();

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventMotion.class), "getYaw","()F"));
                }
                if (methodInsnNode.name.equals("getXRot") || methodInsnNode.name.equals("m_146909_")) {
                    iterator.remove();
                    iterator.previous();
                    iterator.remove();

                    iterator.add(new VarInsnNode(ALOAD, stack));
                    iterator.add(new MethodInsnNode(INVOKEVIRTUAL, Type.getInternalName(EventMotion.class), "getPitch","()F"));
                }
            }

        }
    }
    public static EventMotion eventMotion = null;
    public static EventMotion callEventMotionPre(LocalPlayer instance) {

        eventMotion = new EventMotion(instance.getX(), instance.getY(), instance.getZ(), instance.getYRot(), instance.getXRot(), instance.onGround(), EventType.PRE);

        EventManager.call(eventMotion);
        return eventMotion;
    }
    public static void callEventMotionPost(LocalPlayer instance) {

        eventMotion = new EventMotion(instance.getX(), instance.getY(), instance.getZ(), instance.getYRot(), instance.getXRot(), instance.onGround(), EventType.POST);

        EventManager.call(eventMotion);
    }


    //@InjectPoint(methodName = {"onInput"}, desc = "(Lnet/minecraft/client/player/Input;)V")
    @Inject(methodName = {"aiStep", "m_8107_"}, desc = "()V", at = InsertPosition.HEAD)
    public static void pre() {
        EventUpdate eventUpdate = new EventUpdate();
        EventManager.call(eventUpdate);
    }
    @InjectPoint(methodName = {"tick", "m_8119_"}, desc = "()V")
    @Inject(methodName = {"tick", "m_8119_"}, desc = "()V")
    public static void tick() {

        EventManager.call(new EventPlayerTick());
    }

    @ASM(methodName = {"aiStep", "m_8107_"}, desc = "()V")
    public void isSlow(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        list.add(new VarInsnNode(ALOAD, 0));
        list.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(LocalPlayerTransformer.class), "onSlow", "(Lnet/minecraft/client/player/LocalPlayer;)Lcom/leave/ink/events/EventSlow;", false));
        methodNode.maxLocals += 1;
        int var = methodNode.maxLocals;
        list.add(new VarInsnNode(ASTORE, var));

        methodNode.instructions.insert(list);
        int index = 0;
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if(instruction.getOpcode() == INVOKEVIRTUAL && instruction instanceof MethodInsnNode methodInsnNode) {
                if (methodInsnNode.name.equals("isUsingItem") || methodInsnNode.name.equals("m_6117_")) {
                    if(index == 0) {
                        iterator.remove();
                        iterator.add(new VarInsnNode(ALOAD, var));
                        iterator.add(
                                new MethodInsnNode(
                                        INVOKEVIRTUAL,
                                        Type.getInternalName(EventSlow.class),
                                        "isSlowDown", "()Z", false)
                        );
                        ++index;
                    }

                }
            }
            if(instruction.getOpcode() == LDC && instruction instanceof LdcInsnNode ldcNode) {
                if (ldcNode.cst instanceof Float && (float) ldcNode.cst == 0.2F) {
                    iterator.remove();
                    iterator.add(new VarInsnNode(ALOAD, var));
                    iterator.add(
                            new MethodInsnNode(
                                    INVOKEVIRTUAL,
                                    Type.getInternalName(EventSlow.class),
                                    "getAmount", "()F", false)
                    );
                }
            }
        }
    }

    public static EventSlow onSlow(LocalPlayer instance) {
        boolean usingItem = instance.isUsingItem();
        EventSlow event = new EventSlow(0.2F, usingItem);

        EventManager.call(event);
        return event;
    }
}
