package com.leave.ink.injection.transformers.client;

import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;

import net.minecraft.client.gui.components.ChatComponent;

@ClassTransformer(ChatComponent.class)
public class ChatComponentTransformer extends Transformer {
//    @Overwrite(methodName = {"render", "m_93780_"}, desc = "(Lcom/mojang/blaze3d/vertex/PoseStack;I)V")
//    public static void render(ChatComponent instance, PoseStack p_93781_, int p_93782_) {
//        if (mc.options.chatVisibility().get() != ChatVisiblity.HIDDEN) {
//            processPendingMessages(instance);
//            List<GuiMessage<FormattedCharSequence>> trimmedMessages = ObfuscationReflectionHelper.getPrivateValue(ChatComponent.class, instance, "f_93761_");
//            int $$2 = instance.getLinesPerPage();
//            int $$3 = trimmedMessages.size();
//            if ($$3 > 0) {
//                boolean $$4 = mc.screen instanceof ChatScreen;
//                float $$5 = (float) instance.getScale();
//                int $$6 = Mth.ceil((float) instance.getWidth() / $$5);
//                p_93781_.pushPose();
//                p_93781_.translate(4.0F, 8.0F, 0.0F);
//                p_93781_.scale($$5, $$5, 1.0F);
//                double $$7 = mc.options.chatOpacity().get() * (double) 0.9F + (double) 0.1F;
//                double $$8 = mc.options.textBackgroundOpacity().get();
//                double $$9 = (double) 9.0F * (mc.options.chatLineSpacing().get() + (double) 1.0F);
//                double $$10 = (double) -8.0F * (mc.options.chatLineSpacing().get() + (double) 1.0F) + (double) 4.0F * mc.options.chatLineSpacing;
//                int $$11 = 0;
//                int chatScrollbarPos = ObfuscationReflectionHelper.getPrivateValue(ChatComponent.class, instance, "f_93763_");
//                for (int $$12 = 0; $$12 + chatScrollbarPos < trimmedMessages.size() && $$12 < $$2; ++$$12) {
//                    GuiMessage<FormattedCharSequence> $$13 = trimmedMessages.get($$12 + chatScrollbarPos);
//                    if ($$13 != null) {
//                        EventText eventText = new EventText($$13.getMessage());
//                        EventManager.call(eventText);
//                        int $$14 = p_93782_ - $$13.getAddedTime();
//                        if ($$14 < 200 || $$4) {
//                            double $$15 = $$4 ? (double) 1.0F : getTimeFactor($$14);
//                            int $$16 = (int) ((double) 255.0F * $$15 * $$7);
//                            int $$17 = (int) ((double) 255.0F * $$15 * $$8);
//                            ++$$11;
//                            if ($$16 > 3) {
//                                double $$19 = (double) (-$$12) * $$9;
//                                p_93781_.pushPose();
//                                p_93781_.translate(0.0F, 0.0F, 50.0F);
//                                GuiComponent.fill(p_93781_, -4, (int) ($$19 - $$9), $$6 + 4, (int) $$19, $$17 << 24);
//                                RenderSystem.enableBlend();
//                                p_93781_.translate(0.0F, 0.0F, 50.0F);
//                                mc.font.drawShadow(p_93781_, eventText.getFormattedCharSequence(), 0.0F, (float) ((int) ($$19 + $$10)), 16777215 + ($$16 << 24));
//                                RenderSystem.disableBlend();
//                                p_93781_.popPose();
//                            }
//                        }
//                    }
//                }
//
//                Deque<Component> chatQueue = ObfuscationReflectionHelper.getPrivateValue(ChatComponent.class, instance, "f_93762_");
//                if (!chatQueue.isEmpty()) {
//                    int $$20 = (int) ((double) 128.0F * $$7);
//                    int $$21 = (int) ((double) 255.0F * $$8);
//                    p_93781_.pushPose();
//                    p_93781_.translate(0.0F, 0.0F, 50.0F);
//                    GuiComponent.fill(p_93781_, -2, 0, $$6 + 4, 9, $$21 << 24);
//                    RenderSystem.enableBlend();
//                    p_93781_.translate(0.0F, 0.0F, 50.0F);
//                    mc.font.drawShadow(p_93781_, new TranslatableComponent("chat.queue", chatQueue.size()), 0.0F, 1.0F, 16777215 + ($$20 << 24));
//                    p_93781_.popPose();
//                    RenderSystem.disableBlend();
//                }
//
//                boolean newMessageSinceScroll = ObfuscationReflectionHelper.getPrivateValue(ChatComponent.class, instance, "f_93764_");
//                if ($$4) {
//                    Objects.requireNonNull(mc.font);
//                    int $$22 = 9;
//                    int $$23 = $$3 * $$22;
//                    int $$24 = $$11 * $$22;
//                    int $$25 = chatScrollbarPos * $$24 / $$3;
//                    int $$26 = $$24 * $$24 / $$23;
//                    if ($$23 != $$24) {
//                        int $$27 = $$25 > 0 ? 170 : 96;
//                        int $$28 = newMessageSinceScroll ? 13382451 : 3355562;
//                        p_93781_.translate(-4.0F, 0.0F, 0.0F);
//                        GuiComponent.fill(p_93781_, 0, -$$25, 2, -$$25 - $$26, $$28 + ($$27 << 24));
//                        GuiComponent.fill(p_93781_, 2, -$$25, 1, -$$25 - $$26, 13421772 + ($$27 << 24));
//                    }
//                }
//
//                p_93781_.popPose();
//            }
//        }
//    }
//
//    private static void processPendingMessages(ChatComponent instance) {
//        Deque<Component> chatQueue = ObfuscationReflectionHelper.getPrivateValue(ChatComponent.class, instance, "f_93762_");
//        long lastMessage = ObfuscationReflectionHelper.getPrivateValue(ChatComponent.class, instance, "f_93765_");
//        if (!chatQueue.isEmpty()) {
//            long $$0 = System.currentTimeMillis();
//            if ($$0 - lastMessage >= (long) (mc.options.chatDelay * (double) 1000.0F)) {
//                instance.addMessage(chatQueue.remove());
//                ObfuscationReflectionHelper.setPrivateValue(ChatComponent.class, instance, $$0, "f_93765_");
//            }
//        }
//    }
//
//    private static double getTimeFactor(int p_93776_) {
//        double $$1 = (double)p_93776_ / (double)200.0F;
//        $$1 = (double)1.0F - $$1;
//        $$1 *= 10.0F;
//        $$1 = Mth.clamp($$1, 0.0F, 1.0F);
//        $$1 *= $$1;
//        return $$1;
//    }
}
