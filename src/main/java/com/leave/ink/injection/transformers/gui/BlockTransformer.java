package com.leave.ink.injection.transformers.gui;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.EventBlockSurfaceRender;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.CallbackInfo;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;

@ClassTransformer(Block.class)
public class BlockTransformer extends Transformer {

    public static EventBlockSurfaceRender onBlockSurfaceRender(BlockState blockState) {
        return Main.INSTANCE != null && Main.INSTANCE.transformerLoader != null && mc.player != null && mc.level != null
                ? (EventBlockSurfaceRender) EventManager.call(new EventBlockSurfaceRender(blockState))
                : new EventBlockSurfaceRender(blockState);
    }

    @PushArgs(index = {0}, opcode = {ALOAD})
    @Inject(
            methodName = {"shouldRenderFace", "m_152444_"},
            desc = "(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/BlockGetter;Lnet/minecraft/core/BlockPos;Lnet/minecraft/core/Direction;Lnet/minecraft/core/BlockPos;)Z",
            at = InsertPosition.HEAD,
            callback = @CallbackInfo(callback = true, type = boolean.class)
    )

    public static CallBackInfo shouldRenderFace(CallBackInfo callBackInfo, BlockState blockState) {
        EventBlockSurfaceRender event = new EventBlockSurfaceRender(blockState);
        EventManager.call(event);
        if (event.isCancelled()) {
            callBackInfo.setBack(true);
            callBackInfo.setBackValue(false);
        }
        return callBackInfo;
    }
}
