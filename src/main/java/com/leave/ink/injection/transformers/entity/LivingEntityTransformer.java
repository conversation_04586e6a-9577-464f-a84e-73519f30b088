package com.leave.ink.injection.transformers.entity;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.injection.base.util.asm.tree.AbstractInsnNode;
import com.leave.ink.injection.base.util.asm.tree.InsnList;
import com.leave.ink.injection.base.util.asm.tree.MethodInsnNode;
import com.leave.ink.injection.base.util.asm.tree.MethodNode;
import com.leave.ink.events.EventJump;
import com.leave.ink.events.EventMoveMath;
import com.leave.ink.features.module.Module;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.rotation.RotationUtils;
import net.minecraft.core.BlockPos;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.phys.Vec3;
import java.lang.reflect.Method;
import java.util.ListIterator;

@ClassTransformer(LivingEntity.class)
public class LivingEntityTransformer extends Transformer {
    @PushArgs(index = 1, opcode = ALOAD)
    @Inject(callback = @CallbackInfo(callback = true), at = InsertPosition.HEAD, methodName = {"travel", "m_7023_"}, desc = "(Lnet/minecraft/world/phys/Vec3;)V")
    public static CallBackInfo travel(CallBackInfo callBackInfo, Vec3 vec3) {
        EventMoveMath eventMoveMath = new EventMoveMath(vec3);
        EventManager.call(eventMoveMath);
        callBackInfo.setBack(eventMoveMath.isCancelled());
        return callBackInfo;
    }

    @Overwrite(methodName = {"jumpFromGround", "m_6135_"}, desc = "()V")
    public static void jumpFromGround(LivingEntity instance) {
        float agnle = instance.getYRot();
        double motionY = getJumpPower(instance);
        if (instance.equals(mc.player)) {
            final EventJump jumpEvent = new EventJump(motionY, agnle);
            EventManager.call(jumpEvent);

            if (jumpEvent.isCancelled())
                return;

            agnle = jumpEvent.getYaw();
            motionY = jumpEvent.getMotionY();
        }

        Vec3 vec3 = instance.getDeltaMovement();
        instance.setDeltaMovement(vec3.x, motionY, vec3.z);

        if (instance.isSprinting()) {
            float f = agnle * ((float) Math.PI / 180F);
            instance.setDeltaMovement(instance.getDeltaMovement().add(-Mth.sin(f) * 0.2F, 0.0D, Mth.cos(f) * 0.2F));
        }

        instance.hasImpulse = true;

    }

    @ASM(methodName = {"tick", "m_8119_"}, desc = "()V")
    public void inject(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();

        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();

            if (instruction.getOpcode() == INVOKEVIRTUAL) {
                MethodInsnNode methodInsnNode = (MethodInsnNode) instruction;
                if (!methodInsnNode.owner.equals(Type.getInternalName(LivingEntity.class))) continue;

                if (methodInsnNode.name.equals("getYRot") || methodInsnNode.name.equals("m_146908_")) {
                    iterator.remove();


                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getYR", "(Lnet/minecraft/world/entity/LivingEntity;)F"));
                }
                if (methodInsnNode.name.equals("getXRot") || methodInsnNode.name.equals("m_146909_")) {
                    iterator.remove();

                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "getXR", "(Lnet/minecraft/world/entity/LivingEntity;)F"));
                }

            }

        }
    }

    public static float getXR(LivingEntity livingEntity) {
        if (livingEntity == mc.player && RotationUtils.active && RotationUtils.rotations != null)
            return RotationUtils.rotations.getPitch();

        return livingEntity.getXRot();
    }

    public static float getYR(LivingEntity livingEntity) {
        if (livingEntity == mc.player && RotationUtils.active && RotationUtils.rotations != null)
            return RotationUtils.rotations.getYaw();

        return livingEntity.getYRot();
    }

    @PushArgs(index = 0, opcode = Opcodes.ALOAD)
    @Inject(methodName = {"aiStep", "m_8107_"}, desc = "()V", at = InsertPosition.HEAD)
    public static void aiStep(LivingEntity instance) {
        if (instance == mc.player) {
            Module module = Main.INSTANCE.moduleManager.getModule("NoJumpDelay");
            if (module.isEnable())
                ObfuscationReflectionHelper.setPrivateValue(LivingEntity.class, instance, 0, "noJumpDelay");
        }
    }

    protected static float getJumpPower(LivingEntity instance) {
        return 0.42F * getBlockJumpFactor(instance) + instance.getJumpBoostPower();
    }

    protected static float getBlockJumpFactor(LivingEntity instance) {
        if (mc.level == null)
            return 0f;

        float f = mc.level.getBlockState(instance.blockPosition()).getBlock().getJumpFactor();
        BlockPos blockPos = null;

        try {
            Method method = ObfuscationReflectionHelper.findMethod(Entity.class, "getBlockPosBelowThatAffectsMyMovement", BlockPos.class);
            blockPos = (BlockPos) method.invoke(instance);
        } catch (Exception e) {
            e.printStackTrace();
        }

        float f1 = mc.level.getBlockState(blockPos).getBlock().getJumpFactor();
        return (double) f == 1.0D ? f1 : f;
    }
}

