package com.leave.ink.injection.transformers.client;

import com.leave.ink.Main;
import com.leave.ink.features.module.modules.other.ClayGuns;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import com.leave.ink.utils.misc.ClassUtils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import net.minecraft.client.CameraType;
import net.minecraft.client.Options;

@ClassTransformer(Options.class)
public class OptionsTransformer extends Transformer {
    @Overwrite(methodName = {"setCameraType", "m_92157_"}, desc = "(Lnet/minecraft/client/CameraType;)V")
    public static void setCameraType(Options instance, CameraType p_92158_) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");

        if (ClassUtils.hasClass("clay.byte")) {
            if (clayGuns.isCalledByClayPackage())
                return;
        }

        ObfuscationReflectionHelper.setPrivateValue(Options.class, instance, p_92158_, "cameraType");
    }
}
