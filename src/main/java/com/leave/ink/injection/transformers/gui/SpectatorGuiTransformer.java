package com.leave.ink.injection.transformers.gui;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.InsertPosition;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.spectator.SpectatorGui;

@ClassTransformer(SpectatorGui.class)
public class SpectatorGuiTransformer extends Transformer {

    @PushArgs(index = 1, opcode = ALOAD)
    @Inject(methodName = {"renderHotbar", "m_280623_"}, desc = "(Lnet/minecraft/client/gui/GuiGraphics;)V", at = InsertPosition.LAST)
    public static void renderHotbar(GuiGraphics guiGraphics) {
        EventRender2D eventRender2D = new EventRender2D(guiGraphics, guiGraphics.pose(), mc.getWindow().getGuiScaledHeight(), mc.getWindow().getGuiScaledWidth());
        EventManager.call(eventRender2D);
    }
}
