package com.leave.ink.injection.transformers.input;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.EventScroll;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.module.modules.combat.AutoArmor;
import com.leave.ink.features.module.modules.world.ChestStealer;
import com.leave.ink.features.module.modules.world.InvClear;
import com.leave.ink.features.module.modules.world.Scaffold;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.Utils;
import net.minecraft.client.KeyMapping;
import net.minecraft.client.MouseHandler;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.world.inventory.ChestMenu;

@ClassTransformer(MouseHandler.class)
public class MouseHandlerTransformer extends Transformer implements IMinecraft {
    private static KillAura aura = null;
    private static Scaffold scaffold = null;
    private static ChestStealer chestStealer = null;
    private static InvClear invClear = null;
    private static AutoArmor autoArmor = null;
    public static boolean usingItem = false;
    @InjectPoint(methodName = {"isSpectator", "m_5833_"}, desc = "()Z")
    @PushArgs(index = 9, opcode = DLOAD)
    @Inject(methodName = {"onScroll", "m_91526_"},desc = "(JDD)V", callback = @CallbackInfo(callback = true))
    public static CallBackInfo onScroll(CallBackInfo callBackInfo, double d) {
        EventScroll eventScroll = new EventScroll(d);
        EventManager.call(eventScroll);
        callBackInfo.setBack(eventScroll.isCancelled());
        return callBackInfo;
    }

    @PushArgs(index = {0,1,3,4,5}, opcode = {ALOAD,LLOAD,ILOAD,ILOAD,ILOAD})
    @Inject(methodName = {"onPress", "m_91530_"},at = InsertPosition.HEAD, desc = "(JIII)V",callback = @CallbackInfo(callback = true))
    public static CallBackInfo attack(CallBackInfo callBackInfo,MouseHandler in, long a, int b, int c, int d) {
        if(Utils.isNull()) return callBackInfo;
        if(aura == null) aura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
        if(scaffold == null) scaffold = (Scaffold) Main.INSTANCE.moduleManager.getModule("Scaffold");
        if(chestStealer == null) chestStealer = (ChestStealer) Main.INSTANCE.moduleManager.getModule("ChestStealer");
        if(invClear == null) invClear = (InvClear) Main.INSTANCE.moduleManager.getModule("InvClear");
        if(autoArmor == null) autoArmor = (AutoArmor) Main.INSTANCE.moduleManager.getModule("AutoArmor");

        if (chestStealer != null && chestStealer.isEnable() &&
                mc.player != null && mc.player.containerMenu instanceof ChestMenu) {

                if(chestStealer.disableClick.getValue() && mc.player.containerMenu instanceof ChestMenu) {
                    if(!chestStealer.hasItems)
                        callBackInfo.setBack(true);

            }
        }

        if (mc.player != null && mc.screen instanceof InventoryScreen) {
            if (invClear != null && invClear.isEnable()) {
                if (invClear.getIgnoreMouseInput() != null && invClear.getIgnoreMouseInput().getValue()) {
                    if (!invClear.isSortFinished()) {
                        if (b == 0 || b == 1) {
                            callBackInfo.setBack(true);
                            return callBackInfo;
                        }
                    }
                }
            }
        }

        //release
        if (c == 0) return callBackInfo;
        if(aura.isEnable() && mc.screen == null) {
            if(aura.target != null && b == 0) {
                if (mc.options.keyAttack.isDown())
                    KeyMapping.set(mc.options.keyAttack.getKey(), false);

                callBackInfo.setBack(true);
            }
        }
        if(scaffold.isEnable() && mc.screen == null) {

            callBackInfo.setBack(true);
        }


//        if(b == 1) {
////            mc.player.isUsingItem();
//            if (!(mc.player.getMainHandItem().getItem() instanceof SwordItem)) {
//                callBackInfo.setBack(false);
//                usingItem = true;
//            }else {
//                usingItem = false;
//            }
//        }
        return callBackInfo;
    }
}
