package com.leave.ink.injection.transformers.input;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.events.EventKeyPress;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.CallbackInfo;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.PushArgs;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.utils.Utils;
import net.minecraft.client.KeyboardHandler;

@ClassTransformer(KeyboardHandler.class)
public class KeyboardHandlerTransformer extends Transformer {
    @PushArgs(index = {1,3,4,5,6}, opcode = {LLOAD,ILOAD,ILOAD,ILOAD,ILOAD})
    @Inject(methodName = {"keyPress", "m_90893_"},at = InsertPosition.HEAD, desc = "(JIIII)V",callback = @CallbackInfo(callback = true))
    public static CallBackInfo keyPress(CallBackInfo callBackInfo, long p_90894_, int p_90895_, int p_90896_, int p_90897_, int p_90898_) {
        if(Utils.isNull()) return callBackInfo;
        EventKeyPress keyPress = new EventKeyPress(p_90895_, p_90896_, p_90897_);
        EventManager.call(keyPress);
        callBackInfo.setBack(keyPress.isCancelled());
        return callBackInfo;
    }
}
