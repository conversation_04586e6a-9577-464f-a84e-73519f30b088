package com.leave.ink.injection.transformers.render;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.events.hud.EventRenderSkija;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.annotation.InjectPoint;
import com.mojang.blaze3d.systems.RenderSystem;

@ClassTransformer(RenderSystem.class)
public class RenderSystemTransformer extends Transformer {
    @InjectPoint(methodName = {"glfwSwapBuffers"}, desc = "(J)V")
    @Inject(methodName = {"flipFrame"}, desc = "(J)V")
    public static void f() {
        EventRenderSkija eventRenderSkija = new EventRenderSkija();
        EventManager.call(eventRenderSkija);
    }
}
