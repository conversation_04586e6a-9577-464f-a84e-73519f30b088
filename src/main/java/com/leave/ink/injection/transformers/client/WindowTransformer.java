package com.leave.ink.injection.transformers.client;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.EventWindowSize;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.util.InsertPosition;
import com.mojang.blaze3d.platform.Window;

@ClassTransformer(Window.class)
public class WindowTransformer extends Transformer {
    //refreshFramebufferSize
    @Inject(at = InsertPosition.LAST, methodName = {"onResize", "m_85427_"},desc = "(JII)V")
    public static void on() {
        if(Main.INSTANCE.skiaProcess != null) {
            Main.INSTANCE.skiaProcess.initSkia();
        }
        EventManager.call(new EventWindowSize());
    }
}
