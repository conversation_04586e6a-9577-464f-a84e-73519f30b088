package com.leave.ink.injection.transformers.render;

import com.leave.ink.Main;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.features.module.modules.render.ItemPhysic;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;

import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.EntityRendererProvider;
import net.minecraft.client.renderer.entity.ItemEntityRenderer;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.client.resources.model.BakedModel;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemDisplayContext;
import net.minecraft.world.item.ItemStack;

import java.util.Random;

@ClassTransformer(ItemEntityRenderer.class)
public class ItemEntityRendererTransformer extends Transformer {
    private static final Random random = new Random();

    @ASM(methodName = {"render", "m_7392_"}, desc = "(Lnet/minecraft/world/entity/item/ItemEntity;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public void injectRender(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        if (methodNode == null) {
            return;
        }

        list.add(new VarInsnNode(Opcodes.ALOAD, 1));
        list.add(new VarInsnNode(Opcodes.FLOAD, 2));
        list.add(new VarInsnNode(Opcodes.FLOAD, 3));
        list.add(new VarInsnNode(Opcodes.ALOAD, 4));
        list.add(new VarInsnNode(Opcodes.ALOAD, 5));
        list.add(new VarInsnNode(Opcodes.ILOAD, 6));

        list.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(getClass()),
                "render",
                "(Lnet/minecraft/world/entity/item/ItemEntity;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V",
                false
        ));

        list.add(new InsnNode(Opcodes.RETURN));
        methodNode.instructions.insert(list);
    }

    protected static int getRenderAmount(ItemStack stack) {
        int count = stack.getCount();
        if (count > 48) return 5;
        if (count > 32) return 4;
        if (count > 16) return 3;
        if (count > 1) return 2;
        return 1;
    }

    public static ItemRenderer getItemRendererFromContext() {
        EntityRendererProvider.Context context = new EntityRendererProvider.Context(
                mc.getEntityRenderDispatcher(),
                mc.getItemRenderer(),
                mc.getBlockRenderer(),
                mc.getEntityRenderDispatcher().getItemInHandRenderer(),
                mc.getResourceManager(),
                mc.getEntityModels(),
                mc.font
        );

        return context.getItemRenderer();
    }

    public static void render(ItemEntity entity, float p_115037_, float p_115038_, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight) {
        var itemPhysic = (ItemPhysic) Main.INSTANCE.moduleManager.getModule("ItemPhysic");
        if (itemPhysic.isEnable()) {
            poseStack.pushPose();
            ItemStack itemstack = entity.getItem();
            int i = itemstack.isEmpty() ? 187 : Item.getId(itemstack.getItem()) + itemstack.getDamageValue();
            random.setSeed(i);
            BakedModel bakedmodel = getItemRendererFromContext().getModel(itemstack, entity.level(), null, entity.getId());
            boolean is3D = bakedmodel.isGui3d();
            int renderAmount = getRenderAmount(itemstack);

            applyPhysicalEffect(entity, poseStack);

            float pitch = entity.onGround() ? 90 : (entity.getXRot() + 1) % 360;
            poseStack.mulPose(Axis.XP.rotationDegrees(pitch));
            poseStack.mulPose(Axis.ZP.rotationDegrees(entity.getYRot()));

            float scaleX = bakedmodel.getTransforms().ground.scale.x();
            float scaleY = bakedmodel.getTransforms().ground.scale.y();
            float scaleZ = bakedmodel.getTransforms().ground.scale.z();

            if (!is3D) {
                float translateX = -0.0F * (renderAmount - 1) * 0.5F * scaleX;
                float translateY = -0.0F * (renderAmount - 1) * 0.5F * scaleY;
                float translateZ = -0.09375F * (renderAmount - 1) * 0.5F * scaleZ;
                poseStack.translate(translateX, translateY, translateZ);
            }

            for (int k = 0; k < renderAmount; ++k) {
                poseStack.pushPose();
                if (k > 0) {
                    applyRandomTranslation(is3D, poseStack);
                }

                getItemRendererFromContext().render(itemstack, ItemDisplayContext.GROUND, false, poseStack, bufferSource, packedLight, OverlayTexture.NO_OVERLAY, bakedmodel);
                poseStack.popPose();
                if (!is3D) {
                    poseStack.translate(0.0D, 0.0D, 0.09375F * scaleZ);
                }
            }

            poseStack.popPose();
        } else {
            poseStack.pushPose();
            ItemStack itemstack = entity.getItem();
            int i = itemstack.isEmpty() ? 187 : Item.getId(itemstack.getItem()) + itemstack.getDamageValue();
            random.setSeed(i);
            BakedModel bakedmodel = getItemRendererFromContext().getModel(itemstack, entity.level(), null, entity.getId());
            boolean flag = bakedmodel.isGui3d();
            int j = getRenderAmount(itemstack);
            float f = 0.25F;
            float f1 = Mth.sin(((float) entity.getAge() + p_115038_) / 10.0F + entity.bobOffs) * 0.1F + 0.1F;
            float f2 = shouldBob() ? bakedmodel.getTransforms().getTransform(ItemDisplayContext.GROUND).scale.y() : 0.0F;
            poseStack.translate(0.0, f1 + 0.25F * f2, 0.0);
            float f3 = entity.getSpin(p_115038_);
            poseStack.mulPose(Axis.YP.rotation(f3));
            float f11;
            float f13;
            if (!flag) {
                float f7 = -0.0F * (float) (j - 1) * 0.5F;
                f11 = -0.0F * (float) (j - 1) * 0.5F;
                f13 = -0.09375F * (float) (j - 1) * 0.5F;
                poseStack.translate(f7, f11, f13);
            }

            for (int k = 0; k < j; ++k) {
                poseStack.pushPose();
                if (k > 0) {
                    if (flag) {
                        f11 = (random.nextFloat() * 2.0F - 1.0F) * 0.15F;
                        f13 = (random.nextFloat() * 2.0F - 1.0F) * 0.15F;
                        float f10 = (random.nextFloat() * 2.0F - 1.0F) * 0.15F;
                        poseStack.translate(shouldSpreadItems() ? (double) f11 : 0.0, shouldSpreadItems() ? (double) f13 : 0.0, shouldSpreadItems() ? (double) f10 : 0.0);
                    } else {
                        f11 = (random.nextFloat() * 2.0F - 1.0F) * 0.15F * 0.5F;
                        f13 = (random.nextFloat() * 2.0F - 1.0F) * 0.15F * 0.5F;
                        poseStack.translate(shouldSpreadItems() ? (double) f11 : 0.0, shouldSpreadItems() ? (double) f13 : 0.0, 0.0);
                    }
                }

                getItemRendererFromContext().render(itemstack, ItemDisplayContext.GROUND, false, poseStack, bufferSource, packedLight, OverlayTexture.NO_OVERLAY, bakedmodel);
                poseStack.popPose();
                if (!flag) {
                    poseStack.translate(0.0, 0.0, 0.09375);
                }
            }

            poseStack.popPose();
        }
    }

    public static boolean shouldSpreadItems() {
        return true;
    }

    public static boolean shouldBob() {
        return true;
    }

    private static void applyPhysicalEffect(ItemEntity entity, PoseStack poseStack) {
        if (!(entity.getItem().getItem() instanceof BlockItem)) {
            poseStack.translate(0.0F, 0.1F, 0.0F);
        } else {
            poseStack.translate(0.0F, 0.2F, 0.0F);
        }
    }

    private static void applyRandomTranslation(boolean is3D, PoseStack poseStack) {
        if (is3D) {
            float randX = (random.nextFloat() * 2.0F - 1.0F) * 0.15F;
            float randY = (random.nextFloat() * 2.0F - 1.0F) * 0.15F;
            float randZ = (random.nextFloat() * 2.0F - 1.0F) * 0.15F;
            poseStack.translate(randX, randY, randZ);
        } else {
            float randX = (random.nextFloat() * 2.0F - 1.0F) * 0.075F;
            float randY = (random.nextFloat() * 2.0F - 1.0F) * 0.075F;
            poseStack.translate(randX, randY, 0.0D);
        }
    }
}
