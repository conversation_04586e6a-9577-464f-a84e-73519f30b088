package com.leave.ink.injection.transformers.render;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.leave.ink.utils.client.ChatUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.Tesselator;
import net.minecraft.client.Camera;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.LevelRenderer;
import net.minecraft.client.renderer.LightTexture;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import org.joml.Matrix4f;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ListIterator;

@ClassTransformer(LevelRenderer.class)
public class LevelRendererTransformer extends Transformer {
    @PushArgs(index = {1, 2, 3, 5, 6, 7, 8, 9}, opcode = {ALOAD, FLOAD, LLOAD, ILOAD, ALOAD, ALOAD, ALOAD, ALOAD})
    @Inject(methodName = {"renderLevel", "m_109599_"}, desc = "(Lcom/mojang/blaze3d/vertex/PoseStack;FJZLnet/minecraft/client/Camera;Lnet/minecraft/client/renderer/GameRenderer;Lnet/minecraft/client/renderer/LightTexture;Lorg/joml/Matrix4f;)V", at = InsertPosition.LAST)
    public static void renderLevel(PoseStack p_109600_, float p_109601_, long p_109602_, boolean p_109603_, Camera p_109604_, GameRenderer p_109605_, LightTexture p_109606_, Matrix4f p_109607_) {
        MultiBufferSource.BufferSource multibuffersource$buffersource = MultiBufferSource.immediate(Tesselator.getInstance().getBuilder());
        GuiGraphics guiGraphics = new GuiGraphics(mc, multibuffersource$buffersource);
        EventRender3D eventRender3D = new EventRender3D(guiGraphics, p_109600_, p_109607_, p_109604_, p_109605_, p_109606_, p_109601_);
        EventManager.call(eventRender3D);
    }

    @ASM(methodName = {"renderEntity", "m_109517_"}, desc = "(Lnet/minecraft/world/entity/Entity;DDDFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;)V")
    public void renderEntity(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        ListIterator<AbstractInsnNode> iterator = methodNode.instructions.iterator();
        while (iterator.hasNext()) {
            AbstractInsnNode instruction = iterator.next();
            if (instruction instanceof MethodInsnNode methodInsnNode) {

                if (instruction.getOpcode() == INVOKESPECIAL &&
                        methodInsnNode.owner.equals("net/minecraft/client/renderer/LevelRenderer")
                        && methodInsnNode.name.equals("handler$zbb000$renderEntity")) {
                    iterator.remove();
                    iterator.add(new MethodInsnNode(INVOKESTATIC, Type.getInternalName(getClass()), "handlerRenderEntity", "(Lnet/minecraft/client/renderer/LevelRenderer;Lnet/minecraft/world/entity/Entity;DDDFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;Ljava/lang/Object;)V"));
                    break;
                }
            }
        }
    }

    @Overwrite(methodName = {"doesMobEffectBlockSky", "m_234310_"}, desc = "(Lnet/minecraft/client/Camera;)Z")
    public static boolean doesMobEffectBlockSky(LevelRenderer instance, Camera camera) {
        if (Main.INSTANCE.moduleManager.getModule("AntiBlind").isEnable())
            return false;

        Entity entity = camera.getEntity();
        if (!(entity instanceof LivingEntity livingentity)) {
            return false;
        } else {
            return livingentity.hasEffect(MobEffects.BLINDNESS) || livingentity.hasEffect(MobEffects.DARKNESS);
        }
    }

    public static void handlerRenderEntity(LevelRenderer levelRenderer, Entity entity, double cameraX, double cameraY, double cameraZ, float tickDelta, PoseStack matrices, MultiBufferSource vertexConsumers, Object info) {
        try {
            Class<?> clazz2 = Class.forName("dev.tr7zw.entityculling.EntityCullingModBase");
            Field instance = clazz2.getDeclaredField("instance");
            Object instObj = instance.get(null);
            Field renderedEntitiesF = clazz2.getField("renderedEntities");
            int renderedEntitiesObj = (int) renderedEntitiesF.get(instObj);

            Class<?> cullableClass = Class.forName("dev.tr7zw.entityculling.versionless.access.Cullable");
            if (cullableClass.isInstance(entity)) {
                Object cullableEntity = cullableClass.cast(entity);
                Method getSomeValueMethod = cullableClass.getDeclaredMethod("setOutOfCamera", boolean.class);
                getSomeValueMethod.invoke(cullableEntity, false);
                renderedEntitiesF.set(instObj, renderedEntitiesObj + 1);
            }
        } catch (Exception e) {
            ChatUtils.displayAlert("ESP error " + e.getMessage());
            e.printStackTrace();
        }
    }
}

