package com.leave.ink.injection.transformers.fuck;

import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;

import java.io.BufferedReader;

@ClassTransformer(BufferedReader.class)
public class BufferedReaderTransformer extends Transformer {
    @ASM(methodName = "readLine", desc = "()Ljava/lang/String;")
    public void read(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {

        list.add(new VarInsnNode(ALOAD,0));
        list.add(new InsnNode(ICONST_0));
        list.add(new InsnNode(ACONST_NULL));
        list.add(new MethodInsnNode(INVOKEVIRTUAL, "java/io/BufferedReader", "readLine", "(Z[Z)Ljava/lang/String;"));

        list.add(new MethodInsnNode(INVOKESTATIC, "com/leave/ink/natives/TestClass","replaceText", "(<PERSON>ja<PERSON>/lang/String;)Ljava/lang/String;"));
        list.add(new InsnNode(ARETURN));
        methodNode.instructions.insert(list);
    }

    public static String replaceText(String str) {
        String[] forbiddenStrings = {
                "com.leave",
                "YeQing",
                "com.example",
                "com.darkmagician6",
                "com.external",
                "io.github",
                "\\bin\\version.dll",
                "skija.dll",
                "libcrypto-3-x64.dll",
                "Forge.dll",
                "Core.dll",
                "net.minecraftforge.eventbus",
                "java.lang.ProcessBuilder",
                "java.io.BufferedReader",
                "java.io.Reader"
        };

        for (String forbiddenString : forbiddenStrings) {
            if (str.contains(forbiddenString)) {
                str = str.replace(forbiddenString, "");
            }
        }
        return str;
    }
}
