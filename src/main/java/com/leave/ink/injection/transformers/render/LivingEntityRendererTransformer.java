package com.leave.ink.injection.transformers.render;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.Main;
import com.leave.ink.events.EventRenderLiving;
import com.leave.ink.events.EventType;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.*;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.utils.Utils;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.LivingEntityRenderer;
import net.minecraft.world.entity.LivingEntity;
import org.lwjgl.opengl.GL20;

@ClassTransformer(LivingEntityRenderer.class)
public class LivingEntityRendererTransformer extends Transformer {
    @PushArgs(index = {0, 1, 2, 3, 4, 5, 6}, opcode = {ALOAD, ALOAD, FLOAD, FLOAD, ALOAD, ALOAD, ILOAD})
    @Inject(callback = @CallbackInfo(callback = true), at = InsertPosition.HEAD, methodName = {"render", "m_7392_"}, desc = "(Lnet/minecraft/world/entity/LivingEntity;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static CallBackInfo renderPre(CallBackInfo callBackInfo, LivingEntityRenderer instance, LivingEntity entity, float p_115309_, float p_115310_, PoseStack p_115311_, MultiBufferSource p_115312_, int p_115313_) {
        EventRenderLiving eventRenderLiving = new EventRenderLiving(EventType.PRE, entity, instance, p_115310_, p_115311_, p_115312_, p_115313_);
        EventManager.call(eventRenderLiving);
        callBackInfo.setBack(eventRenderLiving.isCancelled());
        if (Utils.isValidEntity(entity)) {
            if (Main.INSTANCE.moduleManager.getModule("Chams").isEnable()) {
                GL20.glEnable(GL20.GL_POLYGON_OFFSET_FILL);
                GL20.glPolygonOffset(1.0F, -1000000F);
            }
        }

        return callBackInfo;
    }
    @PushArgs(index = {0, 1, 2, 3, 4, 5, 6,16,17,18}, opcode = {ALOAD, ALOAD, FLOAD, FLOAD, ALOAD, ALOAD, ILOAD,ILOAD,ILOAD,ILOAD})
    @InjectPoint(methodName = {"popPose", "m_85849_"},desc = "()V")
    @Inject( methodName = {"render", "m_7392_"}, desc = "(Lnet/minecraft/world/entity/LivingEntity;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderPre2(LivingEntityRenderer instance, LivingEntity entity, float p_115309_, float p_115310_, PoseStack p_115311_,
                                          MultiBufferSource p_115312_,
                                          int p_115313_,
                                          boolean flag,boolean flag1, boolean flag2) {
        EventRenderLiving eventRenderLiving = new EventRenderLiving(EventType.MIDDLE, entity, instance, p_115310_, p_115311_, p_115312_, p_115313_);
        eventRenderLiving.flag =flag;
        eventRenderLiving.flag1 = flag1;
        eventRenderLiving.flag2 = flag2;
        EventManager.call(eventRenderLiving);

    }

    @PushArgs(index = {0, 1, 2, 3, 4, 5, 6}, opcode = {ALOAD, ALOAD, FLOAD, FLOAD, ALOAD, ALOAD, ILOAD})
    @Inject(at = InsertPosition.LAST, methodName = {"render", "m_7392_"}, desc = "(Lnet/minecraft/world/entity/LivingEntity;FFLcom/mojang/blaze3d/vertex/PoseStack;Lnet/minecraft/client/renderer/MultiBufferSource;I)V")
    public static void renderPost(LivingEntityRenderer instance, LivingEntity entity, float p_115309_, float p_115310_, PoseStack p_115311_, MultiBufferSource p_115312_, int p_115313_) {
        EventRenderLiving eventRenderLiving = new EventRenderLiving(EventType.POST, entity, instance, p_115310_, p_115311_, p_115312_, p_115313_);
        EventManager.call(eventRenderLiving);
        if (Utils.isValidEntity(entity)) {
            if (Main.INSTANCE.moduleManager.getModule("Chams").isEnable()) {
                GL20.glPolygonOffset(1.0F, 1000000F);
                GL20.glDisable(GL20.GL_POLYGON_OFFSET_FILL);
            }
        }
    }
}