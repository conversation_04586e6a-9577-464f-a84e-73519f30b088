package com.leave.ink.injection.transformers.gui;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.events.EventText;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.annotation.Overwrite;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import net.minecraft.client.gui.Font;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.network.chat.FormattedText;
import net.minecraft.util.FormattedCharSequence;
import net.minecraft.util.Mth;

import org.joml.Matrix4f;
import org.joml.Vector3f;

import java.lang.reflect.Method;

@ClassTransformer(Font.class)
public class FontTransformer extends Transformer {
    @Overwrite(methodName = {"width", "m_92895_"}, desc = "(Ljava/lang/String;)I")
    public static int width(Font instance, String p_92896_) {
        EventText eventText = new EventText(p_92896_);
        EventManager.call(eventText);

        return Mth.ceil(instance.getSplitter().stringWidth(eventText.getText()));
    }

    @Overwrite(methodName = {"width", "m_92852_"}, desc = "(Lnet/minecraft/network/chat/FormattedText;)I")
    public static int width(Font instance, FormattedText p_92853_) {
        EventText eventText = new EventText(p_92853_);
        EventManager.call(eventText);

        return Mth.ceil(instance.getSplitter().stringWidth(eventText.getFormattedText()));
    }

    @Overwrite(methodName = {"width", "m_92724_"}, desc = "(Lnet/minecraft/util/FormattedCharSequence;)I")
    public static int width(Font instance, FormattedCharSequence p_92725_) {
        EventText eventText = new EventText(p_92725_);
        EventManager.call(eventText);

        return Mth.ceil(instance.getSplitter().stringWidth(eventText.getFormattedCharSequence()));
    }

    private static int adjustColor(int p_92720_) {
        return (p_92720_ & -67108864) == 0 ? p_92720_ | -16777216 : p_92720_;
    }

    @Overwrite(methodName = {"drawInternal", "m_272078_"}, desc = "(Ljava/lang/String;FFIZLorg/joml/Matrix4f;Lnet/minecraft/client/renderer/MultiBufferSource;Lnet/minecraft/client/gui/Font$DisplayMode;IIZ)I")
    public static int drawInternal(Font instance, String p_273658_, float p_273086_, float p_272883_, int p_273547_, boolean p_272778_, Matrix4f p_272662_, MultiBufferSource p_273012_, Font.DisplayMode p_273381_, int p_272855_, int p_272745_, boolean p_272785_) {
        EventText eventText = new EventText(p_273658_, p_273086_, p_273547_);
        EventManager.call(eventText);

        if (eventText.getText() == null) {
            return 0;
        } else {
            if (p_272785_) {
                eventText.setText(instance.bidirectionalShaping(eventText.getText()));
            }

            int color = p_273547_;
            color = adjustColor(color);

            Matrix4f matrix4f = new Matrix4f(p_272662_);
            Method method = ObfuscationReflectionHelper.findMethod(Font.class, "renderText", float.class, String.class, float.class, float.class, int.class, boolean.class, Matrix4f.class, MultiBufferSource.class, Font.DisplayMode.class, int.class, int.class);
            if (p_272778_) {
                try {
                    method.invoke(instance, eventText.getText(), p_273086_, p_272883_, color, true, p_272662_, p_273012_, p_273381_, p_272855_, p_272745_);
                } catch (Exception ignored) {
                }
                matrix4f.translate(new Vector3f(0.0F, 0.0F, 0.03F));
            }

            try {
                p_273086_ = (float) method.invoke(instance, eventText.getText(), p_273086_, p_272883_, color, false, matrix4f, p_273012_, p_273381_, p_272855_, p_272745_);
            } catch (Exception ignored) {
            }
            return (int) p_273086_ + (p_272778_ ? 1 : 0);
        }
    }

    @Overwrite(methodName = {"drawInternal", "m_272085_"}, desc = "(Lnet/minecraft/util/FormattedCharSequence;FFIZLorg/joml/Matrix4f;Lnet/minecraft/client/renderer/MultiBufferSource;Lnet/minecraft/client/gui/Font$DisplayMode;II)I")
    public static int drawInternal(Font instance, FormattedCharSequence p_273025_, float p_273121_, float p_272717_, int p_273653_, boolean p_273531_, Matrix4f p_273265_, MultiBufferSource p_273560_, Font.DisplayMode p_273342_, int p_273373_, int p_273266_) {
        EventText eventText = new EventText(p_273025_);
        EventManager.call(eventText);

        p_273653_ = adjustColor(p_273653_);
        Matrix4f matrix4f = new Matrix4f(p_273265_);
        Method method = ObfuscationReflectionHelper.findMethod(Font.class, "renderText",float.class, FormattedCharSequence.class, float.class, float.class, int.class, boolean.class, Matrix4f.class, MultiBufferSource.class, Font.DisplayMode.class, int.class, int.class);
        if (p_273531_) {
            try {
                method.invoke(instance, eventText.getFormattedCharSequence(), p_273121_, p_272717_, p_273653_, true, p_273265_, p_273560_, p_273342_, p_273373_, p_273266_);
            } catch (Exception ignored) {
            }
            matrix4f.translate(new Vector3f(0.0F, 0.0F, 0.03F));
        }

        try {
            p_273121_ = (float) method.invoke(instance, eventText.getFormattedCharSequence(), p_273121_, p_272717_, p_273653_, false, matrix4f, p_273560_, p_273342_, p_273373_, p_273266_);
        } catch (Exception ignored) {
        }

        return (int) p_273121_ + (p_273531_ ? 1 : 0);
    }
}
