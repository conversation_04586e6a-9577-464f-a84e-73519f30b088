package com.leave.ink.injection.transformers.render;

import com.leave.ink.Main;
import com.leave.ink.injection.base.annotation.CallbackInfo;
import com.leave.ink.injection.base.annotation.Inject;
import com.leave.ink.injection.base.util.InsertPosition;
import com.leave.ink.injection.base.util.asm.Opcodes;
import com.leave.ink.injection.base.util.asm.Type;
import com.leave.ink.features.module.modules.render.NoFog;
import com.leave.ink.injection.Transformer;
import com.leave.ink.injection.base.annotation.ASM;
import com.leave.ink.injection.base.annotation.ClassTransformer;
import com.leave.ink.injection.base.util.CallBackInfo;
import com.leave.ink.injection.base.util.asm.tree.*;
import com.mojang.blaze3d.systems.RenderSystem;
import net.minecraft.client.renderer.FogRenderer;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.level.dimension.DimensionType;

@ClassTransformer(FogRenderer.class)
public class FogRendererTransformer extends Transformer {
    @Inject(methodName = {"setupFog", "m_234172_"}, desc = "(Lnet/minecraft/client/Camera;Lnet/minecraft/client/renderer/FogRenderer$FogMode;FZF)V", at = InsertPosition.HEAD, callback = @CallbackInfo(callback = true))
    public static CallBackInfo setupFog2(CallBackInfo callBackInfo) {
        callBackInfo.setBack(Main.INSTANCE.moduleManager.getModule("AntiBlind").isEnable() && (mc.player.hasEffect(MobEffects.BLINDNESS) || mc.player.hasEffect(MobEffects.DARKNESS)));
        return callBackInfo;
    }

    @ASM(methodName = {"setupFog", "m_234172_"}, desc = "(Lnet/minecraft/client/Camera;Lnet/minecraft/client/renderer/FogRenderer$FogMode;FZF)V")
    public void setupFog(InsnList list, MethodNode methodNode, CallBackInfo callBackInfo) {
        InsnList instructions = new InsnList();
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(FogRendererTransformer.class),
                "shouldDisableFog",
                "()Z",
                false
        ));
        LabelNode continueLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        instructions.add(new MethodInsnNode(
                Opcodes.INVOKESTATIC,
                Type.getInternalName(FogRendererTransformer.class),
                "setFarFogDistance",
                "()V",
                false
        ));
        instructions.add(new InsnNode(Opcodes.RETURN));
        instructions.add(continueLabel);
        methodNode.instructions.insert(instructions);
    }

    public static boolean shouldDisableFog() {
        if (NoFog.shouldDisableFog()) {
            return true;
        }

        if (NoFog.shouldDisableLavaFog() && mc.gameRenderer != null && mc.gameRenderer.getMainCamera() != null) {
            try {
                Object fluidInCamera = mc.gameRenderer.getMainCamera().getFluidInCamera();
                if (fluidInCamera != null && fluidInCamera.toString().contains("LAVA")) {
                    return true;
                }
            } catch (Exception ignored) {
            }
        }

        if (NoFog.shouldDisableWaterFog() && mc.gameRenderer != null && mc.gameRenderer.getMainCamera() != null) {
            try {
                Object fluidInCamera = mc.gameRenderer.getMainCamera().getFluidInCamera();
                if (fluidInCamera != null && fluidInCamera.toString().contains("WATER")) {
                    return true;
                }
            } catch (Exception ignored) {
            }
        }

        if (NoFog.shouldDisableEndFog() && mc.level != null) {
            DimensionType dimensionType = mc.level.dimensionType();
            boolean isEndDimension = dimensionType.effectsLocation().getPath().contains("end");
            if (isEndDimension) {
                return true;
            }
        }

        return NoFog.shouldDisableWeatherFog() && mc.level != null && (mc.level.isRaining() || mc.level.isThundering());
    }

    public static void setFarFogDistance() {
        RenderSystem.setShaderFogStart(-8.0F);
        RenderSystem.setShaderFogEnd(1000000.0F);
    }
}