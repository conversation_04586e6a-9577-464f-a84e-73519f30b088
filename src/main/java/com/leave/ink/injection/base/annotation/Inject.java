package com.leave.ink.injection.base.annotation;


import com.leave.ink.injection.base.util.InsertPosition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Inject {
    String[] methodName();

    String desc();

    InsertPosition at() default InsertPosition.CUSTOM;

    CallbackInfo callback() default @CallbackInfo(callback = false, type = void.class);
}
