package com.leave.ink.injection.base.util;

import com.leave.ink.injection.base.util.asm.tree.AbstractInsnNode;
import com.leave.ink.injection.base.util.asm.tree.InsnList;
import com.leave.ink.injection.base.util.asm.tree.MethodNode;
import lombok.Getter;

@Getter
public enum InsertPosition {
    STR(true),
    CUSTOM(true),
    NONE(true),
    HEAD(false),
    LAST(true);
    private MethodNode methodNode;
    private boolean isBefore;

    InsertPosition(boolean isBefore) {
        this.isBefore = isBefore;
    }

    public void setMethodNode(MethodNode methodNode) {
        this.methodNode = methodNode;
    }

    public void setBefore(boolean before) {
        isBefore = before;
    }

    public void insert(AbstractInsnNode point, InsnList list) {
        if (isBefore()) {
            getMethodNode().instructions.insertBefore(point, list);
        } else {
            getMethodNode().instructions.insert(point, list);
        }
    }

    public AbstractInsnNode getPosition(InsertPosition insertPosition) {
        return switch (insertPosition) {
            case HEAD -> getMethodNode().instructions.getFirst();
            case LAST -> getMethodNode().instructions.get(getMethodNode().instructions.size() - 2);
            default -> null;
        };
    }
}
