<!DOCTYPE html>
<html lang="en">
<!--
 * ASM: a very small and fast Java bytecode manipulation framework
 * Copyright (c) 2000-2011 INRIA, France Telecom
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. Neither the name of the copyright holders nor the names of its
 *    contributors may be used to endorse or promote products derived from
 *    this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
-->
<head>
  <title>Package org.objectweb.asm</title>
</head>
<body>
Provides a small and fast bytecode manipulation framework.

<p>
The <a href="http://asm.ow2.org/">ASM</a> framework is organized
around the {@link org.objectweb.asm.ClassVisitor ClassVisitor},
{@link org.objectweb.asm.FieldVisitor FieldVisitor},
{@link org.objectweb.asm.MethodVisitor MethodVisitor} and
{@link org.objectweb.asm.AnnotationVisitor AnnotationVisitor} abstract classes,
which allow one to visit the fields, methods and annotations of a class,
including the bytecode instructions of each method.

<p>
In addition to these main abstract classes, ASM provides a {@link
org.objectweb.asm.ClassReader ClassReader} class, that can parse an
existing class and make a given visitor visit it. ASM also provides
a {@link org.objectweb.asm.ClassWriter ClassWriter} class, which is
a visitor that generates Java class files.

<p>
In order to generate a class from scratch, only the {@link
org.objectweb.asm.ClassWriter ClassWriter} class is necessary. Indeed,
in order to generate a class, one must just call its visit<em>Xxx</em>
methods with the appropriate arguments to generate the desired fields
and methods.

<p>
In order to modify existing classes, one must use a {@link
org.objectweb.asm.ClassReader ClassReader} class to analyze
the original class, a class modifier, and a {@link org.objectweb.asm.ClassWriter
ClassWriter} to construct the modified class. The class modifier
is just a {@link org.objectweb.asm.ClassVisitor ClassVisitor}
that delegates most of the work to another {@link org.objectweb.asm.ClassVisitor
ClassVisitor}, but that sometimes changes some parameter values,
or call additional methods, in order to implement the desired
modification process. In order to make it easier to implement such
class modifiers, the {@link org.objectweb.asm.ClassVisitor
ClassVisitor} and {@link org.objectweb.asm.MethodVisitor MethodVisitor}
classes delegate by default all the method calls they receive to an
optional visitor.

@since ASM 1.3
</body>
</html>
