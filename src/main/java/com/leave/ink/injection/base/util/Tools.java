package com.leave.ink.injection.base.util;

import com.leave.ink.injection.base.util.asm.ClassReader;
import com.leave.ink.injection.base.util.asm.ClassWriter;
import com.leave.ink.injection.base.util.asm.tree.*;
import org.apache.commons.compress.utils.IOUtils;

import javax.annotation.Nullable;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.leave.ink.injection.base.util.asm.ClassWriter.COMPUTE_FRAMES;
import static com.leave.ink.injection.base.util.asm.ClassWriter.COMPUTE_MAXS;

public class Tools {
    private static final Map<String, Map<String, MethodNode>> METHOD_CACHE = new ConcurrentHashMap<>();

    public static byte[] getClassBytes(Class<?> c) throws IOException {
        String className = c.getName();
        String classAsPath = className.replace('.', '/') + ".class";

        try (InputStream stream = c.getClassLoader().getResourceAsStream(classAsPath)) {
            return stream != null ? IOUtils.toByteArray(stream) : null;
        }
    }

    public static Object getAndInvokeMethod(Object instance, String name, Class<?>[] desc, Object... args) {
        Class<?> c = instance.getClass();
        while (c.getSuperclass() != null) {
            try {
                Method method = c.getDeclaredMethod(name, desc);
                method.setAccessible(true);
                return method.invoke(instance, args);
            } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException ignored) {
            }

            c = c.getSuperclass();
        }
        return null;
    }

    public static boolean setFieldValue(Object instance, Object value, String... name) {
        Class<?> c = instance.getClass();
        for (String s : name) {

            while (c.getSuperclass() != null) {

                try {
                    Field field = c.getDeclaredField(s);
                    // System.out.println(field.get(instance) + " 123123");
                    field.setAccessible(true);
                    field.set(instance, value);
                    return true;
                } catch (IllegalAccessException | NoSuchFieldException ignored) {
                }
                c = c.getSuperclass();
            }
        }


        return false;
    }

    public static Object getFieldValue(Object instance, String... name) {
        Class<?> c = instance.getClass();
        for (String s : name) {

            while (c.getSuperclass() != null) {

                try {
                    Field field = c.getField(s);

                    // System.out.println(field.get(instance) + " 123123");
                    field.setAccessible(true);
                    return field.get(instance);
                } catch (IllegalAccessException | NoSuchFieldException ignored) {
                }
                c = c.getSuperclass();
            }
        }


        return null;
    }

    public static Field getField(Object instance, String... name) {
        Class<?> c = instance.getClass();
        for (String s : name) {

            while (c.getSuperclass() != null) {

                try {
                    Field field = c.getField(s);

                    // System.out.println(field.get(instance) + " 123123");
                    field.setAccessible(true);
                    return field;
                } catch (NoSuchFieldException ignored) {
                }
                c = c.getSuperclass();
            }
        }


        return null;
    }

    public static Object getDeclaredFieldValue(Object instance, String... name) {
        Class<?> c = instance.getClass();
        for (String s : name) {
            while (c.getSuperclass() != null) {

                try {
                    Field field = c.getDeclaredField(s);

                    // System.out.println(field.get(instance) + " 123123");
                    field.setAccessible(true);
                    return field.get(instance);
                } catch (IllegalAccessException | NoSuchFieldException ignored) {
                }
                c = c.getSuperclass();
            }
        }


        return null;
    }


    public static String getMethodDescriptor(Class<?> returnType, Class<?>... params) {
        StringBuilder descriptorBuilder = new StringBuilder("(");
        if (params != null) {
            for (Class<?> param : params) {
                appendTypeDescriptor(descriptorBuilder, param);
            }
        }

        descriptorBuilder.append(")");
        appendTypeDescriptor(descriptorBuilder, returnType);
        return descriptorBuilder.toString();
    }

    public static void appendTypeDescriptor(StringBuilder descriptorBuilder, Class<?> type) {
        if (type == void.class) {
            descriptorBuilder.append("V");
        } else if (type.isPrimitive()) {
            if (type == byte.class) {
                descriptorBuilder.append("B");
            } else if (type == char.class) {
                descriptorBuilder.append("C");
            } else if (type == double.class) {
                descriptorBuilder.append("D");
            } else if (type == float.class) {
                descriptorBuilder.append("F");
            } else if (type == int.class) {
                descriptorBuilder.append("I");
            } else if (type == long.class) {
                descriptorBuilder.append("J");
            } else if (type == short.class) {
                descriptorBuilder.append("S");
            } else if (type == boolean.class) {
                descriptorBuilder.append("Z");
            }
        } else if (type.isArray()) {
            descriptorBuilder.append("[");
            appendTypeDescriptor(descriptorBuilder, type.getComponentType());
        } else {
            descriptorBuilder.append("L").append(type.getName().replace('.', '/')).append(";");
        }
    }

    public static Method tryGetMethod(Class<?> clazz, String name, String obfName, Class<?>... classes) {
        Method method = null;
        try {
            method = clazz.getDeclaredMethod(name, classes);
        } catch (NoSuchMethodException e) {
            try {
                method = clazz.getDeclaredMethod(obfName, classes);
            } catch (NoSuchMethodException ignored) {
            }
        }
        return method;
    }

    public static Method tryGetMethod2(Class<?> clazz, String name, String obfName, Class<?>... classes) {
        Method method = null;
        try {
            method = clazz.getMethod(name, classes);
        } catch (NoSuchMethodException e) {
            try {
                method = clazz.getMethod(obfName, classes);
            } catch (NoSuchMethodException ignored) {
            }
        }
        return method;
    }

    public static Object invokeMethod(Object instance, Method method, @Nullable Object... values) {
        try {
            method.setAccessible(true);
            return method.invoke(instance, values);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static MethodNode getMethod(ClassNode cn, String desc, String name, String obfName) {
        for (MethodNode mn : cn.methods) {
            if ((mn.name.equals(name) || mn.name.equals(obfName)) && mn.desc.equals(desc))
                return mn;
        }
        return null;
    }

    public static MethodNode getMethod(ClassNode cn, String desc, String name) {
        Map<String, MethodNode> index = METHOD_CACHE.get(cn.name);
        return index != null ? index.get(name + desc) : null;
    }

    public static MethodNode getMethod(ClassNode cn, String name) {
        for (MethodNode mn : cn.methods) {
            if ((mn.name.equals(name)))
                return mn;
        }
        return null;
    }

    public static byte[] rewriteClass(ClassNode node) {
        ClassWriter writer = new ClassWriter(COMPUTE_MAXS | COMPUTE_FRAMES);
        node.accept(writer);
        return writer.toByteArray();
    }

    public static ClassNode getClassNode(byte[] bytes) throws IllegalStateException {
        if (bytes == null || bytes.length < 10) {
            throw new IllegalStateException("Invalid class bytes");
        }

        if (!isValidClassFile(bytes)) {
            throw new IllegalStateException("Invalid class file magic bytes");
        }

        try {
            ClassReader reader = new ClassReader(bytes);
            ClassNode node = new ClassNode();
            reader.accept(node, ClassReader.SKIP_FRAMES | ClassReader.SKIP_DEBUG);

            if (node.name == null || node.name.isEmpty()) {
                throw new IllegalStateException("Class name is Null/Empty");
            }

            buildMethodIndex(node);
            return node;
        } catch (Exception e) {
            throw new IllegalStateException("Failed to parse class file", e);
        }
    }

    private static boolean isValidClassFile(byte[] bytes) {
        return bytes[0] == (byte)0xCA && bytes[1] == (byte)0xFE &&
                bytes[2] == (byte)0xBA && bytes[3] == (byte)0xBE;
    }

    private static void buildMethodIndex(ClassNode cn) {
        Map<String, MethodNode> index = new HashMap<>();
        for (MethodNode mn : cn.methods) {
            index.put(mn.name + mn.desc, mn);
        }

        METHOD_CACHE.put(cn.name, index);
    }

    public static AbstractInsnNode getPoint(MethodNode methodNode, int index, String desc, String... funcName) {
        int i = 0;
        for (AbstractInsnNode instruction : methodNode.instructions) {
            if (!(instruction instanceof MethodInsnNode))
                continue;

            MethodInsnNode min = (MethodInsnNode) instruction;

            for (String name : funcName) {
                if (min.name.equals(name)) {
                    ++i;
                    if (i == index && (desc.isEmpty() || min.desc.equals(desc)))
                        return min.getPrevious();
                }

            }
        }
        return null;
    }

    public static AbstractInsnNode getPoint(MethodNode methodNode, String str) {
        int i = 0;
        for (AbstractInsnNode instruction : methodNode.instructions) {
            if (instruction instanceof LdcInsnNode) {
                LdcInsnNode ldcInsnNode = (LdcInsnNode) instruction;
                if (ldcInsnNode.cst instanceof String && ldcInsnNode.cst.equals(str)) {
                    return instruction;
                }
            }

        }
        return null;
    }

    public static AbstractInsnNode getPoint(MethodNode methodNode, String... funcName) {
        for (AbstractInsnNode instruction : methodNode.instructions) {
            if (!(instruction instanceof MethodInsnNode))
                continue;
            MethodInsnNode min = (MethodInsnNode) instruction;
            for (String name : funcName) {
                if (min.name.equals(name))
                    return min.getPrevious();
            }
        }
        return null;
    }

    public static int castToInteger(Object object) {
        return Integer.parseInt(String.valueOf(object));
    }

    public static float castToFloat(Object object) {
        return Float.parseFloat(String.valueOf(object));
    }

    public static double castToDouble(Object object) {
        return Double.parseDouble(String.valueOf(object));
    }

    public static boolean castToBoolean(Object object) {
        return Boolean.parseBoolean(String.valueOf(object));
    }

    public static long castToLong(Object object) {
        return Long.parseLong(String.valueOf(object));
    }

    public static String castToString(Object object) {
        return String.valueOf(object);
    }


}
