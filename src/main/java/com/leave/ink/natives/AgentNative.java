package com.leave.ink.natives;

import sun.misc.Unsafe;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class AgentNative extends Thread {
    public static String CoreLib = "C:\\Leave\\lib\\Core.dll";

    static {
        System.load(CoreLib);
    }

    public byte[][] classes;
    public static Class<?> mainClazz = null;
    public ClassLoader classLoader;

    public static void run(byte[][] classes, ClassLoader classLoader) {
        AgentNative agentNative = new AgentNative();
        agentNative.classLoader = classLoader;
        agentNative.classes = classes;
        agentNative.start();
    }
    public static native String get(String ver);
    @Override
    public void run() {
        try {
            Class<?> unsafeClass = Class.forName("sun.misc.Unsafe");
            Field field = unsafeClass.getDeclaredField("theUnsafe");
            field.setAccessible(true);
            Unsafe unsafe = (Unsafe) field.get(null);
            Module baseModule = Object.class.getModule();
            Class<?> currentClass = AgentNative.class;
            long addr = unsafe.objectFieldOffset(Class.class.getDeclaredField("module"));
            unsafe.getAndSetObject(currentClass, addr, baseModule);
            this.setContextClassLoader(classLoader);

        } catch (Exception e) {
            e.printStackTrace();
        }
        log("[Loader] Thread Run");
        System.load(CoreLib);
        for (byte[] classByte : classes) {
            Class<?> clazz = defineClass(classByte);

            if (clazz == null) {
                log("[Loader] skipped 1 class");
//                System.out.println("skipped 1 class");
                continue;
            }

//            log(clazz.getName());
            if (clazz.getName().contains("com.leave.ink.core.Entry"))
                mainClazz = clazz;
        }
        if (mainClazz == null) {
            log("[Loader] main class null");
//            System.out.println("main class null");
            return;
        }
        try {
            log("[Loader] tryInvoke");
            Method method = mainClazz.getDeclaredMethod("Attach");
            method.invoke(null);
        } catch (Exception e) {
            log("[Loader] " + e.getMessage());
            e.printStackTrace();
        }
    }
    public static native void log(String string);

    public static native Class<?> defineClass(byte[] clazz);

    public static byte[][] getByteArray(int size) {
        return new byte[size][];
    }

    public static native boolean startup(boolean debug);

    public static native int RedefineClass(Class<?> clazz, byte[] bytes);

    public static native byte[] getClassBytes(Class<?> clazz);

    public static native Class<?>[] getAllLoadedClasses();

    /*IRC*/
    public static native boolean init_websocket_connection(String host);

    public static native void close_websocket_connection();

    public static native boolean websocket_send(String message);

    public static native boolean websocket_isConnectionClosed();

    public static void JNI_OnMessage(String message) {


    }
}
