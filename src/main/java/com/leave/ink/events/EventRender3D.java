package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.mojang.blaze3d.vertex.PoseStack;

import net.minecraft.client.Camera;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.LightTexture;
import org.joml.Matrix4f;

public class EventRender3D extends EventCancellable {
    private PoseStack poseStack;
    private Matrix4f matrix4f;
    private Camera camera;
    private GameRenderer gameRenderer;
    private LightTexture lightTexture;
    private float partialTicks;
    private GuiGraphics guiGraphics;
    public EventRender3D(GuiGraphics guiGraphics,PoseStack poseStack, Matrix4f matrix4f, Camera camera, GameRenderer gameRenderer, LightTexture lightTexture, float partialTicks) {
        this.poseStack = poseStack;
        this.guiGraphics =guiGraphics;
        this.matrix4f = matrix4f;
        this.camera = camera;
        this.gameRenderer = gameRenderer;
        this.lightTexture = lightTexture;
        this.partialTicks = partialTicks;
    }

    public GuiGraphics getGuiGraphics() {
        return guiGraphics;
    }

    public PoseStack getPoseStack() {
        return poseStack;
    }

    public void setScaledResolution(PoseStack poseStack) {
        this.poseStack = poseStack;
    }

    public Matrix4f getMatrix4f() {
        return matrix4f;
    }

    public void setMatrix4f(Matrix4f matrix3f) {
        this.matrix4f = matrix3f;
    }

    public Camera getCamera() {
        return camera;
    }

    public void setCamera(Camera camera) {
        this.camera = camera;
    }

    public GameRenderer getGameRenderer() {
        return gameRenderer;
    }

    public void setGameRenderer(GameRenderer gameRenderer) {
        this.gameRenderer = gameRenderer;
    }

    public LightTexture getLightTexture() {
        return lightTexture;
    }

    public void setLightTexture(LightTexture lightTexture) {
        this.lightTexture = lightTexture;
    }

    public float getPartialTicks() {
        return partialTicks;
    }

    public void setPartialTicks(float partialTicks) {
        this.partialTicks = partialTicks;
    }
}
