package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import net.minecraft.client.player.Input;

public class EventMoveInput extends EventCancellable {
    private float forward;
    private float strafe;
    private double sneakSlowDownMultiplier;
    private Input input;

    public EventMoveInput(final float forward, final float strafe, double sneakSlowDownMultiplier, Input input) {
        this.forward = forward;
        this.strafe = strafe;
        this.sneakSlowDownMultiplier = sneakSlowDownMultiplier;
        this.input = input;
    }

    public Input getInput() {
        return input;
    }
    public void setInput(Input input) {
        this.input = input;
    }
    public double getSneakSlowDownMultiplier() {
        return this.sneakSlowDownMultiplier;
    }
    public void setSneakSlowDownMultiplier(double sneakSlowDownMultiplier) {
        this.sneakSlowDownMultiplier = sneakSlowDownMultiplier;
    }
    public float getForward() {
        return this.forward;
    }
    public float getStrafe() {
        return this.strafe;
    }
    public void setForward(final float forward) {
        this.forward = forward;
    }
    public void setStrafe(final float strafe) {
        this.strafe = strafe;
    }
}
