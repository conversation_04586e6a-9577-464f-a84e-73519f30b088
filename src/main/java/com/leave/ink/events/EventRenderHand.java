package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.item.ItemStack;

public class EventRenderHand extends EventCancellable {
    //InteractionHand hand, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, float partialTick, float interpPitch, float swingProgress, float equipProgress, ItemStack stack
    private InteractionHand hand;
    private PoseStack poseStack;
    private MultiBufferSource.BufferSource bufferSource;
    private int packedLight;
    private float partialTick, interpPitch, equipProgress, swingProgress;
    private ItemStack stack;

    public EventRenderHand(InteractionHand hand, PoseStack poseStack, MultiBufferSource.BufferSource bufferSource, int packedLight, float partialTick, float interpPitch, float equipProgress, float swingProgress, ItemStack stack) {
        this.hand = hand;
        this.poseStack = poseStack;
        this.bufferSource = bufferSource;
        this.packedLight = packedLight;
        this.partialTick = partialTick;
        this.interpPitch = interpPitch;
        this.equipProgress = equipProgress;
        this.swingProgress = swingProgress;
        this.stack = stack;
    }

    public InteractionHand getHand() {
        return hand;
    }

    public PoseStack getPoseStack() {
        return poseStack;
    }

    public MultiBufferSource.BufferSource getBufferSource() {
        return bufferSource;
    }

    public int getPackedLight() {
        return packedLight;
    }

    public float getPartialTick() {
        return partialTick;
    }

    public float getInterpPitch() {
        return interpPitch;
    }

    public float getEquipProgress() {
        return equipProgress;
    }

    public float getSwingProgress() {
        return swingProgress;
    }

    public ItemStack getStack() {
        return stack;
    }
}
