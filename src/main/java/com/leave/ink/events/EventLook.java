package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;

public class EventLook extends EventCancellable {
    public float yaw, pitch;

    public EventLook(float yaw, float pitch) {
        this.yaw = yaw;
        this.pitch = pitch;
    }

    public float getYaw() {
        return yaw;
    }

    public void setYaw(float yaw) {
        this.yaw = yaw;
    }

    public float getPitch() {
        return pitch;
    }

    public void setPitch(float pitch) {
        this.pitch = pitch;
    }
}
