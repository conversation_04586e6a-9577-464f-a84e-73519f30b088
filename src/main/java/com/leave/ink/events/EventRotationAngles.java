package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.world.entity.LivingEntity;

public class EventRotationAngles extends EventCancellable {
    private HumanoidModel<?> humanoidModel;
    private float limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch;
    private LivingEntity entityIn;

    public EventRotationAngles(HumanoidModel<?> humanoidModel, float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch, LivingEntity entityIn) {
        this.humanoidModel = humanoidModel;
        this.limbSwing = limbSwing;
        this.limbSwingAmount = limbSwingAmount;
        this.ageInTicks = ageInTicks;
        this.netHeadYaw = netHeadYaw;
        this.headPitch = headPitch;
        this.entityIn = entityIn;
    }

    public HumanoidModel<?> getHumanoidModel() {
        return humanoidModel;
    }

    public void setHumanoidModel(HumanoidModel<?> humanoidModel) {
        this.humanoidModel = humanoidModel;
    }

    public float getLimbSwing() {
        return limbSwing;
    }

    public void setLimbSwing(float limbSwing) {
        this.limbSwing = limbSwing;
    }

    public float getLimbSwingAmount() {
        return limbSwingAmount;
    }

    public void setLimbSwingAmount(float limbSwingAmount) {
        this.limbSwingAmount = limbSwingAmount;
    }

    public float getAgeInTicks() {
        return ageInTicks;
    }

    public void setAgeInTicks(float ageInTicks) {
        this.ageInTicks = ageInTicks;
    }

    public float getNetHeadYaw() {
        return netHeadYaw;
    }

    public void setNetHeadYaw(float netHeadYaw) {
        this.netHeadYaw = netHeadYaw;
    }

    public float getHeadPitch() {
        return headPitch;
    }

    public void setHeadPitch(float headPitch) {
        this.headPitch = headPitch;
    }

    public LivingEntity getEntityIn() {
        return entityIn;
    }

    public void setEntityIn(LivingEntity entityIn) {
        this.entityIn = entityIn;
    }
}
