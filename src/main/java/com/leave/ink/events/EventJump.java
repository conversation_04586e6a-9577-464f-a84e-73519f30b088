package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;

public class EventJump extends EventCancellable {
    private double motionY;
    private float yaw;

    public EventJump(double motionY, float yaw) {
        this.motionY = motionY;
        this.yaw = yaw;
    }

    public double getMotionY() {
        return motionY;
    }

    public void setMotionY(float motionY) {
        this.motionY = motionY;
    }

    public float getYaw() {
        return yaw;
    }

    public void setYaw(float yaw) {
        this.yaw = yaw;
    }
}
