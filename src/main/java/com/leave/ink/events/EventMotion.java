package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class EventMotion extends EventCancellable {
    private double x, y, z;
    private float yaw, pitch;
    private boolean onGround;
    private EventType eventType;

    public EventMotion(EventType eventType) {
        this.eventType = eventType;
    }
}
