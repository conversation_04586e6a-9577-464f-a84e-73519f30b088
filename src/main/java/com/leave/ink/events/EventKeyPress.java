package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;

public class EventKeyPress extends EventCancellable {
    private final int key, scancode, action;

    public EventKeyPress(int key, int scancode, int action) {
        this.key = key;
        this.scancode = scancode;
        this.action = action;
    }

    public int getAction() {
        return action;
    }

    public int getKey() {
        return key;
    }

    public int getScancode() {
        return scancode;
    }
}
