package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import net.minecraft.client.model.PlayerModel;

public class EventAnimationSetup extends EventCancellable {
    private PlayerModel<?> playerModel;

    public EventAnimationSetup(PlayerModel<?> playerModel) {
        this.playerModel = playerModel;

    }

    public PlayerModel<?> getPlayerModel() {
        return playerModel;
    }

    public void setPlayerModel(PlayerModel<?> playerModel) {
        this.playerModel = playerModel;
    }
}

