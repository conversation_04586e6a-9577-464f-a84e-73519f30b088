package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.leave.ink.injection.transformers.entity.LivingEntityTransformer;
import net.minecraft.world.phys.Vec3;

/**
 * @see LivingEntityTransformer
 */
public class EventMoveMath extends EventCancellable {
    private final Vec3 vec3;

    public EventMoveMath(Vec3 vec3) {
        this.vec3 = vec3;
    }

    public Vec3 getVec3() {
        return vec3;
    }
}
