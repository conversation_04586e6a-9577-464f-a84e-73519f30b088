package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import net.minecraft.network.protocol.Packet;

@Setter
@Getter
@AllArgsConstructor
public class EventPacket extends EventCancellable {
    private Packet<?> packet;
    private PacketType packetType;

    public EventPacket(Packet<?> packet) {
        this.packet = packet;
    }

    public enum PacketType {
        Client, Server, POST
    }
}

