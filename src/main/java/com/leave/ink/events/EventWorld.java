package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import net.minecraft.client.multiplayer.ClientLevel;

public class EventWorld extends EventCancellable {
    private final ClientLevel clientLevel;

    public EventWorld(ClientLevel clientLevel) {
        this.clientLevel = clientLevel;
    }

    public ClientLevel getClientLevel() {
        return clientLevel;
    }
}
