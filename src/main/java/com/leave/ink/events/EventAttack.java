package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import lombok.Getter;
import net.minecraft.world.entity.Entity;

public class EventAttack extends EventCancellable {
    private final Entity targetEntity;
    @Getter
    private EventType eventType;
    public EventAttack(Entity targetEntity) {
        this.targetEntity = targetEntity;
    }

    public Entity getTargetEntity() {
        return targetEntity;
    }
}