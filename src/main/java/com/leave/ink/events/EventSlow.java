package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
@AllArgsConstructor
public class EventSlow extends EventCancellable {
    private float amount;
    private boolean isUsingItem;
    private boolean slowDown;
    
    public EventSlow(float amount, boolean isUsingItem) {
        this.amount = amount;
        this.isUsingItem = isUsingItem;
        this.slowDown = isUsingItem; // 默认：如果在使用物品则减速
    }
}
