package com.leave.ink.events.hud;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.leave.ink.ui.skija.CanvasStack;

public class EventSkiaProcess extends EventCancellable {
    private final CanvasStack canvasStack;

    public EventSkiaProcess(CanvasStack canvasStack) {
        this.canvasStack = canvasStack;
    }

    public CanvasStack getCanvasStack() {
        return canvasStack;
    }
}
