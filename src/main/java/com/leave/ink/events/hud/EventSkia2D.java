package com.leave.ink.events.hud;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.utils.render.Projection;
import com.mojang.blaze3d.vertex.PoseStack;

public class EventSkia2D extends EventCancellable {
    private final CanvasStack canvasStack;
    private final Projection projection;
    private final PoseStack poseStack;
    private final float partialTicks;

    public EventSkia2D(CanvasStack canvasStack, Projection projection, PoseStack poseStack, float partialTicks) {
        this.canvasStack = canvasStack;
        this.projection = projection;
        this.poseStack = poseStack;
        this.partialTicks = partialTicks;
    }

    public CanvasStack getCanvasStack() {
        return canvasStack;
    }

    public Projection getProjection() {
        return projection;
    }

    public PoseStack getPoseStack() {
        return poseStack;
    }

    public float getPartialTicks() {
        return partialTicks;
    }
} 