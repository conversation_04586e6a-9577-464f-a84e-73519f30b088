package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.world.entity.Entity;

public class EventEntityJoinWorld extends EventCancellable {
    private final ClientLevel clientLevel;
    private final Entity entity;

    public EventEntityJoinWorld(ClientLevel clientLevel, Entity entity) {
        this.clientLevel = clientLevel;
        this.entity = entity;
    }

    public ClientLevel getClientLevel() {
        return clientLevel;
    }

    public Entity getEntity() {
        return entity;
    }
}
