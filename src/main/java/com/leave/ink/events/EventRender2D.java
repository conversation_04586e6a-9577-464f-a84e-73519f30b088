package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.gui.GuiGraphics;

public class EventRender2D extends EventCancellable {
    private PoseStack poseStack;
    private float partialTicks;
    private int screenHeight;
    private int screenWidth;
    private GuiGraphics guiGraphics;

    public EventRender2D(GuiGraphics guiGraphics,PoseStack poseStack, float partialTicks, int screenHeight, int screenWidth) {
        this.poseStack = poseStack;
        this.guiGraphics = guiGraphics;
        this.partialTicks = partialTicks;
        this.screenHeight = screenHeight;
        this.screenWidth = screenWidth;
    }

    public EventRender2D(GuiGraphics guiGraphics, PoseStack poseStack, int screenHeight, int screenWidth) {
        this.poseStack = poseStack;
        this.guiGraphics = guiGraphics;
        this.screenHeight = screenHeight;
        this.screenWidth = screenWidth;
    }

    public GuiGraphics getGuiGraphics() {
        return guiGraphics;
    }

    public PoseStack getPoseStack() {
        return poseStack;
    }

    public void setScaledResolution(PoseStack poseStack) {
        this.poseStack = poseStack;
    }

    public float getPartialTicks() {
        return partialTicks;
    }

    public void setPartialTicks(float partialTicks) {
        this.partialTicks = partialTicks;
    }

    public int getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(int screenHeight) {
        this.screenHeight = screenHeight;
    }

    public int getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(int screenWidth) {
        this.screenWidth = screenWidth;
    }
}
