package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import com.leave.ink.utils.Utils;
import net.minecraft.network.chat.FormattedText;
import net.minecraft.util.FormattedCharSequence;

public class EventText extends EventCancellable {
    private String text;
    private FormattedText formattedText;
    private FormattedCharSequence formattedCharSequence;
    private float x;
    private int color;
    public EventText(String text) {
        this.text = text;
    }

    public EventText(String text, float x, int color) {
        this.text = text;
        this.x = x;
        this.color = color;
    }

    public EventText(FormattedText formattedText) {
        this.formattedText = formattedText;
    }

    public EventText(FormattedCharSequence formattedCharSequence) {
        this.formattedCharSequence = formattedCharSequence;
    }

    public FormattedText getFormattedText() {
        return formattedText;
    }

    public FormattedCharSequence getFormattedCharSequence() {
        return formattedCharSequence;
    }

    public String getStringFromFormattedCharSequence() {
        return formattedCharSequence != null ? Utils.getStringCached(formattedCharSequence) : null;
    }

    public float getX() {
        return x;
    }

    public int getColor() {
        return color;
    }

    public String getText() {
        return text;
    }

    public void setFormattedText(FormattedText formattedText) {
        this.formattedText = formattedText;
    }

    public void setFormattedCharSequence(FormattedCharSequence formattedCharSequence) {
        this.formattedCharSequence = formattedCharSequence;
    }

    public void setText(String text) {
        this.text = text;
    }
}
