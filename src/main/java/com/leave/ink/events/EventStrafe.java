package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import net.minecraft.world.phys.Vec3;

public class EventStrafe extends EventCancellable {
    private Vec3 velocity;
    private float speed;
    private float yaw;

    public EventStrafe(Vec3 velocity , float speed, float yaw) {
        this.velocity = velocity;
        this.speed = speed;
        this.yaw = yaw;
    }

    public Vec3 getVelocity() {
        return velocity;
    }

    public void setVelocity(Vec3 velocity) {
        this.velocity = velocity;
    }

    public float getSpeed() {
        return speed;
    }

    public void setSpeed(float speed) {
        this.speed = speed;
    }

    public float getYaw() {
        return yaw;
    }

    public void setYaw(float yaw) {
        this.yaw = yaw;
    }
}
