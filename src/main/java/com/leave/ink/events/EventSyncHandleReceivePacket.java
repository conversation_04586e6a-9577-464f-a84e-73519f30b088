package com.leave.ink.events;

import com.darkmagician6.eventapi.events.callables.EventCancellable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientGamePacketListener;

@Getter
@AllArgsConstructor
public class EventSyncHandleReceivePacket extends EventCancellable {
    private Packet<ClientGamePacketListener> packet;
}