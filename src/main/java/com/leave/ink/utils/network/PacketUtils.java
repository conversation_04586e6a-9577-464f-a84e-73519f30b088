package com.leave.ink.utils.network;

import com.leave.ink.utils.client.mapping.Mapping;
import com.leave.ink.utils.wrapper.IMinecraft;
import lombok.experimental.UtilityClass;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.multiplayer.prediction.BlockStatePredictionHandler;
import net.minecraft.client.multiplayer.prediction.PredictiveAction;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

@UtilityClass
public class PacketUtils implements IMinecraft {
    private final ArrayList<Packet<ServerGamePacketListener>> packets = new ArrayList<>();

    private final Set<Class<?>> SERVERBOUND_PACKET_CLASSES = new HashSet<>();
    private final Set<Class<?>> CLIENTBOUND_PACKET_CLASSES = new HashSet<>();

    static {
        // === 添加 Serverbound 包 ===
        SERVERBOUND_PACKET_CLASSES.add(ServerboundAcceptTeleportationPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundBlockEntityTagQuery.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundChangeDifficultyPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundChatAckPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundChatCommandPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundChatPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundChatSessionUpdatePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundClientCommandPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundClientInformationPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundCommandSuggestionPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundContainerButtonClickPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundContainerClickPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundContainerClosePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundCustomPayloadPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundEditBookPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundEntityTagQuery.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundInteractPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundJigsawGeneratePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundKeepAlivePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundLockDifficultyPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundMovePlayerPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundMovePlayerPacket.Pos.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundMovePlayerPacket.StatusOnly.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundMovePlayerPacket.Rot.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundMovePlayerPacket.PosRot.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundMoveVehiclePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPaddleBoatPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPickItemPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPlaceRecipePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPlayerAbilitiesPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPlayerActionPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPlayerCommandPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPlayerInputPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundPongPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundRecipeBookChangeSettingsPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundRecipeBookSeenRecipePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundRenameItemPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundResourcePackPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSeenAdvancementsPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSelectTradePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetBeaconPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetCarriedItemPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetCommandBlockPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetCommandMinecartPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetCreativeModeSlotPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetJigsawBlockPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSetStructureBlockPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSignUpdatePacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundSwingPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundTeleportToEntityPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundUseItemOnPacket.class);
        SERVERBOUND_PACKET_CLASSES.add(ServerboundUseItemPacket.class);

        // === 添加 Clientbound 包 ===
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundAddEntityPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundAddExperienceOrbPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundAddPlayerPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundAnimatePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundAwardStatsPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBlockChangedAckPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBlockDestructionPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBlockEntityDataPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBlockEventPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBlockUpdatePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBossEventPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundBundlePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundChangeDifficultyPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundChunksBiomesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundClearTitlesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundCommandsPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundCommandSuggestionsPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundContainerClosePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundContainerSetContentPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundContainerSetDataPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundContainerSetSlotPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundCooldownPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundCustomChatCompletionsPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundCustomPayloadPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundDamageEventPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundDeleteChatPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundDisconnectPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundDisguisedChatPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundEntityEventPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundExplodePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundForgetLevelChunkPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundGameEventPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundHorseScreenOpenPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundHurtAnimationPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundInitializeBorderPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundKeepAlivePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLevelChunkPacketData.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLevelChunkWithLightPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLevelEventPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLevelParticlesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLightUpdatePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLightUpdatePacketData.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundLoginPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundMapItemDataPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundMerchantOffersPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundMoveEntityPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundMoveVehiclePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundOpenBookPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundOpenScreenPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundOpenSignEditorPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPingPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlaceGhostRecipePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerAbilitiesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerChatPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerCombatEndPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerCombatEnterPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerCombatKillPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerInfoRemovePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerInfoUpdatePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerLookAtPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundPlayerPositionPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundRecipePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundRemoveEntitiesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundRemoveMobEffectPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundResourcePackPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundRespawnPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundRotateHeadPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSectionBlocksUpdatePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSelectAdvancementsTabPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundServerDataPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetActionBarTextPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetBorderCenterPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetBorderLerpSizePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetBorderSizePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetBorderWarningDelayPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetBorderWarningDistancePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetCameraPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetCarriedItemPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetChunkCacheCenterPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetChunkCacheRadiusPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetDefaultSpawnPositionPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetDisplayObjectivePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetEntityDataPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetEntityLinkPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetEntityMotionPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetEquipmentPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetExperiencePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetHealthPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetObjectivePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetPassengersPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetPlayerTeamPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetScorePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetSimulationDistancePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetSubtitleTextPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetTimePacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetTitlesAnimationPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSetTitleTextPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSoundEntityPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSoundPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundStopSoundPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundSystemChatPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundTabListPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundTagQueryPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundTakeItemEntityPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundTeleportEntityPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundUpdateAdvancementsPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundUpdateAttributesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundUpdateEnabledFeaturesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundUpdateMobEffectPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundUpdateRecipesPacket.class);
        CLIENTBOUND_PACKET_CLASSES.add(ClientboundUpdateTagsPacket.class);
    }

    public boolean isServerboundPacket(Packet<?> packet) {
        return SERVERBOUND_PACKET_CLASSES.contains(packet.getClass());
    }

    public boolean isClientboundPacket(Packet<?> packet) {
        return CLIENTBOUND_PACKET_CLASSES.contains(packet.getClass());
    }

    public boolean handleSendPacket(Packet<?> packet) {
        if (packets.contains(packet)) {
            packets.remove(packet);
            return true;
        }
        return false;
    }

    public void sendPacket(Packet<ServerGamePacketListener> packet) {
        if (mc.getConnection() == null) return;

        mc.getConnection().send(packet);
    }

    public void handlePacket(Packet<ClientGamePacketListener> packet) {
        if (mc.getConnection() == null) return;

        packet.handle(mc.getConnection());
    }

    public void sendPacketNoEvent(Packet<ServerGamePacketListener> packet) {
        packets.add(packet);
        sendPacket(packet);
    }

    public void queuePacket(Packet<?> packet) {
        if (isServerboundPacket(packet)) {
            final Packet<ServerGamePacketListener> serverPacket = (Packet<ServerGamePacketListener>) packet;
            sendPacket(serverPacket);
        } else if (isClientboundPacket(packet)) {
            final Packet<ClientGamePacketListener> clientPacket = (Packet<ClientGamePacketListener>) packet;
            handlePacket(clientPacket);
        } else {
        }
    }

    public void queuePacketNoEvent(Packet<?> packet) {
        if (isServerboundPacket(packet)) {
            final Packet<ServerGamePacketListener> serverPacket = (Packet<ServerGamePacketListener>) packet;
            sendPacketNoEvent(serverPacket);
        } else if (isClientboundPacket(packet)) {
            final Packet<ClientGamePacketListener> clientPacket = (Packet<ClientGamePacketListener>) packet;
            handlePacket(clientPacket);
        } else {
        }
    }

    public void sendSequencedPacket(PredictiveAction packetCreator) {
        if (mc.getConnection() == null || mc.level == null) return;

        try {
            String blockStatePredictionHandler = Mapping.get(ClientLevel.class, "blockStatePredictionHandler", null);

            if (blockStatePredictionHandler == null) return;

            Field field = ClientLevel.class.getDeclaredField(blockStatePredictionHandler);
            field.setAccessible(true);
            BlockStatePredictionHandler handler = (BlockStatePredictionHandler) field.get(mc.level);

            try (BlockStatePredictionHandler pendingUpdateManager = handler.startPredicting()) {
                int i = pendingUpdateManager.currentSequence();
                mc.getConnection().send(packetCreator.predict(i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void sendSequencedPacketNoEvent(PredictiveAction packetCreator) {
        if (mc.getConnection() == null || mc.level == null) return;

        try {
            String blockStatePredictionHandler = Mapping.get(ClientLevel.class, "blockStatePredictionHandler", null);

            if (blockStatePredictionHandler == null) return;

            Field field = ClientLevel.class.getDeclaredField(blockStatePredictionHandler);
            field.setAccessible(true);
            BlockStatePredictionHandler handler = (BlockStatePredictionHandler) field.get(mc.level);

            try (BlockStatePredictionHandler pendingUpdateManager = handler.startPredicting()) {
                int i = pendingUpdateManager.currentSequence();
                packets.add(packetCreator.predict(i));
                mc.getConnection().send(packetCreator.predict(i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
