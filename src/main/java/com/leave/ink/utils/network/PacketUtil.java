package com.leave.ink.utils.network;

import com.leave.ink.utils.client.mapping.Mapping;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.multiplayer.prediction.BlockStatePredictionHandler;
import net.minecraft.client.multiplayer.prediction.PredictiveAction;
import net.minecraft.network.protocol.Packet;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class PacketUtil implements IMinecraft {
    private static final List<Packet<?>> packets = new ArrayList<>();

    public static boolean handleSendPacket(Packet<?> packet) {
        if (packets.contains(packet)) {
            packets.remove(packet);
            return true;
        }
        return false;
    }

    public static void sendSequencedPacketNoEvent(PredictiveAction packetCreator) {
        if (mc.getConnection() == null || mc.level == null) return;

        try {
            String blockStatePredictionHandler = Mapping.get(ClientLevel.class, "blockStatePredictionHandler", null);

            if (blockStatePredictionHandler == null) return;

            Field field = ClientLevel.class.getDeclaredField(blockStatePredictionHandler);
            field.setAccessible(true);
            BlockStatePredictionHandler handler = (BlockStatePredictionHandler) field.get(mc.level);

            try (BlockStatePredictionHandler pendingUpdateManager = handler.startPredicting()) {
                int i = pendingUpdateManager.currentSequence();
                packets.add(packetCreator.predict(i));
                mc.getConnection().send(packetCreator.predict(i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void sendPacketNoEvent(Packet packet) {
        packets.add(packet);
        if (isClientPacket(packet)) {
            mc.getConnection().send(packet);
        }
        if (isServerPacket(packet)) {
            packet.handle(mc.getConnection().getConnection().getPacketListener());
        }
    }

    public static boolean isClientPacket(Packet<?> packet) {
        return packet.getClass().getName().contains("Serverbound");
    }

    public static boolean isServerPacket(Packet<?> packet) {
        return packet.getClass().getName().contains("Clientbound");
    }
}
