package com.leave.ink.utils.pathfinder;

import com.leave.ink.utils.pathfinder.path.Cell;
import com.leave.ink.utils.pathfinder.path.IWorldProvider;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.FenceBlock;
import net.minecraft.world.level.block.WallBlock;
import net.minecraft.world.level.block.state.BlockState;

public class MinecraftWorldProvider implements IWorldProvider {

    private final ClientLevel world;

    public MinecraftWorldProvider(ClientLevel world) {
        this.world = world;
    }

    @Override
    public boolean isBlocked(Cell cell) {
        return isBlocked(cell.x, cell.y, cell.z);
    }

    public boolean isBlocked(int x, int y, int z) {
        return isSolid(x, y, z) || isSolid(x, y + 1, z) || unableToStand(x, y - 1, z);
    }

    private boolean isSolid(int x, int y, int z) {
        BlockState block = world.getBlockState(new BlockPos(x, y, z));

        return block.isSolid();
    }

    private boolean unableToStand(int x, int y, int z) {
        final Block block = world.getBlockState(new BlockPos(x, y, z)).getBlock();
        return block instanceof FenceBlock || block instanceof WallBlock;
    }
}