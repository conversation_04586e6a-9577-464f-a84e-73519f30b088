package com.leave.ink.utils.auth;

import YeQing.ClassObfuscator;
import com.external.ui.ExternalUI;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.client.ChatUtils;
import com.leave.ink.utils.wrapper.IMinecraft;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@ClassObfuscator
public class HeartbeatThread implements IMinecraft {
    public static int errorCount = 0;

    public static void sendHeartbeat(String gameId) {
        if (errorCount >= 5) {
            return;
        }
        String res = ExternalUI.sendHeartbeat0(gameId);


        if (!res.equals("正常返回")) {
            ++errorCount;

            if (res.equals("认证错误")) {

            }
            ExternalUI.log("Heartbeat Error " + res);
            ChatUtils.displayAlert("Heartbeat Error " + res);
            System.out.println("Heartbeat Error " + res);
        }
    }

    public static void startThread(int second) {

        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleAtFixedRate(new Thread(() -> {
            String id = Utils.isNull() ? "" : mc.player.getName().getString();
            sendHeartbeat(id);

        }), 0, second, TimeUnit.SECONDS);
    }
}
