package com.leave.ink.utils.player;

import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.rotation.Rotation;
import com.mojang.blaze3d.platform.InputConstants;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import org.joml.Vector3d;
import net.minecraft.client.KeyMapping;

public class PlayerUtils implements IMinecraft {
    public static double getMaxFallDistance() {
        BlockPos playerPos = mc.player.blockPosition();
        for (int y = playerPos.getY(); y > mc.level.getMinBuildHeight(); y--) {
            BlockPos pos = new BlockPos(playerPos.getX(), y, playerPos.getZ());
            if (!mc.level.getBlockState(pos).isAir()) {
                return playerPos.getY() - y;
            }
        }
        return 0;
    }

    public static void sendClick(final int button, final boolean state) {
        final InputConstants.Key keyBind = button == 0 ? mc.options.keyAttack.getKey() : mc.options.keyUse.getKey();

        KeyMapping.set(keyBind, state);

        if (state) {
            KeyMapping.click(keyBind);
        }
    }

    public static int getTotalArmorValue(LivingEntity entity) {
        int totalArmor = 0;

        for (ItemStack armorItem : entity.getArmorSlots()) {
            if (armorItem.getItem() instanceof ArmorItem armor) {
                totalArmor += armor.getDefense();
            }
        }

        return totalArmor;
    }

    public static Block blockRelativeToPlayer(final double offsetX, final double offsetY, final double offsetZ) {
        if (mc.level == null || mc.player == null) return null;

        BlockPos playerPos = mc.player.blockPosition();
        BlockPos offsetPos = playerPos.offset((int)offsetX, (int)offsetY, (int)offsetZ);

        return mc.level.getBlockState(offsetPos).getBlock();
    }

    public static Block getBlock(BlockPos pos) {
        if (mc.level == null || mc.player == null) return null;

        return mc.level.getBlockState(pos).getBlock();
    }

    public static Block block(final double x, final double y, final double z) {
        if (mc.level == null || mc.player == null) return null;

        return mc.level.getBlockState(BlockPos.containing(x, y, z)).getBlock();
    }

    public static Block block(final Vector3d pos) {
        return block(pos.x, pos.y, pos.z);
    }

    public static BlockHitResult collisionRayTrace(final Level world, final BlockPos pos, Vec3 start, Vec3 end) {
        ClipContext context = new ClipContext(
                start,
                end,
                ClipContext.Block.OUTLINE,
                ClipContext.Fluid.NONE,
                null);

        BlockHitResult result = world.clip(context);
        if (result.getType() == HitResult.Type.BLOCK && result.getBlockPos().equals(pos)) {
            return result; // Return the hit result if it hit the block at the given position
        }

        return null; // No hit or didn't hit the specific block
    }

    public static boolean equalsVector(BlockPos blockPos, Vector3d vec) {
        return (Math.floor(vec.x) == blockPos.getX() &&
                Math.floor(vec.y) == blockPos.getY() &&
                Math.floor(vec.z) == blockPos.getZ());
    }

    public static Block getBlockUnderPlayer(final Player player) {
        return getBlock(BlockPos.containing(player.getX(), player.getY() - 1.0, player.getZ()));
    }

    public static double getDistance(double x, double y, double z) {
        if (mc.player == null) return 0.0d;

        double d0 = mc.player.getX() - x;
        double d1 = mc.player.getY() - y;
        double d2 = mc.player.getZ() - z;
        return Mth.sqrt((float) (d0 * d0 + d1 * d1 + d2 * d2));
    }

    public static HitResult pickCustom(double blockReachDistance, float yaw, float pitch) {
        if (mc.player == null || mc.level == null) return null;

        Vec3 vec3 = mc.player.getEyePosition(1.0F);
        Vec3 vec31 = getVectorForRotation(new Rotation(yaw, pitch));
        Vec3 vec32 = vec3.add(vec31.x * blockReachDistance, vec31.y * blockReachDistance, vec31.z * blockReachDistance);
        return mc.level.clip(new ClipContext(vec3, vec32, ClipContext.Block.OUTLINE, ClipContext.Fluid.NONE, mc.player));
    }

    public static Vec3 getVectorForRotation(final Rotation rotation) {
        float yawCos = (float) Math.cos(-rotation.getYaw() * 0.017453292F - 3.1415927F);
        float yawSin = (float) Math.sin(-rotation.getYaw() * 0.017453292F - 3.1415927F);
        float pitchCos = (float) -Math.cos(-rotation.getPitch() * 0.017453292F);
        float pitchSin = (float) Math.sin(-rotation.getPitch() * 0.017453292F);
        return new Vec3(yawSin * pitchCos, pitchSin, yawCos * pitchCos);
    }

    public static float getBlockHardness(final Level worldIn, final BlockPos pos) {
        BlockState blockState = worldIn.getBlockState(pos);
        return blockState.getDestroySpeed(worldIn, pos);
    }

    public static boolean isHoldingGodAxe(Player player) {

        return isGodAxe(player.getMainHandItem()) || isGodAxe(player.getOffhandItem());
    }

    private static boolean isGodAxe(ItemStack stack) {
        if (stack.isEmpty() || !stack.is(Items.GOLDEN_AXE)) return false;
        int durability = stack.getMaxDamage() - stack.getDamageValue();
        int sharpnessLevel = getEnchantmentLevel(stack);
        return durability <= 2 && sharpnessLevel > 20;
    }

    private static int getEnchantmentLevel(ItemStack stack) {
        ListTag enchantmentTagList = stack.getEnchantmentTags();
        for (int i = 0; i < enchantmentTagList.size(); i++) {
            CompoundTag nbt = enchantmentTagList.getCompound(i);
            if (nbt.contains("id") && nbt.contains("lvl") &&
                    nbt.getString("id").equals("minecraft:sharpness")) {
                return nbt.getInt("lvl");
            }
        }
        return 0;
    }

    public static boolean inLiquid() {
        return mc.player.isInWaterOrBubble() || mc.player.isInLava();
    }
}