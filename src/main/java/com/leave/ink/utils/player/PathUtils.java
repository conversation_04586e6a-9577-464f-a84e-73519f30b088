package com.leave.ink.utils.player;

import com.leave.ink.utils.pathfinder.MinecraftWorldProvider;
import com.leave.ink.utils.pathfinder.path.Cell;
import com.leave.ink.utils.pathfinder.path.Pathfinder;
import com.leave.ink.utils.rotation.vector.Vector3d;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.util.Mth;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.List;

public final class PathUtils implements IMinecraft {

    public static List<Vec3> findBlinkPath(final double tpX, final double tpY, final double tpZ) {
        return findBlinkPath(tpX, tpY, tpZ, 5);
    }

    public static List<Vec3> findBlinkPath(final double tpX, final double tpY, final double tpZ, final double dist) {
        return findBlinkPath(mc.player.getX(), mc.player.getY(), mc.player.getZ(), tpX, tpY, tpZ, dist);
    }

    public static List<Vec3> findBlinkPath(double curX, double curY, double curZ, final double tpX, final double tpY, final double tpZ, final double dashDistance) {
        final MinecraftWorldProvider worldProvider = new MinecraftWorldProvider(mc.level);
        final Pathfinder pathfinder = new Pathfinder(new Cell((int) curX, (int) curY, (int) curZ), new Cell((int) tpX, (int) tpY, (int) tpZ),
                Pathfinder.COMMON_NEIGHBORS, worldProvider);

        return simplifyPath(pathfinder.findPath(3000), dashDistance, worldProvider);
    }

    public static ArrayList<Vec3> simplifyPath(final ArrayList<Cell> path, final double dashDistance, final MinecraftWorldProvider worldProvider) {
        final ArrayList<Vec3> finalPath = new ArrayList<>();

        Cell cell = path.get(0);
        Vec3 vec3;
        Vec3 lastLoc = new Vec3(cell.x + 0.5, cell.y, cell.z + 0.5);
        Vec3 lastDashLoc = lastLoc;
        for (int i = 1; i < path.size() - 1; i++) {
            cell = path.get(i);
            vec3 = new Vec3(cell.x + 0.5, cell.y, cell.z + 0.5);
            boolean canContinue = true;
            if (vec3.distanceToSqr(lastDashLoc) > dashDistance * dashDistance) {
                canContinue = false;
            } else {
                double smallX = Math.min(lastDashLoc.x, vec3.x);
                double smallY = Math.min(lastDashLoc.y, vec3.y);
                double smallZ = Math.min(lastDashLoc.z, vec3.z);
                double bigX = Math.max(lastDashLoc.x, vec3.x);
                double bigY = Math.max(lastDashLoc.y, vec3.y);
                double bigZ = Math.max(lastDashLoc.z, vec3.z);
                cordsLoop:
                for (int x = (int) smallX; x <= bigX; x++) {
                    for (int y = (int) smallY; y <= bigY; y++) {
                        for (int z = (int) smallZ; z <= bigZ; z++) {
                            if (worldProvider.isBlocked(x, y, z)) {
                                canContinue = false;
                                break cordsLoop;
                            }
                        }
                    }
                }
            }
            if (!canContinue) {
                finalPath.add(lastLoc);
                lastDashLoc = lastLoc;
            }
            lastLoc = vec3;
        }

        return finalPath;
    }

    public static List<Vector3d> findPath(final double tpX, final double tpY, final double tpZ, final double offset) {
        final List<Vector3d> positions = new ArrayList<>();
        final double steps = Math.ceil(getDistance(mc.player.getX(), mc.player.getY(), mc.player.getZ(), tpX, tpY, tpZ) / offset);

        final double dX = tpX - mc.player.getX();
        final double dY = tpY - mc.player.getY();
        final double dZ = tpZ - mc.player.getZ();

        for (double d = 1D; d <= steps; ++d) {
            positions.add(new Vector3d(mc.player.getX() + (dX * d) / steps, mc.player.getY() + (dY * d) / steps, mc.player.getZ() + (dZ * d) / steps));
        }

        return positions;
    }

    private static double getDistance(final double x1, final double y1, final double z1, final double x2, final double y2, final double z2) {
        final double xDiff = x1 - x2;
        final double yDiff = y1 - y2;
        final double zDiff = z1 - z2;
        return Mth.sqrt((float) (xDiff * xDiff + yDiff * yDiff + zDiff * zDiff));
    }
}
