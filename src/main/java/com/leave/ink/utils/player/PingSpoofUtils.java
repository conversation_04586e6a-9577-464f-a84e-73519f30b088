package com.leave.ink.utils.player;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventMotion;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventType;
import com.leave.ink.events.EventWorld;
import com.leave.ink.utils.timer.TimeUtils;
import com.leave.ink.utils.timer.TimedPacket;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.client.gui.screens.ReceivingLevelScreen;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.*;

import java.util.Arrays;
import java.util.concurrent.ConcurrentLinkedQueue;

public class PingSpoofUtils implements IMinecraft {
    public static ConcurrentLinkedQueue<TimedPacket> packets = new ConcurrentLinkedQueue<>();
    static TimeUtils enabledTimer = new TimeUtils();
    public static boolean enabled;
    static long amount;

    static PacketType<ServerboundPongPacket> regular = new PacketType<>(new Class[]{ServerboundPongPacket.class, ServerboundKeepAlivePacket.class}, false);
    static PacketType<ClientboundSetEntityMotionPacket> velocity = new PacketType<>(new Class[]{ClientboundSetEntityMotionPacket.class, ClientboundExplodePacket.class}, false);
    static PacketType<ClientboundPlayerPositionPacket> teleports = new PacketType<>(new Class[]{ClientboundPlayerPositionPacket.class, ClientboundPlayerAbilitiesPacket.class, ClientboundSetCarriedItemPacket.class}, false);
    static PacketType<ClientboundRemoveEntitiesPacket> players = new PacketType<>(new Class[]{ClientboundRemoveEntitiesPacket.class, ClientboundMoveEntityPacket.class, ClientboundMoveEntityPacket.Rot.class, ClientboundMoveEntityPacket.Pos.class, ClientboundMoveEntityPacket.PosRot.class, ClientboundTeleportEntityPacket.class, ClientboundUpdateAttributesPacket.class, ClientboundRotateHeadPacket.class}, false);
    static PacketType<ServerboundInteractPacket> blink = new PacketType<>(new Class[]{ServerboundInteractPacket.class, ServerboundContainerClosePacket.class, ServerboundContainerClickPacket.class, ServerboundMovePlayerPacket.class, ServerboundMovePlayerPacket.Pos.class, ServerboundMovePlayerPacket.Rot.class, ServerboundMovePlayerPacket.PosRot.class, ServerboundPlayerActionPacket.class, ServerboundUseItemPacket.class, ServerboundUseItemOnPacket.class, ServerboundSetCarriedItemPacket.class, ServerboundPlayerAbilitiesPacket.class, ServerboundClientInformationPacket.class, ServerboundClientCommandPacket.class, ServerboundCustomPayloadPacket.class, ServerboundSwingPacket.class}, false);
    static PacketType<ServerboundMovePlayerPacket> movement = new PacketType<>(new Class[]{ServerboundMovePlayerPacket.class, ServerboundMovePlayerPacket.Pos.class, ServerboundMovePlayerPacket.Rot.class, ServerboundMovePlayerPacket.PosRot.class}, false);

    static PacketType<?>[] types = new PacketType[]{regular, velocity, teleports, players, blink, movement};
    @EventTarget
    public void onPacketC(EventPacket event) {
        if (event.getPacketType() == EventPacket.PacketType.Client)
            event.setCancelled(onPacket(event.getPacket(), event).isCancelled());
    }

    @EventTarget
    public void onPacketS(EventPacket event) {
        //pre
        if (event.getPacketType() == EventPacket.PacketType.Server)
            event.setCancelled(onPacket(event.getPacket(), event).isCancelled());
    }

    public EventPacket onPacket(Packet<?> packet, EventPacket event) {
        if (!event.isCancelled() && enabled && Arrays.stream(types).anyMatch(tuple -> tuple.enabled && Arrays.stream(tuple.packetClasses).anyMatch(regularpacket -> regularpacket == packet.getClass()))) {
            event.setCancelled(true);
            packets.add(new TimedPacket(packet));
        }
        return event;
    }

    public static void dispatch() {
        if (!packets.isEmpty()) {
            // 防止数据包被调用两次
            boolean wasEnabled = enabled;
            enabled = false;
            packets.forEach(timedPacket -> mc.getConnection().send(timedPacket.getPacket()));
            enabled = wasEnabled;
            packets.clear();
        }
    }

    public static void disable() {
        enabled = false;
        enabledTimer.reset();
    }

    @EventTarget
    public void onWorldChange(EventWorld event) {
        dispatch();
    }

    @EventTarget
    public void onMotion(EventMotion event) {
        if (event.getEventType() == EventType.POST) {
            if (!(enabled = !enabledTimer.hasTimeElapsed(100,true) && !(mc.screen instanceof ReceivingLevelScreen))) {
                dispatch();
            } else {
                // 防止数据包被调用两次
                enabled = false;

                packets.forEach(packet -> {
                    if (packet.getMillis() + amount < System.currentTimeMillis()) {
                        mc.getConnection().send(packet.getPacket());
                        packets.remove(packet);
                    }
                });

                enabled = true;
            }
        }
    }

    public static void spoof(int amount, boolean regular, boolean velocity, boolean teleports, boolean players) {
        spoof(amount, regular, velocity, teleports, players, false);
    }

    public static void spoof(int amount, boolean regular, boolean velocity, boolean teleports, boolean players, boolean blink, boolean movement) {
        enabledTimer.reset();

        PingSpoofUtils.regular.enabled = regular;
        PingSpoofUtils.velocity.enabled = velocity;
        PingSpoofUtils.teleports.enabled = teleports;
        PingSpoofUtils.players.enabled = players;
        PingSpoofUtils.blink.enabled = blink;
        PingSpoofUtils.movement.enabled = movement;
        PingSpoofUtils.amount = amount;
    }

    public static void spoof(int amount, boolean regular, boolean velocity, boolean teleports, boolean players, boolean blink) {
        spoof(amount, regular, velocity, teleports, players, blink, false);
    }

    public static void blink() {
        spoof(9999999, true, false, false, false, true);
    }
    public static class PacketType<T extends Packet<?>> {
        private final Class<?>[] packetClasses;
        private boolean enabled;

        public PacketType(Class<?>[] packetClasses, boolean enabled) {
            this.packetClasses = packetClasses;
            this.enabled = enabled;
        }
    }
}
