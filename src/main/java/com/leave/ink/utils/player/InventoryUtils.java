package com.leave.ink.utils.player;

import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;

import java.util.Arrays;
import java.util.List;

public final class InventoryUtils implements IMinecraft {
    public static int findItem(final int startSlot, final int endSlot, final Item item) {
        for (int i = startSlot; i < endSlot; i++) {
            final ItemStack stack = mc.player.getInventory().getItem(i);

            if (!stack.isEmpty() && stack.getItem() == item)
                return i;
        }
        return -1;
    }

    public static int findBlock(final int startSlot, final int endSlot, final Block block) {
        for (int i = startSlot; i < endSlot; i++) {
            final ItemStack stack = mc.player.getInventory().getItem(i);
            final Block blockItem = Block.byItem(stack.getItem());
            if (!stack.isEmpty() && blockItem == block)
                return i;
        }
        return -1;
    }

    public static boolean hasSpaceHotbar() {
        for (int i = 0; i < 9; i++) { // Hotbar slots are 0-8
            final ItemStack itemStack = mc.player.getInventory().getItem(i);

            if (itemStack.isEmpty())
                return true;
        }
        return false;
    }

    public static int findAutoBlockBlock() {
        if (mc.player == null)
            return -1;

        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getItem(i);
            if (stack.isEmpty() || !(stack.getItem() instanceof BlockItem)) continue;

            Block block = ((BlockItem) stack.getItem()).getBlock();
            if (!placeBlockBlacklist(block)) {
                return i;
            }
        }

        return -1;
    }
    public static boolean placeBlockBlacklist(Block block) {
        List<Block> blockedBlocks = Arrays.asList(
                Blocks.ENCHANTING_TABLE, Blocks.CHEST, Blocks.ENDER_CHEST, Blocks.TRAPPED_CHEST,
                Blocks.ANVIL, Blocks.SAND, Blocks.COBWEB, Blocks.TORCH, Blocks.CRAFTING_TABLE,
                Blocks.FURNACE, Blocks.LILY_PAD, Blocks.DISPENSER, Blocks.STONE_PRESSURE_PLATE,
                Blocks.OAK_PRESSURE_PLATE, Blocks.NOTE_BLOCK, Blocks.DROPPER,Blocks.ACACIA_SAPLING,
                Blocks.BAMBOO_SAPLING,Blocks.SPRUCE_SAPLING,Blocks.OAK_SAPLING, Blocks.TNT
        );
        return blockedBlocks.contains(block);
    }
    public static boolean isBlocked(Block block) {
        List<Block> blockedBlocks = Arrays.asList(
                Blocks.ENCHANTING_TABLE, Blocks.CHEST, Blocks.ENDER_CHEST, Blocks.TRAPPED_CHEST,
                Blocks.ANVIL, Blocks.SAND, Blocks.COBWEB, Blocks.TORCH, Blocks.CRAFTING_TABLE,
                Blocks.FURNACE, Blocks.LILY_PAD, Blocks.DISPENSER, Blocks.STONE_PRESSURE_PLATE,
                Blocks.OAK_PRESSURE_PLATE, Blocks.NOTE_BLOCK, Blocks.DROPPER,Blocks.ACACIA_SAPLING,
                Blocks.BAMBOO_SAPLING,Blocks.SPRUCE_SAPLING,Blocks.OAK_SAPLING
        );
        return blockedBlocks.contains(block);
    }

    public static boolean findBlock() {
        final List<Block> blockedBlocks = Arrays.asList(
                Blocks.ENCHANTING_TABLE, Blocks.CHEST, Blocks.ENDER_CHEST, Blocks.TRAPPED_CHEST,
                Blocks.ANVIL, Blocks.SAND, Blocks.COBWEB, Blocks.TORCH, Blocks.CRAFTING_TABLE,
                Blocks.FURNACE, Blocks.LILY_PAD, Blocks.DISPENSER, Blocks.STONE_PRESSURE_PLATE,
                Blocks.OAK_PRESSURE_PLATE, Blocks.NOTE_BLOCK, Blocks.DROPPER);

        for (int i = 0; i < mc.player.getInventory().items.size(); i++) {
            final ItemStack itemStack = mc.player.getInventory().getItem(i);

            if (!itemStack.isEmpty() && itemStack.getItem() instanceof BlockItem) {
                final BlockItem itemBlock = (BlockItem) itemStack.getItem();
                final Block block = itemBlock.getBlock();

                if (block.defaultBlockState().isSolidRender(mc.level, mc.player.blockPosition()) && !blockedBlocks.contains(block))
                    return true;
            }
        }

        for (int i = 0; i < mc.player.getInventory().items.size(); i++) {
            final ItemStack itemStack = mc.player.getInventory().getItem(i);

            if (!itemStack.isEmpty() && itemStack.getItem() instanceof BlockItem) {
                final BlockItem itemBlock = (BlockItem) itemStack.getItem();
                final Block block = itemBlock.getBlock();

                if (!blockedBlocks.contains(block))
                    return true;
            }
        }

        return false;
    }
}
