package com.leave.ink.utils.player;

import com.leave.ink.utils.rotation.PlaceInfo;
import com.leave.ink.utils.wrapper.IMinecraft;
import lombok.experimental.UtilityClass;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.AirBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@UtilityClass
public class BlockUtils implements IMinecraft {
    public BlockPos offset(BlockPos basePos,double x, double y, double z) {
        return new BlockPos((int) Math.floor(basePos.getX() + x), (int) Math.floor(basePos.getY() + y), (int) Math.floor(basePos.getZ() + z));
    }

    public Block getBlock(BlockPos blockPos) {
        return mc.level != null ? mc.level.getBlockState(blockPos).getBlock() : null;
    }

    public boolean isAirBlock(final BlockPos blockPos) {
        if (mc.level == null || mc.player == null) return true;

        final Block block = mc.level.getBlockState(blockPos).getBlock();
        return block instanceof AirBlock;
    }

    public Block block(double x, double y, double z) {
        if (mc.level != null) {
            return mc.level.getBlockState(BlockPos.containing(x, y, z)).getBlock();
        }
        return null;
    }

    public Vec3 getVec3(BlockPos pos, Direction face, float vecy) {
        var x = pos.getX() + 0.5;
        var y = pos.getY() + 0.5;
        var z = pos.getZ() + 0.5;
        if (face == Direction.UP || face == Direction.DOWN) {
            x += 0.3;
            z += 0.3;
        } else {
            y += vecy;
        }
        if (face == Direction.WEST || face == Direction.EAST) {
            z += 0.3;
        }
        if (face == Direction.NORTH || face == Direction.SOUTH) {
            x += 0.3;
        }
        return new Vec3(x, y, z);
    }

    public Block getBlock(Vec3 vec3) {
        return getBlock(BlockPos.containing(vec3));
    }

    public int getBlocksCountInv() {
        if (mc.player == null) return 0;

        int cnt = 0;
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getItem(i);
            if (stack.isEmpty() || !(stack.getItem() instanceof BlockItem)) continue;

            Block block = ((BlockItem) stack.getItem()).getBlock();
            if (!InventoryUtils.isBlocked(block)) {
                cnt += stack.getCount();
            }
        }
        return cnt;
    }

    public BlockState getState(BlockPos blockPos) {
        return mc.level != null ? mc.level.getBlockState(blockPos) : null;
    }

    public boolean canBeClicked(BlockPos blockPos) {
        Block block = getBlock(blockPos);
        BlockState state = getState(blockPos);

        if (block == null || state == null || mc.level == null) {
            return false;
        }

        boolean canCollide = state.getCollisionShape(mc.level, blockPos).isEmpty();
        boolean isWithinBorder = mc.level.getWorldBorder().isWithinBounds(blockPos);

        return canCollide && isWithinBorder;
    }

    public Map<BlockPos, Block> searchBlocks(int radius) {
        Map<BlockPos, Block> blocks = new HashMap<>();
        if (mc.player == null) return blocks;

        for (int x = radius; x >= -radius + 1; x--) {
            for (int y = radius; y >= -radius + 1; y--) {
                for (int z = radius; z >= -radius + 1; z--) {
                    BlockPos blockPos = BlockPos.containing(mc.player.getX() + x, mc.player.getY() + y, mc.player.getZ() + z);
                    Block block = getBlock(blockPos);
                    if (block != null) {
                        blocks.put(blockPos, block);
                    }
                }
            }
        }
        return blocks;
    }

    public double getCenterDistance(BlockPos blockPos) {
        if (mc.player == null) {
            return Double.MAX_VALUE;
        }
        double centerX = blockPos.getX() + 0.5;
        double centerY = blockPos.getY() + 0.5;
        double centerZ = blockPos.getZ() + 0.5;
        return getDistance(mc.player, centerX, centerY, centerZ);
    }

    public double getDistance(Entity entity, double p_getDistance_1_, double p_getDistance_3_, double p_getDistance_5_) {
        double d0 = entity.getX() - p_getDistance_1_;
        double d1 = entity.getY() - p_getDistance_3_;
        double d2 = entity.getZ() - p_getDistance_5_;
        return Mth.sqrt((float) (d0 * d0 + d1 * d1 + d2 * d2));
    }

    public Vec3 getVec(BlockPos blockPos) {
        return new Vec3(blockPos.getX() + 0.5, blockPos.getY() + 0.5, blockPos.getZ() + 0.5);
    }

    public List<BlockEntity> getBlockEntities() {
        List<BlockEntity> list = new ArrayList<>();
        for (LevelChunk chunk : getLoadedChunks())
            list.addAll(chunk.getBlockEntities().values());

        return list;
    }

    public Map<BlockPos, Block> searchBlocks2() {
        Map<BlockPos, Block> blocks = new HashMap<>();
        if (mc.player == null) return blocks;

        int viewDist = mc.options.renderDistance().get();
        for (int x = -viewDist; x <= viewDist; x++) {
            for (int y = -viewDist; y <= viewDist; y++) {
                for (int z = -viewDist; z <= viewDist; z++) {
                    BlockPos blockPos = BlockPos.containing(mc.player.getX() + x, mc.player.getY() + y, mc.player.getZ() + z);
                    Block block = getBlock(blockPos);
                    if (block != null) {
                        blocks.put(blockPos, block);
                    }
                }
            }
        }

        return blocks;
    }

    public List<LevelChunk> getLoadedChunks() {
        if (mc.player == null || mc.level == null) return new ArrayList<>();

        List<LevelChunk> chunks = new ArrayList<>();
        int viewDist = mc.options.renderDistance().get();
        for (int x = -viewDist; x <= viewDist; x++) {
            for (int z = -viewDist; z <= viewDist; z++) {
                LevelChunk chunk =  mc.level.getChunkSource().getChunk((int) mc.player.getX() / 16 + x, (int) mc.player.getZ() / 16 + z, true);
                if (chunk != null) chunks.add(chunk);
            }
        }
        return chunks;
    }

    public PlaceInfo getBlockPlace(BlockPos blockPos) {
        int[][] offsets = {
                {0, 0, 0},

                {-1, 0, 0},
                {1, 0, 0},
                {0, 0, 1},
                {0, 0, -1},

                {-2, 0, 0},
                {2, 0, 0},
                {0, 0, 2},
                {0, 0, -2},

                {1, 0, 1},
                {-1, 0, 1},
                {1, 0, -1},
                {-1, 0, -1},

                {2, 0, 2},
                {-2, 0, 2},
                {2, 0, -2},
                {-2, 0, -2},

                {-3, 0, 0},
                {3, 0, 0},
                {0, 0, 3},
                {0, 0, -3},

                {1, 0, 2},
                {-1, 0, 2},
                {1, 0, -2},
                {-1, 0, -2},

                {2, 0, 1},
                {-2, 0, 1},
                {2, 0, -1},
                {-2, 0, -1},

                {0, -1, 0},
                {1, -1, 0},
                {-1, -1, 0},
                {0, -1, 1},
                {0, -1, -1},

                {1, 0, 3},
                {-1, 0, 3},
                {1, 0, -3},
                {-1, 0, -3},

                {3, 0, 1},
                {-3, 0, 1},
                {3, 0, -1},
                {-3, 0, -1},

                {-4, 0, 0},
                {4, 0, 0},
                {0, 0, 4},
                {0, 0, -4},
        };

        for (int[] offset : offsets) {
            BlockPos currentPos = blockPos.offset(offset[0], offset[1], offset[2]);
            PlaceInfo result = PlaceInfo.checkNearBlocks(currentPos);
            if (result != null) {
                return result;
            }
        }
        return null;
    }
}
