package com.leave.ink.utils.player;

import com.leave.ink.utils.misc.RandomUtils;
import com.leave.ink.utils.rotation.Rotation;
import com.leave.ink.utils.rotation.RotationUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.util.Mth;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.AABB;

public class MovementUtils implements IMinecraft {
    public static void strafe(float speed) {
        if (!isMoving())
            return;

        double yaw = direction();
        double motionX = -Mth.sin((float) yaw) * speed;
        double motionZ = Mth.cos((float) yaw) * speed;
        mc.player.setDeltaMovement(motionX, mc.player.getDeltaMovement().y(), motionZ);
    }

    public static boolean isMoving() {
        return mc.player != null && (mc.player.input.forwardImpulse != 0F || mc.player.input.leftImpulse != 0F);
    }

    public static double direction() {
        float rotationYaw = RotationUtils.clampAngle(mc.player.getYRot());
        if (mc.player.input.forwardImpulse < 0f) rotationYaw += 180f;
        float forward = 1f;
        if (mc.player.input.forwardImpulse < 0f) {
            forward = -0.5f;
        } else if (mc.player.input.forwardImpulse > 0f) {
            forward = 0.5f;
        }
        if (mc.player.input.leftImpulse > 0f) rotationYaw -= 90f * forward;
        if (mc.player.input.leftImpulse < 0f) rotationYaw += 90f * forward;
        return Math.toRadians(rotationYaw);
    }

    public static boolean isOnGround(double height) {
        return !mc.level.getEntityCollisions(mc.player, mc.player.getBoundingBox().move(0.0, -height, 0.0)).isEmpty();
    }

    public static float getMoveYaw(float yaw) {
        Rotation from = new Rotation((float) mc.player.xo, (float) mc.player.zo),
                to = new Rotation((float) mc.player.getX(), (float) mc.player.getZ()),
                diff = new Rotation(to.getYaw() - from.getYaw(), to.getPitch() - from.getPitch());

        double x = diff.getYaw(), z = diff.getPitch();
        if (x != 0 && z != 0) {
            yaw = (float) Math.toDegrees((Math.atan2(-x, z) + RandomUtils.PI2) % RandomUtils.PI2);
        }
        return yaw;
    }

    public static boolean isBlockUnder(final double height, final boolean boundingBox) {
        if (boundingBox) {
            for (int offset = 0; offset < height; offset += 2) {
                final AABB bb = mc.player.getBoundingBox().inflate(0, -offset, 0);

                if (!mc.level.getEntityCollisions(mc.player, bb).isEmpty()) {
                    return true;
                }
            }
        } else {
            for (int offset = 0; offset < height; offset++) {
                if (blockRelativeToPlayer(0, -offset, 0).defaultBlockState().isSolidRender(mc.level, new BlockPos((int)mc.player.getX(), (int)mc.player.getY() - offset, (int)mc.player.getZ()))) {
                    return true;
                }
            }
        }
        return false;
    }

    public static Block blockRelativeToPlayer(final double offsetX, final double offsetY, final double offsetZ) {
        return mc.level.getBlockState(BlockPos.containing(mc.player.getX(),mc.player.getY(), mc.player.getZ()).offset((int)offsetX, (int)offsetY,(int) offsetZ)).getBlock();
    }
}
