package com.leave.ink.utils.misc;

import com.leave.ink.utils.rotation.vector.Vector3d;
import lombok.experimental.UtilityClass;
import net.minecraft.world.phys.Vec3;

import java.security.SecureRandom;


@UtilityClass
public class MathUtils {
    public final float PI = (float) Math.PI;
    public float clamp_float(float num, float min, float max) {
        return num < min ? min : Math.min(num, max);
    }

    public Vec3 wrapVec3(Vector3d vec) {
        return new Vec3(vec.x(), vec.y(), vec.z());
    }

    public double getRandomInRange(double min, double max) {
        SecureRandom random = new SecureRandom();
        return min == max ? min : random.nextDouble() * (max - min) + min;
    }

    public float wrapAngle_To180(float angle) {
        angle %= 360.0F;
        if (angle >= 180.0F) {
            angle -= 360.0F;
        }
        if (angle < -180.0F) {
            angle += 360.0F;
        }
        return angle;
    }

    public float clampPitch_To90(float pitch) {
        if (pitch > 90.0f) {
            return 90.0f;
        } else if (pitch < -90.0f) {
            return -90.0f;
        }
        return pitch;
    }

    public int getRandomNumber(int n1, int n2) {
        if (n1 == n2) return n1;
        int max = Math.max(n1, n2);
        int min = Math.min(n1, n2);
        return getRandomNumberUsingNextInt(min, max);
    }

    public int getRandomNumberUsingNextInt(int min, int max) {
        java.util.Random random = new java.util.Random();
        return random.nextInt(max - min) + min;
    }

    public boolean equals(float a, float b) {
        return Math.abs(a - b) < 10E-5;
    }
}
