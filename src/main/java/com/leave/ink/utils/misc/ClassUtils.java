package com.leave.ink.utils.misc;

import java.util.HashMap;
import java.util.Map;

public class ClassUtils {
    private static final Map<String, Boolean> cachedClasses = new HashMap<>();

    public static boolean hasClass(String className) {
        if (cachedClasses.containsKey(className)) {
            return cachedClasses.get(className);
        } else {
            try {
                Class.forName(className);
                cachedClasses.put(className, true);
                return true;
            } catch (ClassNotFoundException e) {
                cachedClasses.put(className, false);
                return false;
            }
        }
    }
}
