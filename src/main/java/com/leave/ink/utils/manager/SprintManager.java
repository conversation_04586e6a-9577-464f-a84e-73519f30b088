package com.leave.ink.utils.manager;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventStrafe;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.module.modules.movement.Sprint;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.client.KeyMapping;

public class SprintManager implements IMinecraft {
    public boolean shouldSprint = true;
    private KillAura aura = null;
    private Sprint sprint = null;

    public SprintManager() {
        EventManager.register(this);
    }

    @EventTarget
    public void onStrafe(EventStrafe event) {
            if (aura == null) {
                aura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");
            }

            if(sprint == null) {
                sprint = (Sprint) Main.INSTANCE.moduleManager.getModule("Sprint");
            }

            if (aura.isEnable() && (aura.currentTarget != null)) {
                switch (aura.keepSprintMode.getValue()) {
                    case "None" ->
                            KeyMapping.set(mc.options.keySprint.getKey(), false);
                    case "Full" ->
                            mc.options.keySprint.setDown(true);
                    case "Simulation" ->
                            mc.options.keySprint.setDown(aura.target.hurtTime >= aura.keepSprintTick.getMax() - aura.keepSprintTick.getValue().intValue());
                }
                return;
            }


            if(sprint.isEnable()) {
                mc.options.keySprint.setDown(true);
            }
    }
}
