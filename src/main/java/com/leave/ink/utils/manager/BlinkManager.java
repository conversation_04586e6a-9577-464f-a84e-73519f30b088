package com.leave.ink.utils.manager;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.darkmagician6.eventapi.types.Priority;
import com.leave.ink.events.EventMotion;
import com.leave.ink.events.EventType;
import com.leave.ink.events.EventBlink;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.network.PacketUtils;
import com.leave.ink.utils.rotation.Rotation;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientboundPingPacket;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.function.Consumer;
import java.util.function.Predicate;

public class BlinkManager implements IMinecraft {
    public final static BlinkManager INSTANCE = new BlinkManager();
    public LinkedBlockingDeque<Packet<?>> packets = new LinkedBlockingDeque<>();
    public boolean pass = false;
    public boolean blinking = false;
    public Vec3 lastPosition = Vec3.ZERO;
    public Rotation lastRotation = new Rotation(0, 0);
    public int blinkTicks = 0;
    public int delayTicks = 0;
    private final List<Consumer<Packet<?>>> releaseConsumers = new ArrayList<>();
    private final List<Consumer<Packet<?>>> handleConsumers = new ArrayList<>();
    private final List<Predicate<Packet<?>>> handlePredicate = new ArrayList<>();

    private BlinkManager() {
        EventManager.register(this);
    }

    private void reset(){
        blinkTicks = 0;
        delayTicks = 0;

        pass = false;
        blinking = false;

        packets.clear();
        handleConsumers.clear();
        handlePredicate.clear();
        releaseConsumers.clear();
        lastPosition = mc.player == null ? Vec3.ZERO : mc.player.position();
        lastRotation = mc.player == null ? new Rotation(0, 0) : new Rotation(mc.player.getYRot(), mc.player.getXRot());
    }

    public boolean add(Packet<?> packet) {
        if (!PacketUtils.isServerboundPacket(packet)) {
            mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal("Error to add a packet:" + packet.getClass().getName()));
            return false;
        }

        // 检查玩家状态
        if (mc.player == null || mc.level == null || mc.player.tickCount < 200 ||
                mc.player.isSpectator() || !mc.player.isAlive() || mc.player.isDeadOrDying()) {
            return false;
        }

        EventBlink eventBlink = new EventBlink(packet, blinkTicks);
        EventManager.call(eventBlink);

        if (eventBlink.isCancelled()) {
            return false;
        }

        for (Predicate<Packet<?>> predicate : handlePredicate) {
            if (predicate.test(packet)) {
                return false;
            }
        }

        handleConsumers.forEach(consumer -> consumer.accept(packet));
        packets.add(packet);
        return true;
    }

    @EventTarget(Priority.LOWEST)
    public void onTick(EventMotion event) {
        if (event.getEventType() != EventType.PRE) return;  // 修改为PRE事件而不是POST事件

        if (blinking){
            blinkTicks++;
            delayTicks++;
        }
        if(mc.player == null) {
            reset();
        }
    }

    public void release() {
        release(Integer.MAX_VALUE);
    }

    public void release(int ticks) {
        while (!INSTANCE.packets.isEmpty()) {
            if (ticks == 0)
                break;
            Packet<?> packet = INSTANCE.packets.poll();

            if (packet instanceof ClientboundPingPacket) {
                delayTicks--;
                ticks--;
                continue;
            }

            if (packet instanceof ServerboundMovePlayerPacket wrapper) {
                lastPosition = new Vec3(
                        wrapper.getX(lastPosition.x),
                        wrapper.getY(lastPosition.y),
                        wrapper.getZ(lastPosition.z)
                );
                lastRotation = new Rotation(
                        wrapper.getYRot(lastRotation.getYaw()),
                        wrapper.getXRot(lastRotation.getPitch())
                );
            }

            releaseConsumers.forEach(consumer -> consumer.accept(packet));

            sendPacket(packet);
        }
    }

    public void sendPacket(Packet<?> packet) {
        try {
            INSTANCE.pass = true;
            if (!PacketUtils.isServerboundPacket(packet)) mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal(packet.getClass().getSimpleName() + " is not a serverbound packet"));
            PacketUtils.queuePacketNoEvent(packet);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            INSTANCE.pass = false;
        }
    }

    public static void startBlink() {
        if (INSTANCE.blinking) {
            stopBlink();
        } else {
            INSTANCE.reset();
        }
        INSTANCE.blinking = true;
    }

    public static void stopBlink() {
        INSTANCE.release();
        INSTANCE.reset();
    }

    public static void releaseTick(int ticks) {
        INSTANCE.release(ticks);
    }

    public static void addFilter(Predicate<Packet<?>> predicate) {
        INSTANCE.handlePredicate.add(predicate);
    }

    public static void addHandleAction(Consumer<Packet<?>> packetConsumer) {
        INSTANCE.handleConsumers.add(packetConsumer);
    }

    public static void addReleaseConsumer(Consumer<Packet<?>> packetConsumer) {
        INSTANCE.releaseConsumers.add(packetConsumer);
    }
}