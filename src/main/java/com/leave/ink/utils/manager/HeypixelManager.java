package com.leave.ink.utils.manager;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.google.common.util.concurrent.AtomicDouble;
import com.leave.ink.events.EventPacket;
import com.leave.ink.events.EventWorld;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.client.player.AbstractClientPlayer;
import net.minecraft.network.protocol.game.ClientboundSetScorePacket;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.scores.Objective;
import net.minecraft.world.scores.Score;
import net.minecraft.world.scores.Scoreboard;

import java.util.HashMap;
import java.util.Map;


public class HeypixelManager implements IMinecraft {
    private static final Map<Integer, AtomicDouble> HEALTHS = new HashMap<>();

    public HeypixelManager() {
        EventManager.register(this);
    }

    @EventTarget
    public void onWorld(EventWorld eventWorld) {
        HEALTHS.clear();
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacket() instanceof ClientboundSetScorePacket packet) {
            if (mc.level != null && ("belowHealth".equals(packet.getObjectiveName()) || "health".equals(packet.getObjectiveName()))) {

                if (!packet.getOwner().equals(mc.player.getGameProfile().getName())) {
                    int id = getEntityIdByOwner(packet.getOwner());
                    if(id == -1) return;
                    if(!HEALTHS.containsKey(id)) {
                        HEALTHS.get(id).set(packet.getScore());
                    }else{
                        HEALTHS.put(id, new AtomicDouble(packet.getScore()));
                    }

                }
            }
        }
    }

    private int getEntityIdByOwner(String owner) {
        for (AbstractClientPlayer player : mc.level.players()) {
            if(player.getName().getString().equals(owner)) return player.getId();
        }
        return -1;
    }

    public static float getEntityHealth(LivingEntity entity) {
        if(entity instanceof Player player) {
            if(HEALTHS.containsKey(player.getId())) {
                return HEALTHS.get(player.getId()).floatValue();
            }
            Scoreboard scoreboard = player.getScoreboard();
            Objective objective = scoreboard.getDisplayObjective(2);
            if(objective == null) return 20;
            Score score = scoreboard.getOrCreatePlayerScore(entity.getScoreboardName(), objective);
            return score.getScore() == 0 ? 20 : score.getScore();
        } else {
            return entity.getHealth();
        }
    }

    public static float getHealthRate(LivingEntity entity) {
        if(entity instanceof Player) {
            int a = Math.min((int) getEntityHealth(entity), 20);
            return a / entity.getMaxHealth();
        } else {
            return entity.getHealth() / entity.getMaxHealth();
        }

    }
}
