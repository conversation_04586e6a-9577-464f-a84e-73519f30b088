package com.leave.ink.utils.render.projectiles;

public class InterpolatedVector4 {
    public float x;
    public float y;
    public float z;
    public float weight;

    public InterpolatedVector4(float x, float y, float z) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.weight = 0.1F;
    }

    public InterpolatedVector4(float x, float y, float z, float weight) {
        this(x, y, z);
        this.weight = weight;
    }

    public float interpolateX(float by) {
        if (by != this.x) {
            this.x = this.x + (by - this.x) * this.weight;
        }

        return this.x;
    }

    public float interpolateY(float by) {
        if (by != this.y) {
            this.y = this.y + (by - this.y) * this.weight;
        }

        return this.y;
    }

    public float interpolateZ(float by) {
        if (by != this.z) {
            this.z = this.z + (by - this.z) * this.weight;
        }

        return this.z;
    }
}