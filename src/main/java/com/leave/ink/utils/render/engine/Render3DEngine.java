package com.leave.ink.utils.render.engine;

import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;

import com.mojang.math.Axis;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.core.Direction;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import org.jetbrains.annotations.NotNull;
import org.joml.Matrix4f;

import org.joml.Vector3f;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class Render3DEngine implements IMinecraft {
    public static List<FillAction> FILLED_QUEUE = new ArrayList<>();
    public static List<OutlineAction> OUTLINE_QUEUE = new ArrayList<>();
    public static List<FadeAction> FADE_QUEUE = new ArrayList<>();
    public static List<FillSideAction> FILLED_SIDE_QUEUE = new ArrayList<>();
    public static List<OutlineSideAction> OUTLINE_SIDE_QUEUE = new ArrayList<>();
    public static List<DebugLineAction> DEBUG_LINE_QUEUE = new ArrayList<>();
    public static List<LineAction> LINE_QUEUE = new ArrayList<>();
    public static final Matrix4f lastProjMat = new Matrix4f();
    public static final Matrix4f lastModMat = new Matrix4f();
    public static final Matrix4f lastWorldSpaceMatrix = new Matrix4f();
    private static float prevCircleStep;
    private static float circleStep;

    public static void onRender3D(PoseStack stack) {
        if (!FILLED_QUEUE.isEmpty() || !FADE_QUEUE.isEmpty() || !FILLED_SIDE_QUEUE.isEmpty()) {
            Tesselator tessellator = Tesselator.getInstance();
            BufferBuilder bufferBuilder = tessellator.getBuilder();
            bufferBuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);
            RenderSystem.disableDepthTest();
            setupRender();
            RenderSystem.setShader(GameRenderer::getPositionColorShader);
            FILLED_QUEUE.forEach(action -> setFilledBoxVertexes(bufferBuilder, stack.last().pose(), action.box(), action.color()));
            FADE_QUEUE.forEach(action -> setFilledFadePoints(action.box(), bufferBuilder, stack.last().pose(), action.color(), action.color2()));
            FILLED_SIDE_QUEUE.forEach(action -> setFilledSidePoints(bufferBuilder, stack.last().pose(), action.box, action.color(), action.side()));
            tessellator.end();
            endRender();
            RenderSystem.enableDepthTest();
            FADE_QUEUE.clear();
            FILLED_SIDE_QUEUE.clear();
            FILLED_QUEUE.clear();
        }

        if (!OUTLINE_QUEUE.isEmpty() || !OUTLINE_SIDE_QUEUE.isEmpty()) {
            Tesselator tessellator = Tesselator.getInstance();
            BufferBuilder buffer = tessellator.getBuilder();
            buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);
            RenderSystem.disableDepthTest();
            setupRender();
            RenderSystem.lineWidth(2f);
            RenderSystem.setShader(GameRenderer::getPositionColorShader);
            OUTLINE_QUEUE.forEach(action -> setOutlinePoints(action.box(), matrixFrom(action.box().minX, action.box().minY, action.box().minZ), buffer, action.color()));
            OUTLINE_SIDE_QUEUE.forEach(action -> setSideOutlinePoints(action.box, matrixFrom(action.box().minX, action.box().minY, action.box().minZ), buffer, action.color(), action.side()));
            tessellator.end();
            endRender();
            RenderSystem.enableDepthTest();
            OUTLINE_QUEUE.clear();
            OUTLINE_SIDE_QUEUE.clear();
        }

        if (!DEBUG_LINE_QUEUE.isEmpty()) {
            setupRender();
            RenderSystem.disableDepthTest();
            Tesselator tessellator = Tesselator.getInstance();
            BufferBuilder buffer = tessellator.getBuilder();
            buffer.begin(VertexFormat.Mode.DEBUG_LINES, DefaultVertexFormat.POSITION_COLOR);
            RenderSystem.disableCull();
            RenderSystem.setShader(GameRenderer::getPositionColorShader);
            DEBUG_LINE_QUEUE.forEach(action -> {
                PoseStack matrices = matrixFrom(action.start.x, action.start.y, action.start.z);
                vertexLine(matrices, buffer, 0f, 0f, 0f, (float) (action.end.x - action.start.x), (float) (action.end.y - action.start.y), (float) (action.end.z - action.start.z), action.color);
            });
            tessellator.end();
            RenderSystem.enableCull();
            RenderSystem.enableDepthTest();
            endRender();
            DEBUG_LINE_QUEUE.clear();
        }

        if (!LINE_QUEUE.isEmpty()) {
            setupRender();
            RenderSystem.disableCull();
            RenderSystem.setShader(GameRenderer::getRendertypeLinesShader);
            RenderSystem.lineWidth(2f);
            RenderSystem.disableDepthTest();
            Tesselator tessellator = Tesselator.getInstance();
            BufferBuilder buffer = tessellator.getBuilder();
            buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);
            LINE_QUEUE.forEach(action -> {
                PoseStack matrices = matrixFrom(action.start.x, action.start.y, action.start.z);
                vertexLine(matrices, buffer, 0f, 0f, 0f, (float) (action.end.x - action.start.x), (float) (action.end.y - action.start.y), (float) (action.end.z - action.start.z), action.color);
            });
            tessellator.end();
            RenderSystem.enableCull();
            RenderSystem.lineWidth(1f);
            RenderSystem.enableDepthTest();
            endRender();
            LINE_QUEUE.clear();
        }
    }

    @Deprecated
    @SuppressWarnings("unused")
    public static void drawFilledBox(PoseStack stack, AABB box, Color c) {
        FILLED_QUEUE.add(new FillAction(box, c));
    }

    public static void setFilledBoxVertexes(@NotNull BufferBuilder bufferBuilder, Matrix4f m, @NotNull AABB box, @NotNull Color c) {
        float minX = (float) (box.minX - mc.getEntityRenderDispatcher().camera.getPosition().x);
        float minY = (float) (box.minY - mc.getEntityRenderDispatcher().camera.getPosition().y);
        float minZ = (float) (box.minZ - mc.getEntityRenderDispatcher().camera.getPosition().z);
        float maxX = (float) (box.maxX - mc.getEntityRenderDispatcher().camera.getPosition().x);
        float maxY = (float) (box.maxY - mc.getEntityRenderDispatcher().camera.getPosition().y);
        float maxZ = (float) (box.maxZ - mc.getEntityRenderDispatcher().camera.getPosition().z);

        bufferBuilder.vertex(m, minX, minY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, minY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, minY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, minY, maxZ).color(c.getRGB()).endVertex();

        bufferBuilder.vertex(m, minX, minY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, maxY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, maxY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, minY, minZ).color(c.getRGB()).endVertex();

        bufferBuilder.vertex(m, maxX, minY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, maxY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, maxY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, minY, maxZ).color(c.getRGB()).endVertex();

        bufferBuilder.vertex(m, minX, minY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, minY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, maxY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, maxY, maxZ).color(c.getRGB()).endVertex();

        bufferBuilder.vertex(m, minX, minY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, minY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, maxY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, maxY, minZ).color(c.getRGB()).endVertex();

        bufferBuilder.vertex(m, minX, maxY, minZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, minX, maxY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, maxY, maxZ).color(c.getRGB()).endVertex();
        bufferBuilder.vertex(m, maxX, maxY, minZ).color(c.getRGB()).endVertex();
    }

    public static @NotNull AABB interpolateBox(@NotNull AABB from, @NotNull AABB to, float delta) {
        double X = RenderUtils.interpolate(from.maxX, to.maxX, delta);
        double Y = RenderUtils.interpolate(from.maxY, to.maxY, delta);
        double Z = RenderUtils.interpolate(from.maxZ, to.maxZ, delta);
        double X1 = RenderUtils.interpolate(from.minX, to.minX, delta);
        double Y1 = RenderUtils.interpolate(from.minY, to.minY, delta);
        double Z1 = RenderUtils.interpolate(from.minZ, to.minZ, delta);
        return new AABB(X1, Y1, Z1, X, Y, Z);
    }

    @Deprecated
    public static void drawFilledSide(PoseStack stack, @NotNull AABB box, Color c, Direction dir) {
        FILLED_SIDE_QUEUE.add(new FillSideAction(box, c, dir));
    }

    public static void setFilledSidePoints(BufferBuilder buffer, Matrix4f matrix, AABB box, Color c, Direction dir) {
        float minX = (float) (box.minX - mc.getEntityRenderDispatcher().camera.getPosition().x);
        float minY = (float) (box.minY - mc.getEntityRenderDispatcher().camera.getPosition().y);
        float minZ = (float) (box.minZ - mc.getEntityRenderDispatcher().camera.getPosition().z);
        float maxX = (float) (box.maxX - mc.getEntityRenderDispatcher().camera.getPosition().x);
        float maxY = (float) (box.maxY - mc.getEntityRenderDispatcher().camera.getPosition().y);
        float maxZ = (float) (box.maxZ - mc.getEntityRenderDispatcher().camera.getPosition().z);

        if (dir == Direction.DOWN) {
            buffer.vertex(matrix, minX, minY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, minY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, minY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, minY, maxZ).color(c.getRGB()).endVertex();
        }

        if (dir == Direction.NORTH) {
            buffer.vertex(matrix, minX, minY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, maxY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, maxY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, minY, minZ).color(c.getRGB()).endVertex();
        }

        if (dir == Direction.EAST) {
            buffer.vertex(matrix, maxX, minY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, maxY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, maxY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, minY, maxZ).color(c.getRGB()).endVertex();
        }
        if (dir == Direction.SOUTH) {
            buffer.vertex(matrix, minX, minY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, minY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, maxY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, maxY, maxZ).color(c.getRGB()).endVertex();
        }

        if (dir == Direction.WEST) {
            buffer.vertex(matrix, minX, minY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, minY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, maxY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, maxY, minZ).color(c.getRGB()).endVertex();
        }

        if (dir == Direction.UP) {
            buffer.vertex(matrix, minX, maxY, minZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, minX, maxY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, maxY, maxZ).color(c.getRGB()).endVertex();
            buffer.vertex(matrix, maxX, maxY, minZ).color(c.getRGB()).endVertex();
        }
    }

    @Deprecated
    @SuppressWarnings("unused")
    public static void drawFilledFadeBox(@NotNull PoseStack stack, @NotNull AABB box, @NotNull Color c, @NotNull Color c1) {
        FADE_QUEUE.add(new FadeAction(box, c, c1));
    }

    public static void setFilledFadePoints(AABB box, BufferBuilder buffer, Matrix4f posMatrix, Color c, Color c1) {
        float minX = (float) (box.minX - mc.getEntityRenderDispatcher().camera.getPosition().x);
        float minY = (float) (box.minY - mc.getEntityRenderDispatcher().camera.getPosition().y);
        float minZ = (float) (box.minZ - mc.getEntityRenderDispatcher().camera.getPosition().z);
        float maxX = (float) (box.maxX - mc.getEntityRenderDispatcher().camera.getPosition().x);
        float maxY = (float) (box.maxY - mc.getEntityRenderDispatcher().camera.getPosition().y);
        float maxZ = (float) (box.maxZ - mc.getEntityRenderDispatcher().camera.getPosition().z);

        buffer.vertex(posMatrix, minX, minY, minZ).color(c.getRGB()).endVertex();
        buffer.vertex(posMatrix, minX, maxY, minZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, maxY, minZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, minY, minZ).color(c.getRGB()).endVertex();

        buffer.vertex(posMatrix, maxX, minY, minZ).color(c.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, maxY, minZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, maxY, maxZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, minY, maxZ).color(c.getRGB()).endVertex();

        buffer.vertex(posMatrix, minX, minY, maxZ).color(c.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, minY, maxZ).color(c.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, maxY, maxZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, minX, maxY, maxZ).color(c1.getRGB()).endVertex();

        buffer.vertex(posMatrix, minX, minY, minZ).color(c.getRGB()).endVertex();
        buffer.vertex(posMatrix, minX, minY, maxZ).color(c.getRGB()).endVertex();
        buffer.vertex(posMatrix, minX, maxY, maxZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, minX, maxY, minZ).color(c1.getRGB()).endVertex();

        buffer.vertex(posMatrix, minX, maxY, minZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, minX, maxY, maxZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, maxY, maxZ).color(c1.getRGB()).endVertex();
        buffer.vertex(posMatrix, maxX, maxY, minZ).color(c1.getRGB()).endVertex();
    }

    public static void drawLine(@NotNull Vec3 start, @NotNull Vec3 end, @NotNull Color color) {
        LINE_QUEUE.add(new LineAction(start, end, color));
    }

    @Deprecated
    public static void drawBoxOutline(@NotNull AABB box, Color color, float lineWidth) {
        OUTLINE_QUEUE.add(new OutlineAction(box, color, lineWidth));
    }

    public static void setOutlinePoints(AABB box, PoseStack matrices, BufferBuilder buffer, Color color) {
        box = box.inflate(-box.minX, -box.minY, -box.minZ);

        float x1 = (float) box.minX;
        float y1 = (float) box.minY;
        float z1 = (float) box.minZ;
        float x2 = (float) box.maxX;
        float y2 = (float) box.maxY;
        float z2 = (float) box.maxZ;

        vertexLine(matrices, buffer, x1, y1, z1, x2, y1, z1, color);
        vertexLine(matrices, buffer, x2, y1, z1, x2, y1, z2, color);
        vertexLine(matrices, buffer, x2, y1, z2, x1, y1, z2, color);
        vertexLine(matrices, buffer, x1, y1, z2, x1, y1, z1, color);
        vertexLine(matrices, buffer, x1, y1, z2, x1, y2, z2, color);
        vertexLine(matrices, buffer, x1, y1, z1, x1, y2, z1, color);
        vertexLine(matrices, buffer, x2, y1, z2, x2, y2, z2, color);
        vertexLine(matrices, buffer, x2, y1, z1, x2, y2, z1, color);
        vertexLine(matrices, buffer, x1, y2, z1, x2, y2, z1, color);
        vertexLine(matrices, buffer, x2, y2, z1, x2, y2, z2, color);
        vertexLine(matrices, buffer, x2, y2, z2, x1, y2, z2, color);
        vertexLine(matrices, buffer, x1, y2, z2, x1, y2, z1, color);
    }

    @Deprecated
    public static void drawSideOutline(@NotNull AABB box, Color color, float lineWidth, Direction dir) {
        OUTLINE_SIDE_QUEUE.add(new OutlineSideAction(box, color, lineWidth, dir));
    }

    public static void setSideOutlinePoints(AABB box, PoseStack matrices, BufferBuilder buffer, Color color, Direction dir) {
        box = new AABB(0, 0, 0, box.maxX - box.minX, box.maxY - box.minY, box.maxZ - box.minZ);

        float x1 = (float) box.minX;
        float y1 = (float) box.minY;
        float z1 = (float) box.minZ;
        float x2 = (float) box.maxX;
        float y2 = (float) box.maxY;
        float z2 = (float) box.maxZ;

        switch (dir) {
            case UP -> {
                vertexLine(matrices, buffer, x1, y2, z1, x2, y2, z1, color);
                vertexLine(matrices, buffer, x2, y2, z1, x2, y2, z2, color);
                vertexLine(matrices, buffer, x2, y2, z2, x1, y2, z2, color);
                vertexLine(matrices, buffer, x1, y2, z2, x1, y2, z1, color);
            }
            case DOWN -> {
                vertexLine(matrices, buffer, x1, y1, z1, x2, y1, z1, color);
                vertexLine(matrices, buffer, x2, y1, z1, x2, y1, z2, color);
                vertexLine(matrices, buffer, x2, y1, z2, x1, y1, z2, color);
                vertexLine(matrices, buffer, x1, y1, z2, x1, y1, z1, color);
            }
            case EAST -> {
                vertexLine(matrices, buffer, x2, y1, z1, x2, y2, z1, color);
                vertexLine(matrices, buffer, x2, y1, z2, x2, y2, z2, color);
                vertexLine(matrices, buffer, x2, y2, z2, x2, y2, z1, color);
                vertexLine(matrices, buffer, x2, y1, z2, x2, y1, z1, color);
            }
            case WEST -> {
                vertexLine(matrices, buffer, x1, y1, z1, x1, y2, z1, color);
                vertexLine(matrices, buffer, x1, y1, z2, x1, y2, z2, color);
                vertexLine(matrices, buffer, x1, y2, z2, x1, y2, z1, color);
                vertexLine(matrices, buffer, x1, y1, z2, x1, y1, z1, color);
            }
            case NORTH -> {
                vertexLine(matrices, buffer, x2, y1, z1, x2, y2, z1, color);
                vertexLine(matrices, buffer, x1, y1, z1, x1, y2, z1, color);
                vertexLine(matrices, buffer, x2, y1, z1, x1, y1, z1, color);
                vertexLine(matrices, buffer, x2, y2, z1, x1, y2, z1, color);
            }
            case SOUTH -> {
                vertexLine(matrices, buffer, x1, y1, z2, x1, y2, z2, color);
                vertexLine(matrices, buffer, x2, y1, z2, x2, y2, z2, color);
                vertexLine(matrices, buffer, x1, y1, z2, x2, y1, z2, color);
                vertexLine(matrices, buffer, x1, y2, z2, x2, y2, z2, color);
            }
        }
    }

    public static void drawHoleOutline(@NotNull AABB box, Color color, float lineWidth) {
        setupRender();
        PoseStack matrices = matrixFrom(box.minX, box.minY, box.minZ);
        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder buffer = tessellator.getBuilder();
        buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);

        RenderSystem.disableCull();
        RenderSystem.setShader(GameRenderer::getRendertypeLinesShader);
        RenderSystem.lineWidth(lineWidth);

        box = box.inflate(-box.minX, -box.minY, -box.minZ);

        float x1 = (float) box.minX;
        float y1 = (float) box.minY;
        float y2 = (float) box.maxY;
        float z1 = (float) box.minZ;
        float x2 = (float) box.maxX;
        float z2 = (float) box.maxZ;

        vertexLine(matrices, buffer, x1, y1, z1, x2, y1, z1, color);
        vertexLine(matrices, buffer, x2, y1, z1, x2, y1, z2, color);
        vertexLine(matrices, buffer, x2, y1, z2, x1, y1, z2, color);
        vertexLine(matrices, buffer, x1, y1, z2, x1, y1, z1, color);

        vertexLine(matrices, buffer, x1, y1, z1, x1, y2, z1, color);
        vertexLine(matrices, buffer, x2, y1, z2, x2, y2, z2, color);
        vertexLine(matrices, buffer, x1, y1, z2, x1, y2, z2, color);
        vertexLine(matrices, buffer, x2, y1, z1, x2, y2, z1, color);

        tessellator.end();
        RenderSystem.enableCull();
        endRender();
    }

    public static void vertexLine(@NotNull PoseStack matrices, @NotNull VertexConsumer buffer, float x1, float y1, float z1, float x2, float y2, float z2, @NotNull Color lineColor) {
        Matrix4f model = matrices.last().pose();
        PoseStack.Pose entry = matrices.last();
        Vector3f normalVec = getNormal(x1, y1, z1, x2, y2, z2);
        buffer.vertex(model, x1, y1, z1).color(lineColor.getRGB()).normal(entry.normal(), normalVec.x(), normalVec.y(), normalVec.z()).endVertex();
        buffer.vertex(model, x2, y2, z2).color(lineColor.getRGB()).normal(entry.normal(), normalVec.x(), normalVec.y(), normalVec.z()).endVertex();
    }

    public static @NotNull Vector3f getNormal(float x1, float y1, float z1, float x2, float y2, float z2) {
        float xNormal = x2 - x1;
        float yNormal = y2 - y1;
        float zNormal = z2 - z1;
        float normalSqrt = Mth.sqrt(xNormal * xNormal + yNormal * yNormal + zNormal * zNormal);

        return new Vector3f(xNormal / normalSqrt, yNormal / normalSqrt, zNormal / normalSqrt);
    }

    public static @NotNull PoseStack matrixFrom(double x, double y, double z) {
        PoseStack matrices = new PoseStack();

        Camera camera = mc.gameRenderer.getMainCamera();
        matrices.mulPose(Axis.XP.rotationDegrees(camera.getXRot()));
        matrices.mulPose(Axis.YP.rotationDegrees(camera.getYRot() + 180.0F));

        matrices.translate(x - camera.getPosition().x, y - camera.getPosition().y, z - camera.getPosition().z);

        return matrices;
    }

    public static void setupRender() {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
    }

    public static void endRender() {
        RenderSystem.disableBlend();
    }

    public static void renderCrosses(@NotNull AABB box, Color color, float lineWidth) {
        setupRender();
        PoseStack matrices = matrixFrom(box.minX, box.minY, box.minZ);
        RenderSystem.disableCull();
        RenderSystem.setShader(GameRenderer::getRendertypeLinesShader);
        RenderSystem.lineWidth(lineWidth);
        BufferBuilder buffer = Tesselator.getInstance().getBuilder();
        buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);

        box = box.inflate(-box.minX, -box.minY, -box.minZ);

        vertexLine(matrices, buffer, (float) box.maxX, (float) box.minY, (float) box.minZ, (float) box.minX, (float) box.minY, (float) box.maxZ, color);
        vertexLine(matrices, buffer, (float) box.minX, (float) box.minY, (float) box.minZ, (float) box.maxX, (float) box.minY, (float) box.maxZ, color);

        RenderUtils.endBuilding(Tesselator.getInstance());
        RenderSystem.enableCull();
        endRender();
    }

    public static void drawSphere(PoseStack matrix, float radius, int slices, int stacks, int color) {
        float drho = 3.1415927F / ((float) stacks);
        float dtheta = 6.2831855F / ((float) slices - 1f);
        float rho;
        float theta;
        float x;
        float y;
        float z;
        int i;
        int j;
        setupRender();
        for (i = 1; i < stacks; ++i) {
            rho = (float) i * drho;

            BufferBuilder buffer = Tesselator.getInstance().getBuilder();
            buffer.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);
            RenderSystem.setShader(GameRenderer::getPositionColorShader);

            for (j = 0; j < slices; ++j) {
                theta = (float) j * dtheta;
                x = (float) (Math.cos(theta) * Math.sin(rho));
                y = (float) (Math.sin(theta) * Math.sin(rho));
                z = (float) Math.cos(rho);
                buffer.vertex(matrix.last().pose(), x * radius, y * radius, z * radius).color(color);
            }
            RenderUtils.endBuilding(Tesselator.getInstance());
        }

        for (j = 0; j < slices; ++j) {
            theta = (float) j * dtheta;

            BufferBuilder buffer = Tesselator.getInstance().getBuilder();
            buffer.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);
            RenderSystem.setShader(GameRenderer::getPositionColorShader);

            for (i = 0; i <= stacks; ++i) {
                rho = (float) i * drho;
                x = (float) (Math.cos(theta) * Math.sin(rho));
                y = (float) (Math.sin(theta) * Math.sin(rho));
                z = (float) Math.cos(rho);
                buffer.vertex(matrix.last().pose(), x * radius, y * radius, z * radius).color(color);
            }
            BufferUploader.draw(buffer.end());
        }
        endRender();
    }

    public static void drawCircle3D(PoseStack stack, Entity ent, float radius, int color, int points) {
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        BufferBuilder buffer = Tesselator.getInstance().getBuilder();
        buffer.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        double x = ent.xo + (ent.getX() - ent.xo) * getTickDelta() - mc.getEntityRenderDispatcher().camera.getPosition().x;
        double y = ent.yo + (ent.getY() - ent.yo) * getTickDelta() - mc.getEntityRenderDispatcher().camera.getPosition().y;
        double z = ent.zo + (ent.getZ() - ent.zo) * getTickDelta() - mc.getEntityRenderDispatcher().camera.getPosition().z;
        stack.pushPose();
        stack.translate(x, y, z);

        Matrix4f matrix = stack.last().pose();
        for (int i = 0; i <= points; i++) {
            buffer.vertex(matrix, (float) (radius * Math.cos(i * 6.28 / points)), 0f, (float) (radius * Math.sin(i * 6.28 / points))).color(color);
        }

        BufferUploader.draw(buffer.end());
        endRender();
        stack.translate(-x, -y, -z);
        stack.popPose();
    }

    public static void updateTargetESP() {
        prevCircleStep = circleStep;
        circleStep += 0.15f;
    }

    public static double absSinAnimation(double input) {
        return Math.abs(1 + Math.sin(input)) / 2;
    }

    public static Vec3 interpolatePos(float prevposX, float prevposY, float prevposZ, float posX, float posY, float posZ) {
        double x = prevposX + ((posX - prevposX) * getTickDelta()) - mc.getEntityRenderDispatcher().camera.getPosition().x;
        double y = prevposY + ((posY - prevposY) * getTickDelta()) - mc.getEntityRenderDispatcher().camera.getPosition().y;
        double z = prevposZ + ((posZ - prevposZ) * getTickDelta()) - mc.getEntityRenderDispatcher().camera.getPosition().z;
        return new Vec3(x, y, z);
    }

    public static void drawLineDebug(Vec3 start, Vec3 end, Color color) {
        DEBUG_LINE_QUEUE.add(new DebugLineAction(start, end, color));
    }

    public static float getTickDelta() {
        return mc.getFrameTime();
    }

    public record FillAction(AABB box, Color color) {
    }

    public record OutlineAction(AABB box, Color color, float lineWidth) {
    }

    public record FadeAction(AABB box, Color color, Color color2) {
    }

    public record FillSideAction(AABB box, Color color, Direction side) {
    }

    public record OutlineSideAction(AABB box, Color color, float lineWidth, Direction side) {
    }

    public record DebugLineAction(Vec3 start, Vec3 end, Color color) {
    }

    public record LineAction(Vec3 start, Vec3 end, Color color) {
    }
}