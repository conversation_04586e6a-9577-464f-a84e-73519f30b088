package com.leave.ink.utils.render.engine;

import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import com.mojang.math.Axis;

import net.minecraft.client.Camera;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix4f;

import java.awt.*;

public class CaptureMark implements IMinecraft {
    private static float espValue = 1f, prevEspValue;
    private static float espSpeed = 1f;
    private static boolean flipSpeed;
    public static void drawJello(PoseStack matrix, Entity target, float delta, float step, Color color) {
        Camera camera = mc.getEntityRenderDispatcher().camera;
        Vec3 cameraPos = camera.getPosition();
        float scale = (float) RenderUtils.animation.getOutput();
        double prevSinAnim = Math.abs(1 + Math.sin((double) step - 0.45f)) / 2;
        double sinAnim = Math.abs(1 + Math.sin(step)) / 2;

        double x = target.xo + (target.getX() - target.xo) * delta - cameraPos.x();
        double y = target.yo + (target.getY() - target.yo) * delta - cameraPos.y() + prevSinAnim * target.getBbHeight();
        double z = target.zo + (target.getZ() - target.zo) * delta - cameraPos.z();
        double nextY = target.yo + (target.getY() - target.yo) * delta - cameraPos.y() + sinAnim * target.getBbHeight();

        matrix.pushPose();
//        matrix.scale(4, 4, 4);
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableCull();
        RenderSystem.disableDepthTest();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        BufferBuilder bufferBuilder = Tesselator.getInstance().getBuilder();
        bufferBuilder.begin(VertexFormat.Mode.TRIANGLE_STRIP, DefaultVertexFormat.POSITION_COLOR);

        float cos;
        float sin;

        for (int i = 0; i <= 360; i += 8) {


            cos = (float) (x + Math.cos(i * 6.28 / 360) * ((target.getBoundingBox().maxX - target.getBoundingBox().minX) + (target.getBoundingBox().maxZ - target.getBoundingBox().minZ)) * scale * 0.5f);
            sin = (float) (z + Math.sin(i * 6.28 / 360) * ((target.getBoundingBox().maxX - target.getBoundingBox().minX) + (target.getBoundingBox().maxZ - target.getBoundingBox().minZ)) * scale * 0.5f);
            bufferBuilder.vertex(matrix.last().pose(), cos, (float) nextY, sin).color(color.getRGB()).endVertex();

            bufferBuilder.vertex(matrix.last().pose(), cos, (float) y, sin).color(RenderUtils.applyOpacity(color, 0).getRGB()).endVertex();

        }

        BufferUploader.drawWithShader(bufferBuilder.end());
        RenderSystem.enableCull();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        matrix.popPose();
    }
    public static void render(Entity target, float tickDelta, PoseStack matrices) {
        float scale = (float) RenderUtils.animation.getOutput();

        matrices.pushPose();
        RenderSystem.disableDepthTest();
        RenderSystem.disableCull();
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
        matrices.translate(
                target.xOld + (target.getX() - target.xOld) * tickDelta - renderManager.camera.getPosition().x,
                target.yOld + (target.getY() - target.yOld) * tickDelta - renderManager.camera.getPosition().y + target.getEyeHeight() - 0.4,
                target.zOld + (target.getZ() - target.zOld) * tickDelta - renderManager.camera.getPosition().z
        );
        matrices.mulPose(Axis.YP.rotationDegrees(-renderManager.camera.getYRot()));
        matrices.mulPose(Axis.XP.rotationDegrees(renderManager.camera.getXRot()));
        matrices.mulPose(Axis.ZP.rotationDegrees(Mth.lerp(tickDelta, prevEspValue, espValue)));
        matrices.scale(scale, scale, scale);
        RenderSystem.enableBlend();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE);
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.setShaderTexture(0, new ResourceLocation("/capture.png"));
        matrices.translate(-0.75, -0.75, -0.01);
        Matrix4f matrix = matrices.last().pose();
        BufferBuilder buffer = Tesselator.getInstance().getBuilder();
        buffer.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_TEX_COLOR);
        buffer.vertex(matrix, 0, 1.5f, 0).uv(0f, 1f).color(new Color(179, 180, 213).getRGB()).endVertex();
        buffer.vertex(matrix, 1.5f, 1.5f, 0).uv(1f, 1f).color(new Color(179, 180, 213).getRGB()).endVertex();
        buffer.vertex(matrix, 1.5f, 0, 0).uv(1f, 0).color(new Color(179, 180, 213).getRGB()).endVertex();
        buffer.vertex(matrix, 0, 0, 0).uv(0, 0).color(new Color(179, 180, 213).getRGB()).endVertex();
        Tesselator.getInstance().end();
        RenderSystem.enableCull();
        RenderSystem.enableDepthTest();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
        RenderSystem.disableBlend();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
        matrices.popPose();
    }

    public static void tick() {
        prevEspValue = espValue;
        espValue += espSpeed;
        if (espSpeed > 25) flipSpeed = true;
        if (espSpeed < -25) flipSpeed = false;
        espSpeed = flipSpeed ? espSpeed - 0.5f : espSpeed + 0.5f;
    }
}
