package com.leave.ink.utils.render.projectiles;

import java.util.ArrayList;

public final class ExtendedProjectileDirectionList extends ArrayList<ProjectileDirection> {
    public ExtendedProjectileDirectionList() {
        this.add(ProjectileDirection.LEFT);
        this.add(ProjectileDirection.RIGHT);
        this.add(ProjectileDirection.FORWARD);
        this.add(ProjectileDirection.BACKWARDS);
        this.add(ProjectileDirection.UP);
        this.add(ProjectileDirection.DOWN);
    }
}