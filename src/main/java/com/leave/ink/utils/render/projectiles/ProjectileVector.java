package com.leave.ink.utils.render.projectiles;

import net.minecraft.core.BlockPos;

public class ProjectileVector {
    public double x;
    public double y;
    public double z;

    public ProjectileVector(double x, double y, double z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    public ProjectileVector(BlockPos blockPos) {
        this.x = blockPos.getX();
        this.y = blockPos.getY();
        this.z = blockPos.getZ();
    }

    public double getX() {
        return this.x;
    }

    public double getY() {
        return this.y;
    }

    public double getZ() {
        return this.z;
    }

    public ProjectileVector add(double x, double y, double z) {
        return new ProjectileVector(this.x + x, this.y + y, this.z + z);
    }

    public ProjectileVector floored() {
        return new ProjectileVector(Math.floor(this.x), Math.floor(this.y), Math.floor(this.z));
    }

    public BlockPos toBlockPos() {
        return BlockPos.containing(Math.floor(this.x), Math.floor(this.y), Math.floor(this.z));
    }

    public double method36626(ProjectileVector var1) {
        return Math.pow(var1.x - this.x, 2.0)
                + Math.pow(var1.y - this.y, 2.0)
                + Math.pow(var1.z - this.z, 2.0);
    }

    public ProjectileVector method36627(ProjectileVector var1) {
        return this.add(var1.getX(), var1.getY(), var1.getZ());
    }

    @Override
    public String toString() {
        return "[" + this.x + ";" + this.y + ";" + this.z + "]";
    }

    public ProjectileVector add(ProjectileVector dir) {
        return this.add(-dir.getX(), -dir.getY(), -dir.getZ());
    }

    public ProjectileVector method36630(ProjectileVector dir) {
        return new ProjectileVector(
                this.y * dir.z - this.z * dir.y,
                this.z * dir.x - this.x * dir.z,
                this.x * dir.y - this.y * dir.x
        );
    }

    public ProjectileVector method36631() {
        double mag = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
        return !(mag < 1.0E-4) ? new ProjectileVector(this.x / mag, this.y / mag, this.z / mag) : new ProjectileVector(0.0, 0.0, 0.0);
    }
}