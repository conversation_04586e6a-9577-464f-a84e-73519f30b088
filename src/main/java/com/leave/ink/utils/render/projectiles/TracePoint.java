package com.leave.ink.utils.render.projectiles;


import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.rotation.vector.Vector3d;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.FallingBlock;
import net.minecraft.world.level.block.FenceBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.VoxelShape;


public class TracePoint implements IMinecraft {
    private double x;
    private double y;
    private double z;
    public ProjectileContext field41869;
    private static ProjectileVector[] directions = new ProjectileVector[]{
            new ProjectileVector(1.0, 0.0, 0.0),
            new ProjectileVector(-1.0, 0.0, 0.0),
            new ProjectileVector(0.0, 0.0, 1.0),
            new ProjectileVector(0.0, 0.0, -1.0),
            new ProjectileVector(0.0, -1.0, 0.0),
            new ProjectileVector(0.0, 1.0, 0.0)
    };

    public TracePoint(double var1, double var3, double var5, ProjectileContext var7) {
        this.fromXYZ(var1, var3, var5);
        this.field41869 = var7;
    }

    public TracePoint(double var1, double var3, double var5) {
        this.fromXYZ(var1, var3, var5);
    }

    public TracePoint(BlockPos var1) {
        this.fromXYZ((double)var1.getX(), (double)var1.getY(), (double)var1.getZ());
    }

    public void fromXYZ(double x, double y, double z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    public TracePoint method33965(double var1, double var3, double var5) {
        return new TracePoint(this.x + var1, this.y + var3, this.z + var5);
    }

    public void setX(float x) {
        this.x = x;
    }

    public void setY(float y) {
        this.y = y;
    }

    public void setZ(float z) {
        this.z = z;
    }

    public double getX() {
        return this.x;
    }

    public double getY() {
        return this.y;
    }

    public double getZ() {
        return this.z;
    }

    public Vector3d getMiddleXZ() {
        return new Vector3d(this.getX(), this.getY(), this.getZ()).add(0.5, 0.0, 0.5);
    }

    public double getXZDistance(Vector3d vec) {
        double xDist = vec.x() - this.getX() - 0.5;
        double zDist = vec.z() - this.getZ() - 0.5;
        return Math.sqrt(xDist * xDist + zDist * zDist);
    }

    public float getDistance(Entity e) {
        double xDist = e.getX() - this.getX();
        double yDist = e.getY() - this.getY();
        double zDist = e.getZ() - this.getZ();
        return (float) Math.sqrt(xDist * xDist + yDist * yDist + zDist * zDist);
    }

    public float method33975(TracePoint var1) {
        double var4 = var1.getX() - this.getX();
        double var6 = var1.getY() - this.getY();
        double var8 = var1.getZ() - this.getZ();
        return (float) Math.sqrt(var4 * var4 + var6 * var6 + var8 * var8);
    }

    public double method33976(TracePoint var1) {
        double var4 = var1.getX() - this.getX();
        double var6 = var1.getY() - this.getY();
        double var8 = var1.getZ() - this.getZ();
        return var4 * var4 + var6 * var6 + var8 * var8;
    }

    public TracePoint method33977(float var1, float var2, float var3) {
        return new TracePoint(this.getX() + (double)var1, this.getY() + (double)var2, this.getZ() + (double)var3, this.field41869);
    }

    public double method33978(TracePoint var1) {
        double var4 = var1.getX() - this.getX();
        double var6 = var1.getY() - this.getY();
        double var8 = var1.getZ() - this.getZ();
        return Math.abs(var4) + Math.abs(var8) + Math.abs(var6);
    }

    public BlockPos toBlockPos() {
        return BlockPos.containing(this.getX(), this.getY(), this.getZ());
    }

    public boolean method33980() {
        if (BlockUtils.getBlock(toBlockPos()) == Blocks.LAVA) {
            return false;
        } else if (mc.level.getBlockState(this.toBlockPos()).getFluidState().isEmpty()) {
            return mc.level.getBlockState(this.toBlockPos().below()).getBlock() instanceof FenceBlock
                    ? false
                    : this.method33988(this.toBlockPos()) && this.method33988(this.toBlockPos().above());
        } else {
            return false;
        }
    }

    public boolean method33981() {
        return this.method33980() && this.method33988(this.toBlockPos().above(2));
    }

    public boolean method33982() {
        return this.method33981() && this.method33988(this.toBlockPos().above(3));
    }

    public boolean method33983() {
        return this.method33980() && this.method33990(this.toBlockPos().below());
    }

    public static boolean method33984(BlockPos var0) {
        for (ProjectileVector var6 : directions) {

            BlockState var7 = mc.level.getBlockState(BlockUtils.offset(var0,var6.x, var6.y, var6.z));
            if (var7.getBlock() == Blocks.WATER || var7.getBlock() == Blocks.LAVA) {
                return true;
            }
        }

        return false;
    }

    public static boolean method33985(BlockPos var0) {
        for (ProjectileVector var6 : directions) {
            BlockState var7 = mc.level.getBlockState(BlockUtils.offset(var0,var6.x, var6.y, var6.z));
            if (var7.isAir()) {
                return false;
            }
        }

        return true;
    }

    public boolean method33986() {
        for (ProjectileVector var6 : directions) {
            BlockState var7 = mc.level.getBlockState(BlockUtils.offset(this.toBlockPos(),var6.x, var6.y, var6.z));
            if (var7.getBlock() == Blocks.WATER || var7.getBlock() == Blocks.LAVA) {
                return true;
            }
        }

        for (ProjectileVector var11 : directions) {
            BlockState var12 = mc.level.getBlockState(BlockUtils.offset(this.toBlockPos(),var11.x, var11.y + 1.0, var11.z));
            if (var12.getBlock() == Blocks.WATER || var12.getBlock() == Blocks.LAVA) {
                return true;
            }
        }

        return mc.level.getBlockState(this.toBlockPos().above(2)).getBlock() instanceof FallingBlock
                && !this.field41869.collidedPositions.contains(this.toBlockPos().above(2).asLong());
    }

    public boolean method33987() {
        return this.method33986()
                || mc.level.getBlockState(this.toBlockPos()).getBlock() == Blocks.BEDROCK
                || mc.level.getBlockState(this.toBlockPos().above()).getBlock() == Blocks.BEDROCK;
    }

    public boolean method33988(BlockPos var1) {
        return !this.field41869.collidedPositions.contains(var1.asLong())
                ? mc.level.getBlockState(var1).getCollisionShape(mc.level, var1).isEmpty()
                : true;
    }

    public boolean method33989() {
        return this.method33990(this.toBlockPos());
    }

    public boolean method33990(BlockPos var1) {
        if (!this.field41869.collidedPositions.contains(this.toBlockPos().asLong())) {
                VoxelShape var4 = mc.level.getBlockState(var1).getCollisionShape(mc.level, var1);
            if (var4.isEmpty()) {
                return false;
            } else {
                AABB var5 = var4.bounds();
                return var5.getYsize() >= 0.9 && var5.getYsize() <= 1.0;
            }
        } else {
            return false;
        }
    }

    public Vec3 getPosition() {
        return new Vec3(this.x,this.y,this.z);
    }
}
