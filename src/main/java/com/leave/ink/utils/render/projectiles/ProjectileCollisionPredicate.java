package com.leave.ink.utils.render.projectiles;
import com.google.common.base.Predicate;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.rotation.vector.Vector3d;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.phys.AABB;

public class ProjectileCollisionPredicate implements Predicate<Entity> {
    public final float collisionRadius;
    public final Vector3d startVec;
    public final Vector3d endVec;
    public final ProjectileType projectileType;

    public ProjectileCollisionPredicate(ProjectileType var1, float var2, Vector3d var3, Vector3d var4) {
        this.projectileType = var1;
        this.collisionRadius = var2;
        this.startVec = var3;
        this.endVec = var4;
    }

    @Override
    public boolean apply(Entity entity) {
        AABB expandedBoundingBox = entity.getBoundingBox().expandTowards(collisionRadius, collisionRadius, collisionRadius);
        boolean intersects = expandedBoundingBox.intersects(MathUtils.wrapVec3(startVec) , MathUtils.wrapVec3(endVec));
/*        if (intersects) {
            System.out.println("Got it" + (entity != null && entity.canBeCollidedWith() && intersects));
        }*/
        return entity != null && (entity instanceof LivingEntity) && intersects;
    }
}