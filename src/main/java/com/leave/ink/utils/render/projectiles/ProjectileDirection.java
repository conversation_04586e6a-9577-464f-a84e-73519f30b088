package com.leave.ink.utils.render.projectiles;



import org.joml.Vector3i;

import java.util.List;

public enum ProjectileDirection {
    NONE(null),
    LEFT(new Vector3i(-1, 0, 0)),
    RIGHT(new Vector3i(1, 0, 0)),
    FORWARD(new Vector3i(0, 0, -1)),
    BACKWARDS(new Vector3i(0, 0, 1)),
    CENTER(new Vector3i(0, 0, 0)),
    UP(new Vector3i(0, 1, 0)),
    DOWN(new Vector3i(0, -1, 0));

    public final Vector3i directionVector;

    ProjectileDirection(Vector3i var3) {
        this.directionVector = var3;
    }

    public static List<ProjectileDirection> getExtendedDirections() {
        return new ExtendedProjectileDirectionList();
    }

    public static List<ProjectileDirection> getBasicDirections() {
        return new ProjectileDirectionList();
    }
}