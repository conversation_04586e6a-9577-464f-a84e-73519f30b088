package com.leave.ink.utils.render.projectiles;


import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.misc.MathUtils;
import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import com.leave.ink.utils.rotation.vector.Vector3d;
import net.minecraft.client.Minecraft;
import net.minecraft.client.Timer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.item.BowItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;

import java.util.ArrayList;
import java.util.List;

public enum ProjectileType implements IMinecraft {
    BOW(Items.BOW, 0.0F, 3.0F, 0.0F),
    SNOWBALL(Items.SNOWBALL, 0.0F, 1.875F, 0.0F),
    ENDER_PEARL(Items.ENDER_PEARL, 0.0F, 1.875F, 0.0F),
    EGG(Items.EGG, 0.0F, 1.875F, 0.0F),
    SPLASH_POTION(Items.SPLASH_POTION, 0.0F, 0.5F, 0.0F),
    EXPERIENCE_BOTTLE(Items.EXPERIENCE_BOTTLE, 0.0F, 0.6F, 0.0F),
    TRIDENT(Items.TRIDENT, 0.0F, 2.5F, 0.0F);

    private final Item item;
    private final float posX;
    private final float posY;
    private final float posZ;
    public double traceX;
    public double traceY;
    public double traceZ;
    public float traceXOffset;
    public float traceYOffset;
    public float traceZOffset;
    public BlockHitResult rayTraceResult;
    public Entity hitEntity;

    ProjectileType(Item item, float posX, float posY, float posZ) {
        this.item = item;
        this.posX = posX;
        this.posY = posY;
        this.posZ = posZ;
    }

    public float getPosY() {
        if (!this.item.equals(Items.BOW)) {
            return this.posY;
        } else {
            return !(this.posY * BowItem.getPowerForTime(mc.player.getUseItemRemainingTicks()) > 0.0F)
                    ? BowItem.getPowerForTime(20)
                    : BowItem.getPowerForTime(mc.player.getUseItemRemainingTicks());
        }
    }

    public float getPosX() {
        return this.posX;
    }

    public float getPosZ() {
        return this.posZ;
    }

    public Item getItem() {
        return this.item;
    }

    public static ProjectileType getProjectileThingyForItem(Item item) {
        for (ProjectileType var6 : values()) {
            if (var6.getItem().equals(item)) {
                return var6;
            }
        }

        return null;
    }

    public List<TracePoint> getTraceThings() {
        ArrayList<TracePoint> list = new ArrayList<>();
		assert  mc.player != null;
		float var4 = (float)Math.toRadians( mc.player.getYRot());
        float var5 = (float)Math.toRadians( mc.player.getXRot());
        Timer t = ObfuscationReflectionHelper.getPrivateValue(Minecraft.class, mc, "timer");
        if(t == null) {
            return null;

        }
        double var6 =  mc.player.xOld
                + ( mc.player.getX() -  mc.player.xOld)
                * (double)  t.partialTick;
        double var8 =  mc.player.yOld
                + ( mc.player.getY() -  mc.player.yOld)
                * (double)  t.partialTick;
        double var10 =  mc.player.zOld
                + ( mc.player.getZ() -  mc.player.zOld)
                * (double)  t.partialTick;
        this.traceX = var6;
        this.traceY = var8 + (double)  mc.player.getEyeHeight() - 0.1F;
        this.traceZ = var10;
        float var12 = Math.min(20.0F, (float)(72000 -  mc.player.getUseItemRemainingTicks()) +  mc.getPartialTick()) / 20.0F;
        this.traceXOffset = (float) (-Math.sin(var4) * Math.cos(var5) * this.posY * var12);
        this.traceYOffset = (float) (-Math.sin(var5) * this.posY * var12);
        this.traceZOffset = (float) (Math.cos(var4) * Math.cos(var5) * this.posY * var12);
        this.rayTraceResult = null;
        this.hitEntity = null;
        list.add(new TracePoint(this.traceX, this.traceY, this.traceZ));

        while (this.rayTraceResult == null && this.hitEntity == null && this.traceY > 0.0) {
            Vector3d startVec = new Vector3d(this.traceX, this.traceY, this.traceZ);
            Vector3d endVec = new Vector3d(
                    this.traceX + (double)this.traceXOffset, this.traceY + (double)this.traceYOffset, this.traceZ + (double)this.traceZOffset
            );
            float size = (float)(!(this.item instanceof BowItem) ? 0.25 : 0.3);
            AABB boundingBox = new AABB(
                    this.traceX - (double)size,
                    this.traceY - (double)size,
                    this.traceZ - (double)size,
                    this.traceX + (double)size,
                    this.traceY + (double)size,
                    this.traceZ + (double)size
            );
			List<Entity> entities =  mc
                    .level
                    .getEntities(
                             mc.player,
                            boundingBox.move(
                                    this.traceXOffset,
                                    this.traceYOffset,
                                    this.traceZOffset
                            ).inflate(1.0, 1.0, 1.0),
                          new ProjectileCollisionPredicate(this, size, startVec, endVec)
                    );
            if (!entities.isEmpty()) {
                for (Entity entity : entities) {
                    this.hitEntity = entity;
                }
                break;
            }

            BlockHitResult trace =  mc
                    .level
                    .clip(
                            new ClipContext(
                                    MathUtils.wrapVec3(startVec), MathUtils.wrapVec3(endVec),
                                    ClipContext.Block.COLLIDER, ClipContext.Fluid.NONE,
                                     mc.player
                            )
                    );
            if (trace != null && trace.getType() != HitResult.Type.MISS) {
                this.rayTraceResult = trace;
                this.traceX = this.rayTraceResult.getLocation().x;
                this.traceY = this.rayTraceResult.getLocation().y;
                this.traceZ = this.rayTraceResult.getLocation().z;
                list.add(new TracePoint(this.traceX, this.traceY, this.traceZ));
                break;
            }

            float offsetMultiplier = 0.99F;
            float yOffsetDecrement = 0.05F;
            this.traceX = this.traceX + (double)this.traceXOffset;
            this.traceY = this.traceY + (double)this.traceYOffset;
            this.traceZ = this.traceZ + (double)this.traceZOffset;
            list.add(new TracePoint(this.traceX, this.traceY, this.traceZ));
            this.traceXOffset *= offsetMultiplier;
            this.traceYOffset *= offsetMultiplier;
            this.traceZOffset *= offsetMultiplier;
            this.traceYOffset -= yOffsetDecrement;
        }

        return list;
    }
}
