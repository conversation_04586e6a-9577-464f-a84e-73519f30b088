package com.leave.ink.utils.render;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.modules.combat.KillAura;
import com.leave.ink.features.module.modules.render.HitBubbles;
import com.leave.ink.features.module.modules.render.SkyParticles;

import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.render.engine.CaptureMark;
import com.leave.ink.utils.render.engine.Render3DEngine;
import com.leave.ink.utils.animation.Animation;
import com.leave.ink.utils.animation.Direction;
import com.leave.ink.utils.animation.impl.DecelerateAnimation;
import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.platform.Lighting;
import com.mojang.blaze3d.platform.NativeImage;
import com.mojang.blaze3d.platform.Window;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import com.mojang.math.Axis;
import net.minecraft.client.Camera;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.LevelRenderer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.client.renderer.texture.DynamicTexture;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.client.renderer.texture.TextureAtlas;
import net.minecraft.client.resources.model.BakedModel;
import net.minecraft.core.BlockPos;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemDisplayContext;
import net.minecraft.world.item.ItemStack;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.world.phys.AABB;

import org.joml.Matrix4f;
import org.joml.Quaternionf;
import org.joml.Vector3f;
import org.lwjgl.opengl.GL11;
import javax.annotation.Nullable;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.HashMap;

@SuppressWarnings("all")
public class RenderUtils implements IMinecraft {
    public RenderUtils() {
        EventManager.register(this);

    }
    public static void rotate(PoseStack matrices,double angle, float x, float y, float z) {
        matrices.mulPose(Axis.of(new Vector3f(x, y, z)).rotationDegrees((float) angle));
    }
    public static void setupRender() {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
    }

    public static void endRender() {

        RenderSystem.defaultBlendFunc();
        RenderSystem.disableBlend();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
    }
    public static void renderEntityInHUD(PoseStack poseStack, int x, int y, float scale, LivingEntity entity) {
        Minecraft mc = Minecraft.getInstance();
        EntityRenderDispatcher dispatcher = mc.getEntityRenderDispatcher();

        poseStack.pushPose();

        // 移动到 HUD 的绘制位置
        poseStack.translate(x, y, 1050);
        poseStack.scale(1.0F, 1.0F, -1.0F);

        Matrix4f poseMatrix = poseStack.last().pose();
        Quaternionf rotation = new Quaternionf().rotateX((float) Math.toRadians(180)); // 上下翻转
        poseMatrix.rotate(rotation);

        // 缩放
        poseStack.scale(scale, scale, scale);

        // 设置 entity 的旋转角度
//        entity.tickCount = mc.player.tickCount;
//        entity.yBodyRot = 0;
//        entity.setYRot(0);
//        entity.setXRot(0);
//        entity.yHeadRot = 0;
//        entity.yHeadRotO = 0;

        // 设置光照环境
//        Lighting.setupForEntityInInventory();

        // 渲染实体
        dispatcher.setRenderShadow(false);
        dispatcher.render(entity, 0, 0, 0, 0.0F, 1.0F, poseStack, mc.renderBuffers().bufferSource(), 15728880);
        dispatcher.setRenderShadow(true);

        mc.renderBuffers().bufferSource().endBatch();

//        Lighting.setupFor3DItems();

        poseStack.popPose();
    }


    public static int Astolfo(int var2) {
        double v1 = Math.ceil(System.currentTimeMillis() + (var2 * 109L)) / 5;
        return Color.getHSBColor((double) ((float) ((v1 %= 360.0) / 360.0)) < 0.5 ? -((float) (v1 / 360.0)) : (float) (v1 / 360.0), 0.5F, 1.0F).getRGB();
    }

    public static void glColor(final Color color) {
        final float red = color.getRed() / 255F;
        final float green = color.getGreen() / 255F;
        final float blue = color.getBlue() / 255F;
        final float alpha = color.getAlpha() / 255F;

        RenderSystem.setShaderColor(red, green, blue, alpha);
    }

    public static void drawLineBox(PoseStack stack, AABB aabb, Color color) {
        Camera camera = mc.gameRenderer.getMainCamera();
        double camX = camera.getPosition().x;
        double camY = camera.getPosition().y;
        double camZ = camera.getPosition().z;
        AABB box = aabb.move(-camX, -camY, -camZ);
        GL11.glEnable(GL11.GL_POLYGON_OFFSET_FILL);
        GL11.glPolygonOffset(1f, -1000000F);
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();
        VertexConsumer vertexConsumer = bufferSource.getBuffer(RenderType.LINES);
        stack.pushPose();
        LevelRenderer.renderLineBox(stack, vertexConsumer, box, color.getRed() / 255F, color.getGreen() / 255F, color.getBlue() / 255F, color.getAlpha() / 255F);
        stack.popPose();
        bufferSource.endBatch();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        GL11.glPolygonOffset(1f, 1000000F);
        GL11.glDisable(GL11.GL_POLYGON_OFFSET_FILL);
    }

    public static void drawLineBox(PoseStack stack, BlockPos blockPos, Color color) {
        Camera camera = mc.gameRenderer.getMainCamera();
        double camX = camera.getPosition().x;
        double camY = camera.getPosition().y;
        double camZ = camera.getPosition().z;
        AABB box = new AABB(blockPos).move(-camX, -camY, -camZ);
        GL11.glEnable(GL11.GL_POLYGON_OFFSET_FILL);
        GL11.glPolygonOffset(1f, -1000000F);
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();
        VertexConsumer vertexConsumer = bufferSource.getBuffer(RenderType.LINES);
        stack.pushPose();
        LevelRenderer.renderLineBox(stack, vertexConsumer, box, color.getRed() / 255F, color.getGreen() / 255F, color.getBlue() / 255F, color.getAlpha() / 255F);
        stack.popPose();
        bufferSource.endBatch();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        GL11.glPolygonOffset(1f, 1000000F);
        GL11.glDisable(GL11.GL_POLYGON_OFFSET_FILL);
    }

    public static double easeInQuint(double x) {
        return x * x * x * x * x;
    }

    public static Color healthColor(float hp, float maxHP, int alpha) {
        int pct = (int) ((hp / maxHP) * 255F);
        return new Color(Math.max(Math.min(255 - pct, 255), 0), Math.max(Math.min(pct, 255), 0), 0, alpha);
    }

    public static void glColor(final int red, final int green, final int blue, final int alpha) {
        RenderSystem.setShaderColor(red / 255F, green / 255F, blue / 255F, alpha / 255F);
    }

    public static Color injectAlpha(final Color color, final int alpha) {
        return new Color(color.getRed(), color.getGreen(), color.getBlue(), Mth.clamp(alpha, 0, 255));
    }

    public static Color fade(Color color) {
        return fade(color, 2, 100);
    }

    public static Color fade(Color color, int index, int count) {
        float[] hsb = new float[3];
        Color.RGBtoHSB(color.getRed(), color.getGreen(), color.getBlue(), hsb);
        float brightness = Math.abs(((float) (System.currentTimeMillis() % 2000L) / 1000.0f + (float) index / (float) count * 2.0f) % 2.0f - 1.0f);
        brightness = 0.5f + 0.5f * brightness;
        hsb[2] = brightness % 2.0f;
        return new Color(Color.HSBtoRGB(hsb[0], hsb[1], hsb[2]));
    }

    public static int getGradientColor(float ratio, int startColor, int endColor) {
        int startRed = (startColor >> 16) & 0xFF;
        int startGreen = (startColor >> 8) & 0xFF;
        int startBlue = startColor & 0xFF;

        int endRed = (endColor >> 16) & 0xFF;
        int endGreen = (endColor >> 8) & 0xFF;
        int endBlue = endColor & 0xFF;

        int red = (int) (startRed + ratio * (endRed - startRed));
        int green = (int) (startGreen + ratio * (endGreen - startGreen));
        int blue = (int) (startBlue + ratio * (endBlue - startBlue));

        return (0xFF << 24) | (red << 16) | (green << 8) | blue; // 透明度固定为 FF
    }

    public static Color getGradientOffset(Color color1, Color color2, double offset) {
        if (offset > 1) {
            double left = offset % 1;
            long off = (long) offset;
            offset = off % 2 == 0 ? left : 1 - left;

        }
        double inverse_percent = 1 - offset;
        int redPart = (int) (color1.getRed() * inverse_percent + color2.getRed() * offset);
        int greenPart = (int) (color1.getGreen() * inverse_percent + color2.getGreen() * offset);
        int bluePart = (int) (color1.getBlue() * inverse_percent + color2.getBlue() * offset);
        return new Color(redPart, greenPart, bluePart);
    }

    public static int astolfoRainbow(int delay, int offset, int index) {
        // 获取当前系统时间，加上根据index乘以delay后的偏移量，并向上取整
        double timeValue = Math.ceil(System.currentTimeMillis() + ((long) delay * index)) / offset;
        // 将时间值映射到0到360之间，表示色相角度
        double hueDegrees = timeValue % 360.0;
        // 将色相角度转换为0到1之间的比例
        float hueFraction = (float) (hueDegrees / 360.0);
        // 根据需求进行调整，这里如果色相比例小于0.5则取负值（可根据实际效果调整）
        hueFraction = hueFraction < 0.5 ? -hueFraction : hueFraction;
        // 利用HSB颜色模型生成RGB颜色，设置饱和度为0.5，亮度为1.0
        return Color.getHSBColor(hueFraction, 0.5F, 1.0F).getRGB();

    }

    public static double interpolate(double oldValue, double newValue, double interpolationValue) {
        return (oldValue + (newValue - oldValue) * interpolationValue);
    }

    public static float interpolateFloat(float oldValue, float newValue, double interpolationValue) {
        return (float) interpolate(oldValue, newValue, (float) interpolationValue);
    }

    public static int interpolateInt(int oldValue, int newValue, double interpolationValue) {
        return (int) interpolate(oldValue, newValue, (float) interpolationValue);
    }

    public static void drawBubble(PoseStack matrices, float angle, float factor) {
        setupRender();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE);
        RenderSystem.setShaderTexture(0, new ResourceLocation("/hitbubble.png"));
        matrices.mulPose(Axis.ZP.rotationDegrees(angle));
        float scale = factor * 2f;
        renderGradientTexture(matrices, -scale / 2, -scale / 2, scale, scale, 0, 0, 128, 128, 128, 128, applyOpacity(getColor(270), 1f - factor), applyOpacity(getColor(0), 1f - factor), applyOpacity(getColor(180), 1f - factor), applyOpacity(getColor(90), 1f - factor));
        endRender();
    }

    public static Color applyOpacity(Color color, float opacity) {
        opacity = Math.min(1, Math.max(0, opacity));
        return new Color(color.getRed(), color.getGreen(), color.getBlue(), (int) (color.getAlpha() * opacity));
    }

    public static int applyOpacity(int color_int, float opacity) {
        opacity = Math.min(1, Math.max(0, opacity));
        Color color = new Color(color_int);
        return new Color(color.getRed(), color.getGreen(), color.getBlue(), (int) (color.getAlpha() * opacity)).getRGB();
    }

    public static Color getColor(int count) {
        return new Color(71, 106, 128);
    }
    public static void drawHead(GuiGraphics guiGraphics, ResourceLocation skin, int width, int height) {
        RenderSystem.setShaderColor(1F, 1F, 1F, 1F);
        RenderSystem.setShaderTexture(0, skin);
        guiGraphics.blit(skin, 0, 0, width, height, 8, 8, 8, 8, 64, 64);
    }
    public static void renderGradientTexture(PoseStack matrices, double x0, double y0, double width, double height, float u, float v, double regionWidth, double regionHeight, double textureWidth, double textureHeight, Color c1, Color c2, Color c3, Color c4) {
        RenderSystem.setShader(GameRenderer::getPositionTexColorShader);
        BufferBuilder buffer = Tesselator.getInstance().getBuilder();
        buffer.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_TEX_COLOR);
        renderGradientTextureInternal(buffer, matrices, x0, y0, width, height, u, v, regionWidth, regionHeight, textureWidth, textureHeight, c1, c2, c3, c4);
        endBuilding(Tesselator.getInstance());
    }

    public static void renderGradientTextureInternal(BufferBuilder buff, PoseStack matrices, double x0, double y0, double width, double height, float u, float v, double regionWidth, double regionHeight, double textureWidth, double textureHeight, Color c1, Color c2, Color c3, Color c4) {
        double x1 = x0 + width;
        double y1 = y0 + height;
        double z = 0;
        Matrix4f matrix = matrices.last().pose();
        buff.vertex(matrix, (float) x0, (float) y1, (float) z).uv((u) / (float) textureWidth, (v + (float) regionHeight) / (float) textureHeight).color(c1.getRGB()).endVertex();
        buff.vertex(matrix, (float) x1, (float) y1, (float) z).uv((u + (float) regionWidth) / (float) textureWidth, (v + (float) regionHeight) / (float) textureHeight).color(c2.getRGB()).endVertex();
        buff.vertex(matrix, (float) x1, (float) y0, (float) z).uv((u + (float) regionWidth) / (float) textureWidth, (v) / (float) textureHeight).color(c3.getRGB()).endVertex();
        buff.vertex(matrix, (float) x0, (float) y0, (float) z).uv((u) / (float) textureWidth, (v + 0.0F) / (float) textureHeight).color(c4.getRGB()).endVertex();
    }

    public static void endBuilding(Tesselator tesselator) {
        tesselator.end();
    }

    public static double roundToDecimal(double n, int point) {
        if (point == 0) {
            return Math.floor(n);
        }
        double factor = Math.pow(10, point);
        return Math.round(n * factor) / factor;
    }

//    public static void renderEntityBoundingBox(PoseStack poseStack, LivingEntity entity, int color, boolean damage) {
//        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
//        double x = entity.xOld + (entity.getX() - entity.xOld) * (double) mc.getFrameTime() - renderManager.camera.getPosition().x();
//        double y = entity.yOld + (entity.getY() - entity.yOld) * (double) mc.getFrameTime() - renderManager.camera.getPosition().y();
//        double z = entity.zOld + (entity.getZ() - entity.zOld) * (double) mc.getFrameTime() - renderManager.camera.getPosition().z();
//        float scale = 0.03F;
//        if (entity instanceof Player && damage && entity.hurtTime > 0) {
//            color = Color.RED.getRGB();
//        }
//
//        RenderSystem.disableDepthTest();
//        poseStack.pushPose();
//        poseStack.translate(x, y, z);
//        poseStack.mulPose(new Quaternionf(0.0F, -renderManager.camera.getYRot(), 0.0F, true));
//        poseStack.scale(scale, scale, scale);
//        int outline = Color.BLACK.getRGB();
//        drawRect(poseStack, -20, -1, -26, 75, outline);
//        drawRect(poseStack, 20, -1, 26, 75, outline);
//        drawRect(poseStack, -20, -1, 21, 5, outline);
//        drawRect(poseStack, -20, 70, 21, 75, outline);
//        if (color != 0) {
//            drawRect(poseStack, -21, 0, -25, 74, color);
//            drawRect(poseStack, 21, 0, 25, 74, color);
//            drawRect(poseStack, -21, 0, 24, 4, color);
//            drawRect(poseStack, -21, 71, 25, 74, color);
//        } else {
//            int startColor = rainbowDraw(2L, 0L);
//            int endColor = rainbowDraw(2L, 1000L);
//            drawGradientRect(poseStack, -21, 0, -25, 74, startColor, endColor);
//            drawGradientRect(poseStack, 21, 0, 25, 74, startColor, endColor);
//            drawRect(poseStack, -21, 0, 21, 4, endColor);
//            drawRect(poseStack, -21, 71, 21, 74, startColor);
//        }
//        RenderSystem.enableDepthTest();
//        poseStack.popPose();
//    }

    public static int rainbowDraw(long speed, long... delay) {
        long time = System.currentTimeMillis() + (delay.length > 0 ? delay[0] : 0L);
        return Color.getHSBColor((float) (time % (15000L / speed)) / (15000.0F / (float) speed), 1.0F, 1.0F).getRGB();
    }

    public static void drawGradientRect(PoseStack poseStack, int left, int top, int right, int bottom, int startColor, int endColor) {
        Matrix4f matrix = poseStack.last().pose();
        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder bufferbuilder = tessellator.getBuilder();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);

        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        float startAlpha = (float) (startColor >> 24 & 0xFF) / 255.0F;
        float startRed = (float) (startColor >> 16 & 0xFF) / 255.0F;
        float startGreen = (float) (startColor >> 8 & 0xFF) / 255.0F;
        float startBlue = (float) (startColor & 0xFF) / 255.0F;
        float endAlpha = (float) (endColor >> 24 & 0xFF) / 255.0F;
        float endRed = (float) (endColor >> 16 & 0xFF) / 255.0F;
        float endGreen = (float) (endColor >> 8 & 0xFF) / 255.0F;
        float endBlue = (float) (endColor & 0xFF) / 255.0F;
        bufferbuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);
        bufferbuilder.vertex(matrix, (float) right, (float) top, 0.0F).color(startRed, startGreen, startBlue, startAlpha).endVertex();
        bufferbuilder.vertex(matrix, (float) left, (float) top, 0.0F).color(startRed, startGreen, startBlue, startAlpha).endVertex();
        bufferbuilder.vertex(matrix, (float) left, (float) bottom, 0.0F).color(endRed, endGreen, endBlue, endAlpha).endVertex();
        bufferbuilder.vertex(matrix, (float) right, (float) bottom, 0.0F).color(endRed, endGreen, endBlue, endAlpha).endVertex();
        tessellator.end();
        RenderSystem.disableBlend();

    }


    public static void drawRect(PoseStack poseStack, double left, double top, double right, double bottom, int color) {
        if (left < right) {
            double j = left;
            left = right;
            right = j;
        }

        if (top < bottom) {
            double j = top;
            top = bottom;
            bottom = j;
        }

        Matrix4f matrix = poseStack.last().pose();
        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder bufferbuilder = tessellator.getBuilder();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        RenderSystem.enableBlend();
        RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
        bufferbuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);
        bufferbuilder.vertex(matrix, (float) left, (float) bottom, 0.0F).color(color >> 16 & 0xFF, color >> 8 & 0xFF, color & 0xFF, color >> 24 & 0xFF).endVertex();
        bufferbuilder.vertex(matrix, (float) right, (float) bottom, 0.0F)
                .color(color >> 16 & 0xFF, color >> 8 & 0xFF, color & 0xFF, color >> 24 & 0xFF)
                .endVertex();
        bufferbuilder.vertex(matrix, (float) right, (float) top, 0.0F).color(color >> 16 & 0xFF, color >> 8 & 0xFF, color & 0xFF, color >> 24 & 0xFF).endVertex();
        bufferbuilder.vertex(matrix, (float) left, (float) top, 0.0F).color(color >> 16 & 0xFF, color >> 8 & 0xFF, color & 0xFF, color >> 24 & 0xFF).endVertex();
        tessellator.end();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.disableBlend();
    }


    public static void renderItemStack(PoseStack poseStack, ItemStack stack, int x, int y) {
        if (!stack.isEmpty()) {
            poseStack.pushPose();
            
            // 使用原始的renderAndDecorateItem方法来确保物品正确渲染
            renderAndDecorateItem(poseStack, stack, x, y);
            renderGuiItemDecorations(poseStack, mc.font, stack, x, y);
            
            poseStack.popPose();
        }
    }

    public static void drawOutline(GuiGraphics poseStack, double x, double y, double width, double height, int color) {
        hLine(poseStack, x - 1, x + width, y - 1, color);
        hLine(poseStack, x - 1, x + width, y + height, color);
        vLine(poseStack, x - 1, y - 1, y + height, color);
        vLine(poseStack, x + width, y - 1, y + height, color);
    }

    public static void hLine(GuiGraphics p_93223_, double p_93156_, double p_93157_, double p_93158_, double p_93159_) {
        if (p_93157_ < p_93156_) {
            double i = p_93156_;
            p_93156_ = p_93157_;
            p_93157_ = i;
        }

        p_93223_.fill((int) p_93156_, (int) p_93158_, (int) (p_93157_ + 1), (int) (p_93158_ + 1), (int) p_93159_);
    }

    public static void vLine(GuiGraphics p_93223_, double p_93224_, double p_93225_, double p_93226_, double p_93227_) {
        if (p_93226_ < p_93225_) {
            double i = p_93225_;
            p_93225_ = p_93226_;
            p_93226_ = i;
        }

        p_93223_.fill((int) p_93224_, (int) (p_93225_ + 1), (int) (p_93224_ + 1), (int) p_93226_, (int) p_93227_);
    }

    public static void drawESP(GuiGraphics guiGraphics, Entity e, int type, double expand, double shift, int color, boolean damage) {
        if (e instanceof LivingEntity) {
            double x = Mth.lerp(Minecraft.getInstance().getFrameTime(), e.xOld, e.getX()) - Minecraft.getInstance().getEntityRenderDispatcher().camera.getPosition().x;
            double y = Mth.lerp(Minecraft.getInstance().getFrameTime(), e.yOld, e.getY()) - Minecraft.getInstance().getEntityRenderDispatcher().camera.getPosition().y;
            double z = Mth.lerp(Minecraft.getInstance().getFrameTime(), e.zOld, e.getZ()) - Minecraft.getInstance().getEntityRenderDispatcher().camera.getPosition().z;
            float d = (float) expand / 40.0F;

            if (e instanceof Player && damage && ((Player) e).hurtTime != 0) {
                color = Color.RED.getRGB();
            }
            PoseStack poseStack = guiGraphics.pose();
            poseStack.pushPose();

            if (type == 3) {
                poseStack.translate(x, y - 0.2D, z);
                poseStack.mulPose(Axis.YP.rotationDegrees(-Minecraft.getInstance().getEntityRenderDispatcher().camera.getYRot()));
                RenderSystem.disableDepthTest();
                poseStack.scale(0.03F + d, 0.03F + d, 0.03F + d);
                int outline = Color.black.getRGB();
                guiGraphics.fill(-20, 0, -26, 20, outline);
                guiGraphics.fill(20, 0, 26, 20, outline);
                guiGraphics.fill(-20, 75, -26, 55, outline);
                guiGraphics.fill(20, 75, 26, 55, outline);
                guiGraphics.fill(20, 0, 6, 5, outline);
                guiGraphics.fill(20, 75, 6, 70, outline);
                guiGraphics.fill(-20, 0, -6, 5, outline);
                guiGraphics.fill(-20, 70, -6, 75, outline);

                if (color != 0) {
                    guiGraphics.fill(-21, 1, -25, 19, color);
                    guiGraphics.fill(21, 1, 25, 19, color);
                    guiGraphics.fill(-21, 74, -25, 56, color);
                    guiGraphics.fill(21, 74, 25, 56, color);
                    guiGraphics.fill(21, 1, 7, 4, color);
                    guiGraphics.fill(21, 74, 7, 71, color);
                    guiGraphics.fill(-21, 1, -7, 4, color);
                    guiGraphics.fill(-21, 71, -7, 74, color);
                } else {
                    int st = rainbowDraw(2L, 0L);
                    int en = rainbowDraw(2L, 1000L);
                    drawGradientRect(poseStack, -21, 0, -25, 74, st, en);
                    drawGradientRect(poseStack, 21, 0, 25, 74, st, en);
                    guiGraphics.fill(-21, 0, 21, 4, en);
                    guiGraphics.fill(-21, 71, 21, 74, st);
                }

                RenderSystem.enableDepthTest();
            } else if (type == 4) {
                LivingEntity livingEntity = (LivingEntity) e;
                double healthRatio = livingEntity.getHealth() / livingEntity.getMaxHealth();
                int barHeight = (int) (74.0D * healthRatio);
                int healthColor = healthRatio < 0.3D ? Color.RED.getRGB() : (healthRatio < 0.5D ? Color.ORANGE.getRGB() : (healthRatio < 0.7D ? Color.YELLOW.getRGB() : Color.GREEN.getRGB()));
                poseStack.translate(x, y - 0.2D, z);
                poseStack.mulPose(Axis.YP.rotationDegrees(-Minecraft.getInstance().getEntityRenderDispatcher().camera.getYRot()));
                RenderSystem.disableDepthTest();
                poseStack.scale(0.03F + d, 0.03F + d, 0.03F + d);
                int xPos = (int) (21.0D + shift * 2.0D);
                guiGraphics.fill(xPos, -1, xPos + 5, 75, Color.BLACK.getRGB());
                guiGraphics.fill(xPos + 1, barHeight, xPos + 4, 74, Color.DARK_GRAY.getRGB());
                guiGraphics.fill(xPos + 1, 0, xPos + 4, barHeight, healthColor);
                RenderSystem.enableDepthTest();
            } else if (type == 6) {
                draw3DPoint(poseStack, x, y, z, 0.7D, 45, 1.5F, color, color == 0);
            } else {
                if (color == 0) {
                    color = rainbowDraw(2L, 0L);
                }
                float a = (color >> 24 & 255) / 255.0F;
                float r = (color >> 16 & 255) / 255.0F;
                float g = (color >> 8 & 255) / 255.0F;
                float b = (color & 255) / 255.0F;
                if (type == 5) {
                    poseStack.translate(x, y - 0.2D, z);
                    poseStack.mulPose(Axis.YP.rotationDegrees(-Minecraft.getInstance().getEntityRenderDispatcher().camera.getYRot()));
                    RenderSystem.disableDepthTest();
                    poseStack.scale(0.03F + d, 0.03F, 0.03F + d);
                    draw2DPoint(poseStack, 0.0D, 95.0D, 10, 3, Color.BLACK.getRGB());
                    for (int i = 0; i < 6; ++i) {
                        draw2DPoint(poseStack, 0.0D, 95 + (10 - i), 3, 4, Color.BLACK.getRGB());
                    }
                    for (int i = 0; i < 7; ++i) {
                        draw2DPoint(poseStack, 0.0D, 95 + (10 - i), 2, 4, color);
                    }
                    draw2DPoint(poseStack, 0.0D, 95.0D, 8, 3, color);
                    RenderSystem.enableDepthTest();
                } else {
                    AABB bbox = e.getBoundingBox().inflate(0.1D + expand);
                    AABB axis = new AABB(bbox.minX - e.getX() + x, bbox.minY - e.getY() + y, bbox.minZ - e.getZ() + z, bbox.maxX - e.getX() + x, bbox.maxY - e.getY() + y, bbox.maxZ - e.getZ() + z);
                    RenderSystem.enableBlend();

                    RenderSystem.disableDepthTest();
                    RenderSystem.depthMask(false);
                    RenderSystem.lineWidth(2.0F);
                    RenderSystem.setShaderColor(r, g, b, a);
                    if (type == 1) {
                        drawSelectionBoundingBox(poseStack, axis);
                    } else if (type == 2) {
                        drawBoundingBox(poseStack, axis, r, g, b);
                    }

                    RenderSystem.enableDepthTest();
                    RenderSystem.depthMask(true);
                    RenderSystem.disableBlend();
                }
            }
            poseStack.popPose();
        }
    }

    public static void drawSelectionBoundingBox(PoseStack poseStack, AABB boundingBox) {
        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder bufferBuilder = tessellator.getBuilder();

        bufferBuilder.begin(VertexFormat.Mode.LINE_STRIP, DefaultVertexFormat.POSITION);
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.minY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.minY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.minY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.minY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.minY, (float) boundingBox.minZ).endVertex();
        tessellator.end();

        bufferBuilder.begin(VertexFormat.Mode.LINE_STRIP, DefaultVertexFormat.POSITION);
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.maxY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.maxY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.maxY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.maxY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.maxY, (float) boundingBox.minZ).endVertex();
        tessellator.end();

        bufferBuilder.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION);
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.minY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.maxY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.minY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.maxY, (float) boundingBox.minZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.minY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.maxX, (float) boundingBox.maxY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.minY, (float) boundingBox.maxZ).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) boundingBox.minX, (float) boundingBox.maxY, (float) boundingBox.maxZ).endVertex();
        tessellator.end();
    }

    public static void NewCSGO(GuiGraphics guiGraphics, LivingEntity entity, int rgb) {
        PoseStack poseStack = guiGraphics.pose();
        poseStack.pushPose();
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
        float d = (float) 0 / 40.0F;
        int i;
        double shift = 1D;
        double r = entity.getHealth() / entity.getMaxHealth();
        int b = (int) (74.0D * r);
        int hc = r < 0.3D ? Color.red.getRGB() : (r < 0.5D ? Color.orange.getRGB() : (r < 0.7D ? Color.yellow.getRGB() : Color.green.getRGB()));
        poseStack.translate(
                entity.xOld + (entity.getX() - entity.xOld) * mc.getFrameTime() - renderManager.camera.getPosition().x,
                entity.yOld + (entity.getY() - entity.yOld) * mc.getFrameTime() - renderManager.camera.getPosition().y - 0.2,
                entity.zOld + (entity.getZ() - entity.zOld) * mc.getFrameTime() - renderManager.camera.getPosition().z
        );
        poseStack.mulPose(Axis.YP.rotationDegrees(-renderManager.camera.getYRot()));
        RenderSystem.disableDepthTest();
        poseStack.scale(0.03F + d, 0.03F + d, 0.03F + d);
        i = (int) (21.0D + shift * 2.0D);
        guiGraphics.fill(i, -1, i + 7, 75, Color.black.getRGB());
        guiGraphics.fill(i + 1, b, i + 6, 74, Color.DARK_GRAY.getRGB());
        guiGraphics.fill(i + 1, 0, i + 6, b, hc);
        int outline = Color.black.getRGB();
        guiGraphics.fill(-20, -1, -26, 75, outline);
        guiGraphics.fill(20, -1, 26, 75, outline);
        guiGraphics.fill(-20, -1, 21, 5, outline);
        guiGraphics.fill(-20, 70, 21, 75, outline);
        guiGraphics.fill(-21, 0, -25, 74, rgb);
        guiGraphics.fill(21, 0, 25, 74, rgb);
        guiGraphics.fill(-21, 0, 24, 4, rgb);
        guiGraphics.fill(-21, 71, 25, 74, rgb);
        RenderSystem.enableDepthTest();
        poseStack.popPose();
    }

    public static void drawBoundingBox(PoseStack poseStack, AABB abb, float r, float g, float b) {
        float a = 255F;

        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder bufferBuilder = tesselator.getBuilder();

        bufferBuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.minY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.maxY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.minY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.maxY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.minY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.maxY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.minY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.maxY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.maxY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.minY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.maxY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.minY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.maxY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.minY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.maxY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.minY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.maxY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.maxY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.maxY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.maxY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.minY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.minY, (float) abb.minZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.maxX, (float) abb.minY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        bufferBuilder.vertex(poseStack.last().pose(), (float) abb.minX, (float) abb.minY, (float) abb.maxZ).color(r, g, b, a).endVertex();
        tesselator.end();
    }

    public static void draw3DPoint(PoseStack poseStack, double x, double y, double z, double radius, int sides, float lineWidth, int color, boolean chroma) {
        float a = (float) (color >> 24 & 255) / 255.0F;
        float r = (float) (color >> 16 & 255) / 255.0F;
        float g = (float) (color >> 8 & 255) / 255.0F;
        float b = (float) (color & 255) / 255.0F;

        mc.gameRenderer.lightTexture().turnOffLightLayer();

        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.lineWidth(lineWidth);

        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder buffer = tessellator.getBuilder();

        buffer.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);

        long d = 0L;
        long ed = 15000L / (long) sides;
        long hed = ed / 2L;

        for (int i = 0; i < sides * 2; ++i) {
            if (chroma) {
                if (i % 2 != 0) {
                    if (i == 47) {
                        d = hed;
                    }
                    d += ed;
                }

                int c = rainbowDraw(2L, d);
                float r2 = (float) (c >> 16 & 255) / 255.0F;
                float g2 = (float) (c >> 8 & 255) / 255.0F;
                float b2 = (float) (c & 255) / 255.0F;
                buffer.vertex(poseStack.last().pose(), (float) (x + Math.cos(2 * Math.PI * i / sides + Math.toRadians(180.0D)) * radius), (float) y, (float) (z + Math.sin(2 * Math.PI * i / sides + Math.toRadians(180.0D)) * radius))
                        .color(r2, g2, b2, 1.0F).endVertex();
            } else {
                buffer.vertex(poseStack.last().pose(), (float) (x + Math.cos(2 * Math.PI * i / sides + Math.toRadians(180.0D)) * radius), (float) y, (float) (z + Math.sin(2 * Math.PI * i / sides + Math.toRadians(180.0D)) * radius))
                        .color(r, g, b, a).endVertex();
            }
        }

        tessellator.end();

        RenderSystem.disableBlend();
        RenderSystem.enableDepthTest();
        mc.gameRenderer.lightTexture().turnOnLightLayer();
    }

    public static void draw2DPoint(PoseStack poseStack, double x, double y, int radius, int sides, int color) {
        float a = (float) (color >> 24 & 255) / 255.0F;
        float r = (float) (color >> 16 & 255) / 255.0F;
        float g = (float) (color >> 8 & 255) / 255.0F;
        float b = (float) (color & 255) / 255.0F;
        RenderSystem.setShaderColor(r, g, b, a);
        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder bufferBuilder = tessellator.getBuilder();
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();

        bufferBuilder.begin(VertexFormat.Mode.TRIANGLE_FAN, DefaultVertexFormat.POSITION);

        for (int i = 0; i < sides; ++i) {
            double angle = 2 * Math.PI * i / sides + Math.toRadians(180.0D);
            bufferBuilder.vertex(poseStack.last().pose(), (float) (x + Math.sin(angle) * radius), (float) (y + Math.cos(angle) * radius), 0.0F).endVertex();
        }

        tessellator.end();

        RenderSystem.disableBlend();
    }

    public static void renderBoundingBox(PoseStack poseStack, AABB BoundingBox, float red, float green, float blue) {
        EntityRenderDispatcher dispatcher = mc.getEntityRenderDispatcher();
        double camX = dispatcher.camera.getPosition().x();
        double camY = dispatcher.camera.getPosition().y();
        double camZ = dispatcher.camera.getPosition().z();
        AABB boundingBox = BoundingBox.move(-camX, -camY, -camZ);

        GL11.glEnable(GL11.GL_POLYGON_OFFSET_FILL);
        GL11.glPolygonOffset(1f, -1000000F);
        RenderSystem.disableDepthTest();
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);

        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();
        VertexConsumer vertexConsumer = bufferSource.getBuffer(RenderType.LINES);
        poseStack.pushPose();
        LevelRenderer.renderLineBox(poseStack, vertexConsumer, boundingBox.minX, boundingBox.minY, boundingBox.minZ, boundingBox.maxX, boundingBox.maxY, boundingBox.maxZ, red, green, blue, 255.0F);
        poseStack.popPose();
        bufferSource.endBatch();
        RenderSystem.disableBlend();
        RenderSystem.enableDepthTest();
        GL11.glPolygonOffset(1f, 1000000F);
        GL11.glDisable(GL11.GL_POLYGON_OFFSET_FILL);
    }

    public static void renderBoundingBox(AABB aabb, Color color) {
        RenderSystem.disableDepthTest();
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);
        drawBoundingBoxLines(buffer, aabb, color);
        tesselator.end();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.disableBlend();
        RenderSystem.enableDepthTest();
    }

    private static void drawBoundingBoxLines(BufferBuilder buffer, AABB aabb, Color color) {
        float r = color.getRed() / 255, g = color.getGreen() / 255, b = color.getBlue() / 255, a = color.getAlpha() / 255;
        buffer.vertex(aabb.minX, aabb.minY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.minY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.minY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.minY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.minY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.minY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.minY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.minY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.maxY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.maxY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.maxY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.maxY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.maxY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.maxY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.maxY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.maxY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.minY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.maxY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.minY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.maxY, aabb.minZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.minY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.maxX, aabb.maxY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.minY, aabb.maxZ).color(r, g, b, a).endVertex();
        buffer.vertex(aabb.minX, aabb.maxY, aabb.maxZ).color(r, g, b, a).endVertex();
    }

    public static void drawTriangle(PoseStack poseStack, float cx, float cy, float r, float n, Color color) {
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        buffer.begin(VertexFormat.Mode.LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);
        double angleStep = 2 * Math.PI / n;
        float firstX = 0, firstY = 0;
        for (int i = 0; i <= n; i++) {
            double angle = i * angleStep;
            float x = (float) (cx + r * Math.cos(angle));
            float y = (float) (cy + r * Math.sin(angle));
            if (i == 0) {
                firstX = x;
                firstY = y;
            }
            buffer.vertex(poseStack.last().pose(), x, y, 0).color(color.getRGB()).endVertex();
        }

        buffer.vertex(poseStack.last().pose(), firstX, firstY, 0).color(color.getRGB()).endVertex();
        tesselator.end();
        RenderSystem.disableBlend();
    }

    public static void renderGuiItemDecorations(PoseStack stack, Font p_115170_, ItemStack p_115171_, int p_115172_, int p_115173_) {
        renderGuiItemDecorations(stack, p_115170_, p_115171_, p_115172_, p_115173_, null);
    }


    public static void renderGuiItemDecorations(PoseStack stack, Font p_115175_, ItemStack p_115176_, int p_115177_, int p_115178_, @Nullable String p_115179_) {

        if (!p_115176_.isEmpty()) {
            if (p_115176_.getCount() != 1 || p_115179_ != null) {
                String s = p_115179_ == null ? String.valueOf(p_115176_.getCount()) : p_115179_;
                stack.translate(0.0D, 0.0D, 200.0F);
                MultiBufferSource.BufferSource multibuffersource$buffersource = MultiBufferSource.immediate(Tesselator.getInstance().getBuilder());
                p_115175_.drawInBatch(s, (float) (p_115177_ + 19 - 2 - p_115175_.width(s)), (float) (p_115178_ + 6 + 3), 16777215, true, stack.last().pose(), multibuffersource$buffersource, Font.DisplayMode.NORMAL, 0, 15728880);
                multibuffersource$buffersource.endBatch();
            }

            if (p_115176_.isBarVisible()) {
                RenderSystem.disableDepthTest();

                RenderSystem.disableBlend();
                Tesselator tesselator = Tesselator.getInstance();
                BufferBuilder bufferbuilder = tesselator.getBuilder();
                int i = p_115176_.getBarWidth();
                int j = p_115176_.getBarColor();
                fillRect(stack, bufferbuilder, p_115177_ + 2, p_115178_ + 13, 13, 2, 0, 0, 0, 255);
                fillRect(stack, bufferbuilder, p_115177_ + 2, p_115178_ + 13, i, 1, j >> 16 & 255, j >> 8 & 255, j & 255, 255);
                RenderSystem.enableBlend();


                RenderSystem.enableDepthTest();
            }

            LocalPlayer localplayer = Minecraft.getInstance().player;
            float f = localplayer == null ? 0.0F : localplayer.getCooldowns().getCooldownPercent(p_115176_.getItem(), Minecraft.getInstance().getFrameTime());
            if (f > 0.0F) {
                RenderSystem.disableDepthTest();

                RenderSystem.enableBlend();
                RenderSystem.defaultBlendFunc();
                Tesselator tesselator1 = Tesselator.getInstance();
                BufferBuilder bufferbuilder1 = tesselator1.getBuilder();
                fillRect(stack, bufferbuilder1, p_115177_, p_115178_ + Mth.floor(16.0F * (1.0F - f)), 16, Mth.ceil(16.0F * f), 255, 255, 255, 127);

                RenderSystem.enableDepthTest();
            }

        }

    }

    private static void fillRect(PoseStack stack, BufferBuilder p_115153_, int p_115154_, int p_115155_, int p_115156_, int p_115157_, int p_115158_, int p_115159_, int p_115160_, int p_115161_) {
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        p_115153_.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);
        p_115153_.vertex(stack.last().pose(), p_115154_, p_115155_, 0.0F).color(p_115158_, p_115159_, p_115160_, p_115161_).endVertex();
        p_115153_.vertex(stack.last().pose(), p_115154_, p_115155_ + p_115157_, 0.0F).color(p_115158_, p_115159_, p_115160_, p_115161_).endVertex();
        p_115153_.vertex(stack.last().pose(), p_115154_ + p_115156_, p_115155_ + p_115157_, 0.0F).color(p_115158_, p_115159_, p_115160_, p_115161_).endVertex();
        p_115153_.vertex(stack.last().pose(), p_115154_ + p_115156_, p_115155_, 0.0F).color(p_115158_, p_115159_, p_115160_, p_115161_).endVertex();

        BufferUploader.draw(p_115153_.end());
    }

    public static void renderAndDecorateItem(PoseStack stack, ItemStack p_115204_, int p_115205_, int p_115206_) {
        tryRenderGuiItem(stack, mc.player, p_115204_, p_115205_, p_115206_, 0);
    }

    protected static void renderGuiItem(PoseStack stack, ItemStack p_115128_, int p_115129_, int p_115130_, BakedModel p_115131_) {
        stack.translate(p_115129_, p_115130_, 100.0F);
        stack.translate(8.0D, 8.0D, 0.0D);
        stack.scale(1.0F, -1.0F, 1.0F);
        stack.scale(16.0F, 16.0F, 16.0F);
        MultiBufferSource.BufferSource multibuffersource$buffersource = Minecraft.getInstance().renderBuffers().bufferSource();
        boolean is3D = p_115131_.isGui3d();
        if (is3D) {
            Lighting.setupForEntityInInventory();
        } else {
            Lighting.setupForFlatItems();
        }
        mc.getItemRenderer().render(p_115128_, ItemDisplayContext.GUI, false, stack, multibuffersource$buffersource, 15728880, OverlayTexture.NO_OVERLAY, p_115131_);
        multibuffersource$buffersource.endBatch();
        Lighting.setupFor3DItems();
    }

    private static void tryRenderGuiItem(PoseStack stack, @Nullable LivingEntity p_174278_, ItemStack p_174279_, int p_174280_, int p_174281_, int p_174282_) {
        tryRenderGuiItem(stack, p_174278_, p_174279_, p_174280_, p_174281_, p_174282_, 0);
    }

    private static void tryRenderGuiItem(PoseStack stack, @Nullable LivingEntity p_174236_, ItemStack p_174237_, int p_174238_, int p_174239_, int p_174240_, int p_174241_) {
        if (!p_174237_.isEmpty()) {
            BakedModel bakedmodel = mc.getItemRenderer().getModel(p_174237_, null, p_174236_, p_174240_);
//            mc.getItemRenderer().blitOffset = bakedmodel.isGui3d() ? mc.getItemRenderer().blitOffset + 50.0F + (float) p_174241_ : mc.getItemRenderer().blitOffset + 50.0F;

            try {
                renderGuiItem(stack, p_174237_, p_174238_, p_174239_, bakedmodel);
            } catch (Throwable throwable) {

            }

//            mc.getItemRenderer().blitOffset = bakedmodel.isGui3d() ? mc.getItemRenderer().blitOffset - 50.0F - (float) p_174241_ : mc.getItemRenderer().blitOffset - 50.0F;
        }
    }

    public static void drawRound(PoseStack matrices, float x, float y, float width, float height, float radius, Color color) {
        renderRoundedQuad(matrices, color, x, y, width + x, height + y, radius, 4);
    }

    public static void renderRoundedQuad(PoseStack matrices, Color c, double fromX, double fromY, double toX, double toY, double radius, double samples) {
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        renderRoundedQuadInternal(matrices.last().pose(), c.getRed() / 255f, c.getGreen() / 255f, c.getBlue() / 255f, c.getAlpha() / 255f, fromX, fromY, toX, toY, radius, samples);
        endRender();
    }

    public static void renderRoundedQuadInternal(Matrix4f matrix, float cr, float cg, float cb, float ca, double fromX, double fromY, double toX, double toY, double radius, double samples) {
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder bufferBuilder = tesselator.getBuilder();
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        bufferBuilder.begin(VertexFormat.Mode.TRIANGLE_FAN, DefaultVertexFormat.POSITION_COLOR);
        double[][] map = new double[][]{
                {toX - radius, toY - radius, radius},
                {toX - radius, fromY + radius, radius},
                {fromX + radius, fromY + radius, radius},
                {fromX + radius, toY - radius, radius}
        };

        for (int i = 0; i < 4; i++) {
            double[] current = map[i];
            double rad = current[2];
            for (double r = i * 90d; r < (360 / 4d + i * 90d); r += (90 / samples)) {
                float rad1 = (float) Math.toRadians(r);
                float sin = (float) (Math.sin(rad1) * rad);
                float cos = (float) (Math.cos(rad1) * rad);
                bufferBuilder.vertex(matrix, (float) current[0] + sin, (float) current[1] + cos, 0.0F)
                        .color(cr, cg, cb, ca)
                        .endVertex();
            }
            float rad1 = (float) Math.toRadians((360 / 4d + i * 90d));
            float sin = (float) (Math.sin(rad1) * rad);
            float cos = (float) (Math.cos(rad1) * rad);
            bufferBuilder.vertex(matrix, (float) current[0] + sin, (float) current[1] + cos, 0.0F)
                    .color(cr, cg, cb, ca)
                    .endVertex();
        }

        RenderSystem.enableDepthTest();
        tesselator.end();
    }
    /**
     * 绘制圆形
     *
     * @param poseStack 渲染用的 PoseStack
     * @param centerX   圆心 X 坐标
     * @param centerY   圆心 Y 坐标
     * @param radius    圆的半径
     * @param color     颜色（ARGB 格式，例如 0xFFFF0000 表示不透明红色）
     * @param segments  圆形分段数，越高越平滑（例如 50 或 100）
     */
    public static void drawCircle(PoseStack poseStack, float centerX, float centerY, float radius, int color, int segments) {
        // 分解颜色
        float alpha = ((color >> 24) & 0xFF) / 255.0F;
        float red   = ((color >> 16) & 0xFF) / 255.0F;
        float green = ((color >> 8) & 0xFF) / 255.0F;
        float blue  = (color & 0xFF) / 255.0F;

        // 获取矩阵、Tesselator 和 BufferBuilder
        Matrix4f matrix = poseStack.last().pose();
        Tesselator tessellator = Tesselator.getInstance();
        BufferBuilder buffer = tessellator.getBuilder();

        // 使用 TRIANGLE_FAN 绘制圆形
        buffer.begin(VertexFormat.Mode.TRIANGLE_FAN, DefaultVertexFormat.POSITION_COLOR);
        // 第一个顶点为圆心
        buffer.vertex(matrix, centerX, centerY, 0).color(red, green, blue, alpha).endVertex();

        // 计算圆周上各点的坐标
        for (int i = 0; i <= segments; i++) {
            double angle = (2 * Math.PI * i) / segments;
            float x = centerX + (float) Math.cos(angle) * radius;
            float y = centerY + (float) Math.sin(angle) * radius;
            buffer.vertex(matrix, x, y, 0).color(red, green, blue, alpha).endVertex();
        }

        tessellator.end();
    }
    public static void drawCircle(PoseStack poseStack, Entity entity, double radius, float partialTicks, Color color, float lineWidth) {
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        double x = Mth.lerp(partialTicks, entity.xOld, entity.getX()) - Minecraft.getInstance().getEntityRenderDispatcher().camera.getPosition().x;
        double y = Mth.lerp(partialTicks, entity.yOld, entity.getY()) - Minecraft.getInstance().getEntityRenderDispatcher().camera.getPosition().y;
        double z = Mth.lerp(partialTicks, entity.zOld, entity.getZ()) - Minecraft.getInstance().getEntityRenderDispatcher().camera.getPosition().z;

        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();

        int steps = Math.max(1, (int) lineWidth * 5);
        float stepSize = lineWidth * 0.001f;

        for (int step = 0; step < steps; step++) {
            float adjustedRadius = (float) radius + (step * stepSize);

            buffer.begin(VertexFormat.Mode.DEBUG_LINE_STRIP, DefaultVertexFormat.POSITION_COLOR);
            for (int i = 0; i <= 360; i++) {
                double angle = 2 * Math.PI * i / 360;
                double xOffset = adjustedRadius * Math.cos(angle);
                double zOffset = adjustedRadius * Math.sin(angle);
                buffer.vertex(poseStack.last().pose(), (float) (x + xOffset), (float) y, (float) (z + zOffset))
                        .color(color.getRGB())
                        .endVertex();
            }

            tesselator.end();
        }

        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
    }

    public static void drawNewArrow(PoseStack matrices, float x, float y, float size, Color color) {
        RenderSystem.setShaderTexture(0, new ResourceLocation("/triangle.png"));
        setupRender();
        RenderSystem.setShaderColor(color.getRed() / 255f, color.getGreen() / 255f, color.getBlue() / 255f, color.getAlpha() / 255f);
        RenderSystem.disableDepthTest();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE);
        Matrix4f matrix = matrices.last().pose();
        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder bufferBuilder = tesselator.getBuilder();
        bufferBuilder.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_TEX);
        bufferBuilder.vertex(matrix, x - (size / 2f), y + size, 0).uv(0f, 1f).endVertex();
        bufferBuilder.vertex(matrix, x + size / 2f, y + size, 0).uv(1f, 1f).endVertex();
        bufferBuilder.vertex(matrix, x + size / 2f, y, 0).uv(1f, 0).endVertex();
        bufferBuilder.vertex(matrix, x - (size / 2f), y, 0).uv(0, 0).endVertex();
        tesselator.end();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
        RenderSystem.defaultBlendFunc();
        RenderSystem.enableDepthTest();
        endRender();
    }


    public static Animation animation = new DecelerateAnimation(400, 1.0d);
    public static Animation scaffoldAnimation = new DecelerateAnimation(200, 1.0d);
    private LivingEntity lastTarget = null;
    public static void drawShadow(PoseStack poseStack, float x, float y, float width, float height) {

        drawImage("paneltopleft.png",poseStack, x - 9, y - 9, 9, 9, false);
        drawImage("panelbottomleft.png", poseStack, x - 9, y + height, 9, 9, false);
        drawImage("panelbottomright.png",poseStack, x + width, y + height, 9, 9, false);
        drawImage("paneltopright.png",poseStack, x + width, y - 9, 9, 9, false);
        drawImage("panelleft.png",poseStack, x - 9, y, 9, height, false);
        drawImage("panelright.png",poseStack, x + width, y, 9, height, false);
        drawImage("paneltop.png", poseStack, x, y - 9, width, 9, false);
        drawImage("panelbottom.png",poseStack, x, y + height, width, 9, false);

    }
    public static void drawImage(String str, PoseStack matrices, float x, float y, float width, float height, int color) {
        drawImage(str, matrices, x, y, width, height, color,true);
    }
    public static void drawImage(String str, PoseStack matrices, float x, float y, float width, float height) {
        drawImage(str, matrices, x, y, width, height, Color.WHITE.getRGB(),true);
    }
    public static void drawImage(String str, PoseStack matrices, float x, float y, float width, float height,boolean blend)
    {
        drawImage(str, matrices, x, y, width, height, Color.WHITE.getRGB(),blend);
    }
    public static void drawImage(String str, PoseStack matrices, float x, float y, float width, float height,int color, boolean blend) {
        matrices.pushPose();
        RenderSystem.disableDepthTest();
        RenderSystem.disableCull();
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();

        if(blend)
            RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE);
//       matrices.scale(0.2f,0.2f,0.2f);

        RenderSystem.setShader(GameRenderer::getPositionTexShader);
        RenderSystem.setShaderTexture(0, new ResourceLocation("/" + str));

        BufferBuilder buffer = Tesselator.getInstance().getBuilder();
        buffer.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_TEX);
        Matrix4f matrix = matrices.last().pose();

        // 获取图片的默认颜色

        int rgba = new Color((color >> 16) & 0xFF, (color >> 8) & 0xFF, color & 0xFF, 255).getRGB();

        // 绘制四个顶点，形成一个矩形（图片）
        buffer.vertex(matrix, x, y + height, 0).uv(0f, 1f).color(rgba).endVertex(); // 左下角
        buffer.vertex(matrix, x + width, y + height, 0).uv(1f, 1f).color(rgba).endVertex(); // 右下角
        buffer.vertex(matrix, x + width, y, 0).uv(1f, 0f).color(rgba).endVertex(); // 右上角
        buffer.vertex(matrix, x, y, 0).uv(0f, 0f).color(rgba).endVertex(); // 左上角

        // 绘制完成
        Tesselator.getInstance().end();

        // 恢复默认渲染状态
        RenderSystem.enableCull();
        RenderSystem.enableDepthTest();
        RenderSystem.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
        RenderSystem.disableBlend();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
        matrices.popPose();
    }

    private Module scaffoldModule = null;

    public static void enableScissor(int x, int y, int width, int height) {
        Window window = mc.getWindow();
        int scaleFactor = (int) window.getGuiScale();

        int xPixels = x * scaleFactor;
        int yPixels = y * scaleFactor;
        int widthPixels = width * scaleFactor;
        int heightPixels = height * scaleFactor;

        int glY = window.getHeight() - (yPixels + heightPixels);
        RenderSystem.enableScissor(xPixels, glY, widthPixels, heightPixels);
    }

    public static void drawRoundedRect(PoseStack poseStack, double x, double y, double width, double height, double radius, Color color) {

        renderRoundedQuad(poseStack, color, x, y, x + width, y + height, radius, 128); // 进一步增加采样数
    }

    private static final HashMap<Integer, Integer> shadowCache = new HashMap<>();
    public static int uploadTexture(BufferedImage image, boolean allocate, boolean linear) {
        NativeImage nativeImage = new NativeImage(image.getWidth(), image.getHeight(), !allocate);
        for (int x = 0; x < image.getWidth(); x++) {
            for (int y = 0; y < image.getHeight(); y++) {
                nativeImage.setPixelRGBA(x, y, image.getRGB(x, y));
            }
        }

        // 创建纹理对象
        DynamicTexture texture = new DynamicTexture(nativeImage);
        int textureId = texture.getId();

        // 设置纹理过滤参数
        if (linear) {
            RenderSystem.bindTexture(textureId);
            RenderSystem.texParameter(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, GL11.GL_LINEAR);
            RenderSystem.texParameter(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR);
        }

        return textureId;
    }

    public static int uploadTexture(BufferedImage image) {
        NativeImage nativeImage = new NativeImage(image.getWidth(), image.getHeight(), false);
        for (int x = 0; x < image.getWidth(); x++) {
            for (int y = 0; y < image.getHeight(); y++) {
                nativeImage.setPixelRGBA(x, y, image.getRGB(x, y));
            }
        }

        DynamicTexture texture = new DynamicTexture(nativeImage);
        return texture.getId();
    }

    private HitBubbles hitBubbles = null;
    private SkyParticles skyParticles = null;
    private KillAura killAura = null;


    private float circleStep = 0;
    @EventTarget
    public void onRenderEngine(EventRender3D event) {
        if (Utils.isNull())
            return;
        if(circleStep > 10000) circleStep = 0;
//        if (event.getStage() == RenderLevelStageEvent.Stage.AFTER_LEVEL) {
        if (hitBubbles == null)
            hitBubbles = (HitBubbles) Main.INSTANCE.moduleManager.getModule("HitBubbles");
        if (skyParticles == null)
            skyParticles = (SkyParticles) Main.INSTANCE.moduleManager.getModule("SkyParticles");
        if (killAura == null)
            killAura = (KillAura) Main.INSTANCE.moduleManager.getModule("KillAura");

        Camera camera = mc.gameRenderer.getMainCamera();
        PoseStack matrixStack = new PoseStack();
        RenderSystem.getModelViewStack().pushPose();
        matrixStack.mulPose(Axis.XP.rotationDegrees(camera.getXRot()));
        matrixStack.mulPose(Axis.YP.rotationDegrees(camera.getYRot() + 180.0f));
        RenderSystem.applyModelViewMatrix();
        Render3DEngine.lastProjMat.set(RenderSystem.getProjectionMatrix());
        Render3DEngine.lastModMat.set(RenderSystem.getModelViewMatrix());
        Render3DEngine.lastWorldSpaceMatrix.set(matrixStack.last().pose());
        animation.setDirection(killAura.target != null ? Direction.FORWARDS : Direction.BACKWARDS);

        if (killAura.target != null) {
            lastTarget = killAura.target;
        }

        if (killAura.isEnable() && lastTarget != null && !(animation.getDirection() == Direction.BACKWARDS && animation.isDone()) && !killAura.mode.getValue().equals("Multi")) {
            if (killAura.espValue.getValue().equals("Texture")) {
                CaptureMark.render(lastTarget, mc.getFrameTime(), matrixStack);
            }

            if (killAura.espValue.getValue().equals("Jello")) {
                CaptureMark.drawJello(matrixStack, lastTarget, mc.getFrameTime(), circleStep,
                        new Color(255,255,255,150));
                circleStep += 0.02f;
            }
            if (killAura.espValue.getValue().equals("Box")) {
                AABB box = lastTarget.getBoundingBox();
                RenderUtils.drawLineBox(event.getPoseStack(), box, Color.RED);
            }
        }

        if (skyParticles.isEnable())
            skyParticles.onRender3D(matrixStack);

        if (hitBubbles.isEnable())
            hitBubbles.onRender3D(matrixStack);

        Render3DEngine.onRender3D(matrixStack);
        RenderSystem.getModelViewStack().popPose();
        RenderSystem.applyModelViewMatrix();
    }
//    }
}