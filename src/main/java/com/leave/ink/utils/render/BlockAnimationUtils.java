package com.leave.ink.utils.render;

import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SwordItem;
import org.joml.Quaternionf;

public class BlockAnimationUtils {

    public static void Sigma(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        matrixStack.translate(side * 0.56, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.08, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f = Math.sin(swingProgress * swingProgress * Math.PI);
        double f1 = Math.sin(Math.sqrt(f) * Math.PI);
        matrixStack.translate(0.0F, f * -0.6F, 0.0F);
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (-f1 * -10.0F)));
        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (-f1 * -10.0F)));
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (-f1 * -10.0F)));
    }

    public static void Push(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        matrixStack.translate(side * 0.56, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.08, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f = Math.sin(swingProgress * swingProgress * Math.PI);
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (f * -10.0F)));
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (f1 * -10.0F)));
        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (f1 * -10.0F)));
    }

    public static void New(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        matrixStack.translate(side * 0.56, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.08, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f = Math.sin(swingProgress * swingProgress * Math.PI);
        double f1 = Math.sin(Math.sqrt(f) * Math.PI);
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (-f1 * 35.0F)));
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (-f1 * 90.0f)));
    }

    public static boolean getCanStackBlock(ItemStack stack) {
        return stack.getItem() instanceof SwordItem;
    }

    public static void SmoothBlock(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        matrixStack.translate(side * 0.56, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.08, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f = Math.sin(swingProgress * swingProgress * Math.PI);
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (f * -20.0F)));
        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (f1 * -20.0F)));
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (f1 * -30.0F)));
    }

    public static void Flux(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        matrixStack.translate(side * 0.56, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.08, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f = Math.sin(swingProgress * swingProgress * Math.PI);
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (f * -30.0F)));
        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (f1 * -15.0F)));
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (f1 * -15.0F)));
    }

    public static void New2(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;

        matrixStack.translate(0.56f, -0.52f, -0.72);
        matrixStack.translate(side * -0.1414214, 0.08, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 7.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
//                float f = (float) Math.sin(swingProgress * swingProgress * Math.PI);
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (f1 * -10.0F)));
        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (f1 * 30.0F)));
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (f1 * -13.0F)));
//        float f = (float) Math.sin(swingProgress * swingProgress * Math.PI);
//        float f1 = (float) Math.sin(Math.sqrt(swingProgress) * Math.PI);
//        poseStack.mulPose(Axis.YP.rotationDegrees(f * -20.0F));
//        poseStack.mulPose(Axis.ZP.rotationDegrees(f1 * -20.0F));
//        poseStack.mulPose(Axis.XP.rotationDegrees(f1 * -80.0F));
    }

    public static void ETB(HumanoidArm hand, PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        matrixStack.translate(0.56F, -0.52F, -0.71999997F);
        matrixStack.translate(0.0F, equippedProg * -0.6F, 0.0F);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        float var3 = Mth.sin(swingProgress * swingProgress * (float) Math.PI);
        float var4 = Mth.sin(Mth.sqrt(swingProgress) * (float) Math.PI);
        matrixStack.mulPose(Axis.YP.rotationDegrees(var3 * -34.0F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(var4 * -20.7F));
        matrixStack.mulPose(Axis.XP.rotationDegrees(var4 * -68.6F));
    }

    public static void transformSideFirstPerson(HumanoidArm handSide, PoseStack poseStack, float equippedProg) {
        int side = handSide == HumanoidArm.RIGHT ? 1 : -1;
        poseStack.translate(side * 0.56, -0.52 + equippedProg * -0.6, -0.72);
        poseStack.translate(side * -0.1414214, 0.08, 0.1414214);
        poseStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        poseStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
    }

    public static void Rotation360(PoseStack matrixStack, float swingProgress) {
        int side = 1;
        matrixStack.translate(0.56f, -0.52f, -0.72);
        matrixStack.translate(side * -0.1414214, 0.18, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 14.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
//                float f = (float) Math.sin(swingProgress * swingProgress * Math.PI);
        matrixStack.mulPose(Axis.XP.rotationDegrees(Mth.sqrt(swingProgress) * 10.0F * 36.0F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(0));
        matrixStack.mulPose(Axis.YP.rotationDegrees(0));

    }
    public static void transformSideFirstPersonBlock_1_7(PoseStack poseStack, HumanoidArm arm, float equippedProg, float swingProgress) {
        int side = arm == HumanoidArm.RIGHT ? 1 : -1;
        poseStack.translate(side * 0.56F, -0.52F + equippedProg * -0.6F, -0.71999997F);
        float f = Mth.sin(swingProgress * swingProgress * (float) Math.PI);
        float f1 = Mth.sin(Mth.sqrt(swingProgress) * (float) Math.PI);
        poseStack.mulPose(new Quaternionf().rotateY((float) Math.toRadians(side * (45.0F + f * -20.0F))));
        poseStack.mulPose(new Quaternionf().rotateZ((float) Math.toRadians(side * f1 * -20.0F)));
        poseStack.mulPose(new Quaternionf().rotateX((float) Math.toRadians(f1 * -80.0F)));
        poseStack.mulPose(new Quaternionf().rotateY((float) Math.toRadians(side * -45.0F)));
        poseStack.scale(0.9F, 0.9F, 0.9F);
        poseStack.translate(-0.2F, 0.126F, 0.2F);
        poseStack.mulPose(new Quaternionf().rotateX((float) Math.toRadians(-102.25F)));
        poseStack.mulPose(new Quaternionf().rotateY((float) Math.toRadians(side * 15.0F)));
        poseStack.mulPose(new Quaternionf().rotateZ((float) Math.toRadians(side * 80.0F)));
    }
    public static void transformSideFirstPersonBlock(HumanoidArm hand, PoseStack poseStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        poseStack.translate(side * 0.56, -0.52 + equippedProg * -0.6 , -0.72);
        poseStack.translate(side * -0.1414214, 0.08, 0.1414214);
        poseStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        poseStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        float f = (float) Math.sin(swingProgress * swingProgress * Math.PI);
        float f1 = (float) Math.sin(Math.sqrt(swingProgress) * Math.PI);
        poseStack.mulPose(Axis.YP.rotationDegrees(f * -20.0F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(f1 * -20.0F));
        poseStack.mulPose(Axis.XP.rotationDegrees(f1 * -80.0F));
    }

    public static void sigmaold(HumanoidArm hand, PoseStack poseStack, float equippedProg, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        poseStack.translate(0.56F, -0.52F + equippedProg * -0.6F, -0.71999997F);
        poseStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        poseStack.mulPose(Axis.YP.rotationDegrees(side * 13.365F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        float var3 = Mth.sin(swingProgress * swingProgress * (float) Math.PI);
        float var4 = Mth.sin(Mth.sqrt(swingProgress) * (float) Math.PI);
        poseStack.mulPose(Axis.YP.rotationDegrees(var3 * -15F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(var4 * -10F));
        poseStack.mulPose(Axis.XP.rotationDegrees(var4 * -30F));
    }

    public static void tap(PoseStack matrixStack, float equippedProg, float swingProgress) {
        int side = 1;
        matrixStack.translate(0.56f, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.18, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 14.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
//                float f = (float) Math.sin(swingProgress * swingProgress * Math.PI);
//        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (f1 * -49.0F)));
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (f1 *- 35.0F)));
//
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (f1 * -33.0F)));
    }
    public static void custom(PoseStack matrixStack, float equippedProg, float swingProgress, float x, float y, float z) {
        int side = 1;
        matrixStack.translate(0.56f, -0.52 + equippedProg * -0.6, -0.72);
        matrixStack.translate(side * -0.1414214, 0.18, 0.1414214);
        matrixStack.mulPose(Axis.XP.rotationDegrees(-102.25F));
        matrixStack.mulPose(Axis.YP.rotationDegrees(side * 14.365F));
        matrixStack.mulPose(Axis.ZP.rotationDegrees(side * 78.050003F));
        double f1 = Math.sin(Math.sqrt(swingProgress) * Math.PI);
//                float f = (float) Math.sin(swingProgress * swingProgress * Math.PI);
        matrixStack.mulPose(Axis.ZP.rotationDegrees((float) (f1 * x)));
        matrixStack.mulPose(Axis.XP.rotationDegrees((float) (f1 * y)));
//
        matrixStack.mulPose(Axis.YP.rotationDegrees((float) (f1 *  z)));
    }
    public static void Zoom(PoseStack poseStack, float equippedProg, float swingProgress) {
        poseStack.translate(0.56F, -0.52F, -0.71999997F);
        poseStack.mulPose(Axis.YP.rotationDegrees(45.0F));
        float var3 = Mth.sin(swingProgress * swingProgress * (float) Math.PI);
        float var4 = Mth.sin(Mth.sqrt(swingProgress) * (float) Math.PI);
        poseStack.mulPose(Axis.YP.rotationDegrees(var3 * -20.0F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(var4 * -20.0F));
        poseStack.mulPose(Axis.ZP.rotationDegrees(var4 * -20.0F));
    }

    public static void jello(HumanoidArm hand, PoseStack poseStack, float swingProgress) {
        int side = hand == HumanoidArm.RIGHT ? 1 : -1;
        poseStack.translate(0.56f, -0.52f, -0.71999997f);
        poseStack.mulPose(Axis.XP.rotationDegrees(-102.25f));
        poseStack.mulPose(Axis.YP.rotationDegrees(side * 13.365f));
        poseStack.mulPose(Axis.ZP.rotationDegrees(side * 78.05f));
        float var13 = Mth.sin(swingProgress * swingProgress * (float) Math.PI);
        float var14 = Mth.sin(Mth.sqrt(swingProgress) * (float) Math.PI);
        poseStack.mulPose(Axis.ZP.rotationDegrees(var13 * -35.0f));
        poseStack.mulPose(Axis.ZP.rotationDegrees(var14 * 0.0f));
        poseStack.mulPose(Axis.XP.rotationDegrees(var14 * 20.0f));
    }
}
