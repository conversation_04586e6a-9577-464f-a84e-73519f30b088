package com.leave.ink.utils.render.projectiles;

import java.util.HashSet;
import java.util.Set;

public class ProjectileContext {
    private static String[] field44270;
    public TracePoint tracePoint;
    public int projectileId;
    public int state = 0;
    public Float initialVelocity;
    public Float currentVelocity = 0.0F;
    public int lifeTime = 1;
    public int maxLifeTime = 2500;
    public ProjectileContext parentContext;
    public ProjectileState projectileState = ProjectileState.IDLE;
    public ProjectileDirection direction = ProjectileDirection.NONE;
    public Set<Long> field44282 = new HashSet<Long>();
    public Set<Long> collidedPositions = new HashSet<Long>();
    public Set<Long> ignoredPositions = new HashSet<Long>();

    public ProjectileContext(TracePoint var1, int var2, ProjectileState var3, ProjectileDirection var4) {
        this.tracePoint = var1;
        this.lifeTime += var2;
        this.projectileState = var3;
        this.direction = var4;
    }

    public ProjectileContext addCollidedPosition(TracePoint tracePoint, boolean isParent) {
        long pos = tracePoint.toBlockPos().asLong();
        if (isParent) {
            long var7 = tracePoint.toBlockPos().above().asLong();
            this.collidedPositions.add(var7);
            this.ignoredPositions.remove(var7);
        }

        this.collidedPositions.add(pos);
        this.ignoredPositions.remove(pos);
        return this;
    }
}