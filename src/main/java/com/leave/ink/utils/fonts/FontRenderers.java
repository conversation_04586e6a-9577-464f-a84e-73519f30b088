package com.leave.ink.utils.fonts;

import org.jetbrains.annotations.NotNull;
import java.awt.*;
import java.io.File;
import java.io.IOException;

public class FontRenderers {
    public static FontRenderer settings;
    public static FontRenderer modules;
    public static FontRenderer categories;
    public static FontRenderer icons;
    public static FontRenderer icons2_30;
    public static FontRenderer mid_icons;
    public static FontRenderer big_icons;
    public static FontRenderer thglitch;
    public static FontRenderer thglitchBig;
    public static FontRenderer monsterrat;
    public static FontRenderer sf_bold;
    public static FontRenderer sf_bold18;
    public static FontRenderer sf_bold20;
    public static FontRenderer sf_bold62;
    public static FontRenderer misans14;
    public static FontRenderer misans16;
    public static FontRenderer misans18;
    public static FontRenderer harmony18;
    public static FontRenderer misans20;
    public static FontRenderer sf_bold_mini;
    public static FontRenderer sf_bold_micro;
    public static FontRenderer sf_medium;
    public static FontRenderer sf_medium_mini;
    public static FontRenderer sf_medium_modules;
    public static FontRenderer minecraft;
    public static FontRenderer profont;

    public static FontRenderer getModulesRenderer() {
        return modules;
    }

    public static @NotNull FontRenderer create(float size, String name) throws IOException, FontFormatException {
        return new FontRenderer(Font.createFont(Font.TRUETYPE_FONT, new File("C:\\Leave\\resource\\fonts\\" + name + ".ttf")).deriveFont(Font.PLAIN, size / 2f), size / 2f);
    }
}
