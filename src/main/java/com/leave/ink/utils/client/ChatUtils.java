package com.leave.ink.utils.client;

import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;

public class ChatUtils implements IMinecraft {

    public static void displayChatMessage(final String message) {
        if (mc.player == null) return;

        MutableComponent chatComponent = Component.Serializer.fromJson("{\"text\":\"" + message + "\"}");

        if (chatComponent != null) {
            mc.player.sendSystemMessage(chatComponent);
        }
    }

    public static void displayAlert(final String message) {
        displayChatMessage("§bLeave§7>>§f " + message);
    }
}

