package com.leave.ink.utils.client;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.external.ui.ExternalUI;
import com.leave.ink.Main;
import com.leave.ink.events.*;
import com.leave.ink.features.module.Module;
import com.leave.ink.utils.*;
import com.leave.ink.utils.file.FileUtils;
import com.leave.ink.utils.file.LocalResource;
import com.leave.ink.utils.timer.TimeUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.client.gui.screens.ChatScreen;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.protocol.game.ClientboundSystemChatPacket;
import net.minecraft.network.protocol.game.ServerboundChatPacket;

import net.minecraft.world.entity.LivingEntity;

import org.lwjgl.glfw.GLFW;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class CommonEvents implements IMinecraft {
    public static boolean gameInit = false;
    public static int HM = 0;
    public static int S = 0;
    public static int M = 0;
    public static int H = 0;
    public static int kill = 0;
    public static int dead = 0;
    private LivingEntity target = null;
    private final TimeUtils timer = new TimeUtils();
    private final TimeUtils timer2 = new TimeUtils();

    public CommonEvents() {
        EventManager.register(this);

    }

    @EventTarget
    public void onUpdate(EventUpdate event) {
        HM += 1;
        if (HM == 22) {
            S = S + 1;
            HM = 0;
        }
        if (S == 60) {
            M = M + 1;
            S = 0;
        }
        if (M == 60) {
            H = H + 1;
            M = 0;
        }

        if (timer2.hasTimeElapsed(1000L)) {
            if (target != null) {
                if (target.isDeadOrDying()) {
                    kill++;
                    target = null;
                    timer2.reset();
                }
            }
        }

        if (timer.hasTimeElapsed(2000L)) {
            if (mc.player.getHealth() <= 0.2f || mc.player.isDeadOrDying()) {
                dead++;
                timer.reset();
            }
        }
    }

    @EventTarget
    public void onAttack(EventAttack event) {
        if (event.getTargetEntity() instanceof LivingEntity entity && Utils.isValidEntity(entity))
            target = entity;
    }

    @EventTarget
    public void onRender(EventRender2D event) {
        if (!gameInit) {
            try {
                loadZip();
                LocalResource.loadTexture(FileUtils.getFilesByFolder(Utils.getResourcePath(), "png"), Utils.getResourcePath());
                LocalResource.loadTexture(FileUtils.getFilesByFolder(Utils.getResourcePath() + "\\shadow", "png"), Utils.getResourcePath() + "\\shadow");
                LocalResource.loadTexture(FileUtils.getFilesByFolder(Utils.getResourcePath() + "\\icons", "png"), Utils.getResourcePath() + "\\icons");

                gameInit = true;
            } catch (Exception ignored) {
                System.out.println("Failed Load Resource");
                gameInit = true;
            }
        }
    }

    private void loadZip() {
        try (ZipFile zipFile = new ZipFile("C:\\Leave\\resource\\logo0")) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                InputStream inputStream = zipFile.getInputStream(entry);
                LocalResource.loadTexture(entry.getName(), inputStream);
//                System.out.println(entry.getName());
                inputStream.close();
            }

        } catch (Exception ignored) {
        }
    }

    @EventTarget
    public void onPacket(EventPacket event) {
        if (event.getPacket() instanceof ServerboundChatPacket chatPacket) {
            if (chatPacket.message().equals(".ui")) {
                ExternalUI.UI_Show();
                if (mc.screen == null) {
                    mc.execute(() -> mc.setScreen(new ChatScreen("")));
                }
                event.setCancelled(true);
            }
        }
//

//        if (event.getPacket() instanceof ClientboundCustomPayloadPacket payloadPacket) {
//            ChatUtils.displayAlert(payloadPacket.getIdentifier().toString());
//            test(payloadPacket.getData());
////            if (payloadPacket.getIdentifier().toString().contains("heypixel:s2cevent")) {
//////                event.setCancelled(true);
////                test(payloadPacket.getData());
////            }
//
//        }
        if (event.getPacket() instanceof ClientboundSystemChatPacket chatPacket) {
            if (chatPacket.content().getString().contains("击杀!") && chatPacket.content().getString().contains("+") && chatPacket.content().getString().contains("XP"))
                kill++;

            if (chatPacket.content().getString().contains("双杀!") && chatPacket.content().getString().contains("+") && chatPacket.content().getString().contains("XP"))
                kill++;

            if (chatPacket.content().getString().contains("三杀!") && chatPacket.content().getString().contains("+") && chatPacket.content().getString().contains("XP"))
                kill++;

            if (chatPacket.content().getString().contains("四杀!") && chatPacket.content().getString().contains("+") && chatPacket.content().getString().contains("XP"))
                kill++;

            if (chatPacket.content().getString().contains("五杀!") && chatPacket.content().getString().contains("+") && chatPacket.content().getString().contains("XP"))
                kill++;

            if (chatPacket.content().getString().contains("被 " + mc.player.getName().getString() + " 击败"))
                kill++;

            if (chatPacket.content().getString().contains("幕后黑手是 " + mc.player.getName().getString()))
                kill++;

            if (chatPacket.content().getString().contains("被杀死因为 " + mc.player.getName().getString()))
                kill++;

            if (chatPacket.content().getString().contains("被射死因为 " + mc.player.getName().getString()))
                kill++;

            if (chatPacket.content().getString().contains("你贡献了") && chatPacket.content().getString().contains("伤害并获得"))
                kill++;

            if (chatPacket.content().getString().contains(mc.player.getName().getString() + " 击败了"))
                kill++;

            if (chatPacket.content().getString().contains("被 " + mc.player.getName().getString() + " 切成了肉片"))
                kill++;

            if (chatPacket.content().getString().contains("被 " + mc.player.getName().getString() + " 烤成了肉串"))
                kill++;

            if (chatPacket.content().getString().contains(mc.player.getName().getString() + " 对于"))
                kill++;

            if (chatPacket.content().getString().contains("布吉岛经验") && chatPacket.content().getString().contains("击败") )
                kill++;

            if (chatPacket.content().getString().contains("死亡!") && chatPacket.content().getString().contains("查看日志")) {
                if (Main.INSTANCE.moduleManager.getModule("KillAura").isEnable())
                    Main.INSTANCE.moduleManager.getModule("KillAura").setEnable(false);

                dead++;
            }

            if (chatPacket.content().getString().contains(mc.player.getName().getString() + " 被") && chatPacket.content().getString().contains("击败"))
                dead++;

            if (chatPacket.content().getString().contains(mc.player.getName().getString() + " 拥抱了虚空娘"))
                dead++;

            if (chatPacket.content().getString().contains(mc.player.getName().getString() + " 被") && chatPacket.content().getString().contains("烤成了肉串"))
                dead++;

            if (chatPacket.content().getString().contains(mc.player.getName().getString() + " 被") && chatPacket.content().getString().contains("切成了肉片"))
                dead++;

            if (chatPacket.content().getString().contains("击败了 " + mc.player.getName().getString()))
                dead++;

            if (chatPacket.content().getString().contains("对于 " + mc.player.getName().getString()))
                dead++;

            if (chatPacket.content().getString().contains(mc.player.getDisplayName().getString() + " 被杀死因为"))
                dead++;

            if (chatPacket.content().getString().contains(mc.player.getDisplayName().getString() + " 被射死因为"))
                dead++;
        }
    }
    public String readStringWithLength(FriendlyByteBuf buf) {
        // 假设先读取一个整数或者字节，判断是否为空字符串
        int initial = buf.readInt();
        if (initial == 0) {
            return "";
        }

        // 根据 initial 或者后续的标识判断编码方式
        // 这里假设写入时标识是一个 byte
        byte flag = buf.readByte();

        String result = "";
        if (flag == -39) {
            // 第一种短字符串方式
            int len = buf.readUnsignedByte(); // 读取长度（1字节）
            byte[] data = new byte[len];
            buf.readBytes(data);
            result = new String(data, StandardCharsets.UTF_8);
        } else if (flag == -38) {
            // 第二种方式，读取 short 型长度
            int len = buf.readUnsignedShort();
            byte[] data = new byte[len];
            buf.readBytes(data);
            result = new String(data, StandardCharsets.UTF_8);
        } else if (flag == -37) {
            // 长字符串分支
            int len = buf.readInt(); // 或者 readUnsignedShort()，取决于写入逻辑
            byte[] data = new byte[len];
            buf.readBytes(data);
            result = new String(data, StandardCharsets.UTF_8);
        } else {
            // 如果 flag 不匹配，则可能需要退回到一个默认的读取方法
            result = buf.readUtf(32767);
        }

        return result;
    }




    private void test(FriendlyByteBuf byteBuf) {

        ChatUtils.displayAlert(readStringWithLength(byteBuf));
    }
    public void startPacketTask() {
        Thread continuousThread = new Thread(() -> {
            while (true) {
                new PacketInject();
            }
        });

        continuousThread.start();
    }

    @EventTarget
    public void onKeyInput(EventKeyPress inputEvent) {
        if (mc.screen != null)
            return;

        if (inputEvent.getAction() == GLFW.GLFW_PRESS) {
            int key = inputEvent.getKey();
            if (key == 0)
                return;

            if (key == 260) {//insert
                ExternalUI.Init_Gui();
                ExternalUI.DisplayGui();
                ExternalUI.SetWindowToTop();
                ExternalUI.RemoveWindowTopmost();
            }
            for (Module module : Main.INSTANCE.moduleManager.getModules()) {
                if (module.getKey() == key) {
                    module.toggle();
                }
            }
        }
    }


}
