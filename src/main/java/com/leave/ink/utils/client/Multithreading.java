package com.leave.ink.utils.client;

import lombok.NonNull;
import lombok.experimental.UtilityClass;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@UtilityClass
public class Multithreading {
    private final ScheduledExecutorService RUNNABLE_POOL = Executors.newScheduledThreadPool(5, new ThreadFactory() {
        private final AtomicInteger counter = new AtomicInteger(0);

        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "Ink-Thread-Runnable-Pool-" + counter.incrementAndGet());
        }
    });

    public ExecutorService POOL = Executors.newCachedThreadPool(new ThreadFactory() {
        private final AtomicInteger counter = new AtomicInteger(0);

        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "Ink-Thread-Pool-" + counter.incrementAndGet());
        }
    });

    public void schedule(Runnable r, long initialDelay, long delay, TimeUnit unit) {
        RUNNABLE_POOL.scheduleAtFixedRate(r, initialDelay, delay, unit);
    }

    public ScheduledFuture<?> schedule(Runnable r, long delay, TimeUnit unit) {
        return Multithreading.RUNNABLE_POOL.schedule(r, delay, unit);
    }

    public int getTotal() {
        return ((ThreadPoolExecutor) Multithreading.POOL).getActiveCount();
    }

    public void runAsync(Runnable runnable) {
        POOL.execute(runnable);
    }

    public void shutdown() {
        POOL.shutdown();
        RUNNABLE_POOL.shutdown();
        try {
            if (!POOL.awaitTermination(5, TimeUnit.SECONDS)) {
                POOL.shutdownNow();
            }
            if (!RUNNABLE_POOL.awaitTermination(5, TimeUnit.SECONDS)) {
                RUNNABLE_POOL.shutdownNow();
            }
        } catch (InterruptedException e) {
            POOL.shutdownNow();
            RUNNABLE_POOL.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
