package com.leave.ink.utils.client;

import com.darkmagician6.eventapi.EventManager;
import com.leave.ink.events.EventPacket;
import com.leave.ink.utils.network.PacketUtil;
import com.leave.ink.utils.wrapper.IMinecraft;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.ChannelPromise;
import net.minecraft.network.protocol.Packet;

import java.util.Objects;

public class PacketInject extends ChannelDuplexHandler implements IMinecraft {
    public PacketInject() {
        try {
            ChannelPipeline pipeline = Objects.requireNonNull(mc.getConnection()).getConnection().channel().pipeline();
            pipeline.addBefore("packet_handler", "PacketHandler", this);
        } catch (Exception ignored) {
        }
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        EventPacket e = new EventPacket((Packet<?>) msg, EventPacket.PacketType.Client);
        if(!PacketUtil.handleSendPacket(e.getPacket())) {
            EventManager.call(e);
            if (e.isCancelled()) {
                return;
            }
        }

        super.write(ctx, e.getPacket(), promise);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        EventPacket e = new EventPacket((Packet<?>) msg, EventPacket.PacketType.Server);
        EventManager.call(e);
        if (e.isCancelled()) {
            return;
        }
        super.channelRead(ctx, e.getPacket());
    }
}
