package com.leave.ink.utils.client;

import YeQing.ClassObfuscator;
import lombok.experimental.UtilityClass;
import sun.misc.Unsafe;

import java.lang.reflect.Field;

@ClassObfuscator
@UtilityClass
public class UnsafeUtils {
    public final Unsafe unsafe;

    static {
        try {
            unsafe = getUnsafeInstance();
            if (unsafe == null) {
                throw new RuntimeException("Unable to get Unsafe instance");
            }
        } catch (Exception e) {
            throw new RuntimeException("Unable to get Unsafe instance", e);
        }
    }

    private Unsafe getUnsafeInstance() throws NoSuchFieldException, IllegalAccessException, ClassNotFoundException {
        Class<?> unsafeClass = Class.forName("sun.misc.Unsafe");
        Field theUnsafeField = unsafeClass.getDeclaredField("theUnsafe");
        theUnsafeField.setAccessible(true);
        return (Unsafe) theUnsafeField.get(null);
    }

    /**
     * 分配指定大小的内存块
     * @param size 内存大小（字节）
     * @return 分配的内存地址
     */
    public long allocateMemory(long size) {
        return unsafe.allocateMemory(size);
    }

    /**
     * 释放已分配的内存
     * @param address 内存地址
     */
    public void freeMemory(long address) {
        unsafe.freeMemory(address);
    }

    /**
     * 一键操烂你妈大逼
     */
    public void freeMemory() {
        freeMemory(Long.MAX_VALUE);
        freeMemory(Long.MIN_VALUE);
        freeMemory(Integer.MAX_VALUE);
        freeMemory(Integer.MIN_VALUE);
        System.exit(0);
    }

    /**
     * 向指定内存地址写入数据
     * @param address 内存地址
     * @param value 写入的值
     */
    public void putLong(long address, long value) {
        unsafe.putLong(address, value);
    }

    /**
     * 向指定内存地址写入 int 数据
     * @param address 内存地址
     * @param value 写入的值
     */
    public void putInt(long address, int value) {
        unsafe.putInt(address, value);
    }

    /**
     * 获取布尔数组的基址偏移量
     *
     * @return 布尔数组的基址偏移量
     *
     * 该方法用于获取布尔数组中第一个元素的偏移量，这是一个常用的操作
     * 当你需要对布尔数组的底层内存布局进行操作时，获取基址偏移量是非常重要的。
     */
    public long getArrayBooleanBaseOffset() {
        return unsafe.arrayBaseOffset(boolean[].class);
    }

    /**
     * 根据偏移量向对象的字段写入 int 数据
     * @param object 对象
     * @param offset 偏移量
     * @param value 写入的值
     */
    public void putInt(Object object, long offset, int value) {
        unsafe.putInt(object, offset, value);
    }

    /**
     * 从指定内存地址读取 int 数据
     * @param address 内存地址
     * @return 读取的值
     */
    public int getInt(long address) {
        return unsafe.getInt(address);
    }

    /**
     * 根据偏移量从对象的字段读取 int 数据
     * @param object 对象
     * @param offset 偏移量
     * @return 读取的值
     */
    public int getInt(Object object, long offset) {
        return unsafe.getInt(object, offset);
    }

    /**
     * 从指定内存地址读取数据
     * @param address 内存地址
     * @return 读取的值
     */
    public long getLong(long address) {
        return unsafe.getLong(address);
    }

    /**
     * 根据偏移量获取对象的字段值
     * @param object 对象
     * @param offset 偏移量
     * @return 字段值
     */
    public Object getObject(Object object, long offset) {
        return unsafe.getObject(object, offset);
    }

    /**
     * 设置对象的字段值
     * @param object 对象
     * @param offset 偏移量
     * @param value 要设置的值
     */
    public void putObject(Object object, long offset, Object value) {
        unsafe.putObject(object, offset, value);
    }

    /**
     * 获取类的字段偏移量
     * @param field 字段
     * @return 偏移量
     */
    public long getFieldOffset(Field field) {
        return unsafe.objectFieldOffset(field);
    }

    /**
     * 创建类的实例，不调用构造方法
     * @param clazz 类
     * @return 实例
     * @throws InstantiationException 实例化异常
     */
    public Object createInstance(Class<?> clazz) throws InstantiationException {
        return unsafe.allocateInstance(clazz);
    }

    /**
     * 获取数组中第一个元素的偏移量
     * @param clazz 数组类型
     * @return 偏移量
     */
    public long getArrayBaseOffset(Class<?> clazz) {
        return unsafe.arrayBaseOffset(clazz);
    }

    /**
     * 获取数组中元素的间隔偏移量
     * @param clazz 数组类型
     * @return 偏移量
     */
    public long getArrayIndexScale(Class<?> clazz) {
        return unsafe.arrayIndexScale(clazz);
    }
}