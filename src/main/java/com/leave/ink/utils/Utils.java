package com.leave.ink.utils;

import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.ChatFormatting;
import com.leave.ink.features.module.modules.settings.Targets;
import net.minecraft.network.chat.Style;
import net.minecraft.network.chat.TextColor;
import net.minecraft.util.FormattedCharSequence;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Player;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.WeakHashMap;
import java.util.concurrent.ConcurrentHashMap;

public class Utils implements IMinecraft {

    public static boolean isNull() {
        return mc == null || mc.player == null || mc.level == null || mc.gameMode == null || mc.getConnection() == null;
    }

    public static String getConfigPath() {
        return "C:\\Leave\\cfg";
    }

    public static boolean isValidEntity(LivingEntity entityLivingBase) {
        if (entityLivingBase instanceof Animal) {
            return Targets.animals.getValue();
        }
        if (entityLivingBase instanceof Villager) {
            return Targets.villagers.getValue();
        }
        if (entityLivingBase instanceof Player) {
            return Targets.player.getValue();
        }
        if (entityLivingBase instanceof Mob) {
            return Targets.mobs.getValue();
        }

        return false;
    }

    public static String getMainPath() {
        return "C:\\Leave";
    }

    public static String  getResourcePath() {
        return getMainPath() + "\\resource";
    }

    public static String getMainClassName() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        return stackTrace[stackTrace.length - 1].getClassName();
    }

    private static final Map<Integer, String> COLOR_FORMAT_CACHE = new HashMap<>();
    private static final ThreadLocal<StringBuilder> STYLE_BUILDER = ThreadLocal.withInitial(() -> new StringBuilder(32));
    private static final Map<FormattedCharSequence, WeakReference<String>> formattedCache = new ConcurrentHashMap<>();

    static {
        for (ChatFormatting formatting : ChatFormatting.values()) {
            if (formatting.isColor()) {
                COLOR_FORMAT_CACHE.put(formatting.getColor(), "§" + formatting.getChar());
            }
        }
    }

    public static String getStringFromFormattedCharSequence(FormattedCharSequence formattedCharSequence) {
        StringBuilder builder = STYLE_BUILDER.get();
        builder.setLength(0);

        final Style[] lastStyle = {null};
        final StringBuilder[] styleBuilder = {null};

        formattedCharSequence.accept((index, style, codePoint) -> {
            if (!Objects.equals(style, lastStyle[0])) {
                if (styleBuilder[0] == null) {
                    styleBuilder[0] = new StringBuilder(16);
                } else {
                    styleBuilder[0].setLength(0);
                }
                appendStyle(styleBuilder[0], style);
                builder.append(styleBuilder[0]);
                lastStyle[0] = style;
            }
            builder.appendCodePoint(codePoint);
            return true;
        });

        return builder.toString();
    }

    public static String getStringCached(FormattedCharSequence sequence) {
        WeakReference<String> cachedRef = formattedCache.get(sequence);
        String cached = (cachedRef != null) ? cachedRef.get() : null;
        if (cached == null) {
            cached = getStringFromFormattedCharSequence(sequence);
            formattedCache.put(sequence, new WeakReference<>(cached));
        }
        return cached;
    }

    public static void appendStyle(StringBuilder builder, Style style) {
        if (style == null) return;

        TextColor color = style.getColor();
        if (color != null) {
            String cachedFormat = COLOR_FORMAT_CACHE.get(color.getValue());
            if (cachedFormat != null) {
                builder.append(cachedFormat);
            } else {
                int rgb = color.getValue();
                StringBuilder rgbBuilder = new StringBuilder(14);
                rgbBuilder.append("§x");
                for (int i = 5; i >= 0; i--) {
                    int nibble = (rgb >> (i * 4)) & 0xF;
                    rgbBuilder.append('§').append(Character.forDigit(nibble, 16));
                }
                String rgbFormat = rgbBuilder.toString();
                COLOR_FORMAT_CACHE.put(color.getValue(), rgbFormat);
                builder.append(rgbFormat);
            }
        }

        if (style.isBold()) builder.append('§').append(ChatFormatting.BOLD.getChar());
        if (style.isItalic()) builder.append('§').append(ChatFormatting.ITALIC.getChar());
        if (style.isUnderlined()) builder.append('§').append(ChatFormatting.UNDERLINE.getChar());
        if (style.isStrikethrough()) builder.append('§').append(ChatFormatting.STRIKETHROUGH.getChar());
        if (style.isObfuscated()) builder.append('§').append(ChatFormatting.OBFUSCATED.getChar());
    }
}
