package com.leave.ink.utils.timer;

import net.minecraft.network.protocol.Packet;

public class TimedPacket {
    private final Packet<?> packet;
    private final TimeUtils time;
    private final long millis;

    public TimedPacket(Packet<?> packet) {
        this.packet = packet;
        this.time = new TimeUtils();
        this.millis = System.currentTimeMillis();
    }

    public Packet<?> getPacket() {
        return packet;
    }

    public TimeUtils getCold() {
        return getTime();
    }

    public TimeUtils getTime() {
        return time;
    }

    public long getMillis() {
        return millis;
    }
}
