package com.leave.ink.utils.rotation;

import com.leave.ink.utils.wrapper.IMinecraft;
import lombok.Getter;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;

@Getter
public class ClipRotation implements IMinecraft {
    private final Rotation rotation;
    private final boolean canSee;
    private final double range;

    private ClipRotation(Rotation rotation, boolean canSee, double range) {
        this.rotation = rotation;
        this.canSee = canSee;
        this.range = range;
    }

    public static ClipRotation getClipEntity(Entity entity, double wallRange) {
        Vec3 eyePos = mc.player.getEyePosition(1.0f);
        AABB box = entity.getBoundingBox();
        double width = box.getXsize();
        double height = box.getYsize();
        double centerX = (box.minX + box.maxX) / 2.0;
        double centerZ = (box.minZ + box.maxZ) / 2.0;

        Vec3[] points = new Vec3[]{
                new Vec3(centerX, box.maxY - 0.3, centerZ),
                new Vec3(centerX, box.maxY + 0.04, centerZ),
                new Vec3(centerX - width * 0.25, box.maxY - 0.1, centerZ),
                new Vec3(centerX + width * 0.25, box.maxY - 0.1, centerZ),
                new Vec3(centerX, box.minY + height * 0.65, centerZ),
                new Vec3(centerX - width * 0.3, box.minY + height * 0.65, centerZ),
                new Vec3(centerX + width * 0.3, box.minY + height * 0.65, centerZ),
                new Vec3(centerX, box.minY + height * 0.4, centerZ),
                new Vec3(centerX - width * 0.2, box.minY + height * 0.15, centerZ),
                new Vec3(centerX + width * 0.2, box.minY + height * 0.15, centerZ)
        };

        for (Vec3 point : points) {
            BlockHitResult hitResult = mc.level.clip(new ClipContext(
                    eyePos,
                    point,
                    ClipContext.Block.COLLIDER,
                    ClipContext.Fluid.NONE,
                    mc.player
            ));

            double range = eyePos.distanceTo(hitResult.getLocation());

            if (hitResult.getType() != HitResult.Type.BLOCK || hitResult.getLocation().distanceToSqr(eyePos) >= point.distanceToSqr(eyePos) - 0.01) {
                Vec3 delta = point.subtract(eyePos);
                double dx = delta.x;
                double dy = delta.y;
                double dz = delta.z;
                float yRot = (float) (Mth.atan2(-dx, dz) * (180F / Math.PI));
                float xRot = (float) (-(Mth.atan2(dy, Math.sqrt(dx * dx + dz * dz)) * (180F / Math.PI)));
                return new ClipRotation(new Rotation(yRot, xRot), true, range);
            } else if (wallRange != 0) {
                return new ClipRotation(getHVHRotation(entity), range < wallRange, range);
            }
        }

        return null;
    }

    public static Rotation getHVHRotation(final Entity entity) {

        final double diffX = entity.getX() - mc.player.getX();
        final double diffZ = entity.getZ() - mc.player.getZ();
        final Vec3 BestPos = RotationUtils.getNearestPointBB(mc.player.getEyePosition(1.0f), entity.getBoundingBox());
        final Location myEyePos = new Location(mc.player.getX(), mc.player.getY() + mc.player.getEyeHeight(), mc.player.getZ());
        final double diffY = BestPos.y - myEyePos.getY();
        final double dist = Mth.sqrt((float) (diffX * diffX + diffZ * diffZ));
        final float yaw = (float) (Math.atan2(diffZ, diffX) * 180.0 / 3.141592653589793) - 90.0f;
        final float pitch = (float) (-(Math.atan2(diffY, dist) * 180.0 / 3.141592653589793));

        return Rotation.getFixedRotation(new Rotation(yaw, pitch));
    }
}
