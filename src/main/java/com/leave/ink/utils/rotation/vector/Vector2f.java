package com.leave.ink.utils.rotation.vector;

public final class Vector2f {
    private float yaw, pitch; //x, y

    public Vector2f(float targetYaw, float targetPitch) {
        this.yaw = targetYaw;
        this.pitch = targetPitch;
    }

    public float getYaw() {
        return yaw;
    }

    public void setYaw(float yaw) {
        this.yaw = yaw;
    }

    public float getPitch() {
        return pitch;
    }

    public void setPitch(float pitch) {
        this.pitch = pitch;
    }
}
