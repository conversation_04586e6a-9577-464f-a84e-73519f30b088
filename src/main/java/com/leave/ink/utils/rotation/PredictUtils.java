package com.leave.ink.utils.rotation;

import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PredictUtils implements IMinecraft {
    private static final Map<Integer, List<Vec3>> playerPosHistory = new HashMap<>();
    private static final Map<Integer, List<Vec3>> playerMotionHistory = new HashMap<>();
    private static final Map<Integer, Boolean> playerGroundState = new HashMap<>();
    private static final Map<Integer, List<Double>> playerSpeedHistory = new HashMap<>();
    private static final int HISTORY_SIZE = 8;
    public static void recordPlayerMovement(Entity entity) {
        if (entity == null) return;

        int entityId = entity.getId();
        Vec3 currentPosition = new Vec3(entity.getX(), entity.getY(), entity.getZ());
        Vec3 currentMotion = new Vec3(entity.getX() - entity.xOld, entity.getY() - entity.yOld, entity.getZ() - entity.zOld);

        List<Vec3> positionHistory = playerPosHistory.getOrDefault(entityId, new ArrayList<>());
        positionHistory.add(currentPosition);
        if (positionHistory.size() > HISTORY_SIZE) {
            positionHistory.remove(0);
        }
        playerPosHistory.put(entityId, positionHistory);

        List<Vec3> motionHistory = playerMotionHistory.getOrDefault(entityId, new ArrayList<>());
        motionHistory.add(currentMotion);
        if (motionHistory.size() > HISTORY_SIZE) {
            motionHistory.remove(0);
        }
        playerMotionHistory.put(entityId, motionHistory);

        double currentSpeed = Math.sqrt(currentMotion.x * currentMotion.x + currentMotion.z * currentMotion.z);
        List<Double> speedHistory = playerSpeedHistory.getOrDefault(entityId, new ArrayList<>());
        speedHistory.add(currentSpeed);
        if (speedHistory.size() > HISTORY_SIZE) {
            speedHistory.remove(0);
        }
        playerSpeedHistory.put(entityId, speedHistory);

        playerGroundState.put(entityId, entity.onGround());
    }



    public static MovementPattern determineMovementType(int entityId) {
        List<Vec3> motionHistory = playerMotionHistory.getOrDefault(entityId, new ArrayList<>());
        List<Double> speedHistory = playerSpeedHistory.getOrDefault(entityId, new ArrayList<>());
        List<Vec3> positionHistory = playerPosHistory.getOrDefault(entityId, new ArrayList<>());

        if (motionHistory.size() < 3 || speedHistory.size() < 3 || positionHistory.isEmpty()) {
            return new MovementPattern(MovementPattern.MovementType.UNKNOWN, 0, 0, false,
                    positionHistory.isEmpty() ? new Vec3(0, 0, 0) : positionHistory.get(positionHistory.size()-1));
        }

        Vec3 currentMotion = motionHistory.get(motionHistory.size() - 1);
        Vec3 prevMotion = motionHistory.get(motionHistory.size() - 2);

        double currentSpeed = speedHistory.get(speedHistory.size() - 1);
        double prevSpeed = speedHistory.get(speedHistory.size() - 2);
        double prevPrevSpeed = speedHistory.get(speedHistory.size() - 3);

        MovementPattern.MovementType type;

        boolean isInAir = !playerGroundState.getOrDefault(entityId, true);

        double currentAngle = Math.atan2(currentMotion.z, currentMotion.x);
        double prevAngle = Math.atan2(prevMotion.z, prevMotion.x);
        double angleDiff = Math.abs(currentAngle - prevAngle);

        if (angleDiff > Math.PI) {
            angleDiff = 2 * Math.PI - angleDiff;
        }

        double acceleration = currentSpeed - prevSpeed;
        double prevAcceleration = prevSpeed - prevPrevSpeed;

        if (angleDiff > 0.3) {
            type = MovementPattern.MovementType.TURNING;
        } else if (acceleration > 0.01 && prevAcceleration > 0) {
            type = MovementPattern.MovementType.ACCELERATING;
        } else if (acceleration < -0.01 && prevAcceleration < 0) {
            type = MovementPattern.MovementType.DECELERATING;
        } else if (Math.abs(acceleration) < 0.01) {
            type = MovementPattern.MovementType.CONSTANT;
        } else if (acceleration > 0.01 && prevAcceleration <= 0) {
            type = MovementPattern.MovementType.START_ACCELERATING;
        } else if (acceleration < -0.01 && prevAcceleration >= 0) {
            type = MovementPattern.MovementType.START_DECELERATING;
        } else {
            type = MovementPattern.MovementType.FLUCTUATING;
        }

        double turnTrend = angleDiff > 0.1 ? currentAngle - prevAngle : 0;

        if (turnTrend > Math.PI) {
            turnTrend -= 2 * Math.PI;
        } else if (turnTrend < -Math.PI) {
            turnTrend += 2 * Math.PI;
        }

        Vec3 lastPosition = positionHistory.get(positionHistory.size() - 1);

        return new MovementPattern(type, currentAngle, turnTrend, isInAir, lastPosition);
    }
    public static double estimateProjectileHitTime(Entity target, double g, double v) {
        if (mc.player == null || mc.level == null) return 0;

        MovementPattern pattern = determineMovementType(target.getId());

        double offsetX = target.getX() - mc.player.getX();
        double offsetZ = target.getZ() - mc.player.getZ();

        double horizontalDist = Math.sqrt(offsetX * offsetX + offsetZ * offsetZ);

        double velocity = v;
        double airResistance = 0.99;
        double gravity = g;

        double baseFlightTime = horizontalDist / velocity;

        double targetMotionX = target.getX() - target.xOld;
        double targetMotionZ = target.getZ() - target.zOld;
        double targetSpeed = Math.sqrt(targetMotionX * targetMotionX + targetMotionZ * targetMotionZ);

        double targetAngle = Math.atan2(targetMotionZ, targetMotionX);
        double throwAngle = Math.atan2(offsetZ, offsetX);
        double angleDiff = Math.abs(targetAngle - throwAngle);

        if (angleDiff > Math.PI) {
            angleDiff = 2 * Math.PI - angleDiff;
        }

        double targetSpeedAlongThrow = targetSpeed * Math.cos(angleDiff);

        double relativeSpeedFactor;

        if (Math.abs(angleDiff) < Math.PI / 2) {
            relativeSpeedFactor = 0.85;
        } else if (Math.abs(angleDiff) > Math.PI * 0.75) {
            relativeSpeedFactor = 1.4 + Math.min(targetSpeed * 2, 0.8);
        } else {
            relativeSpeedFactor = 1.15;
        }

        double distanceFactor = Math.min(1.0 + (horizontalDist / 20.0) * 0.5, 1.8);

        double speedAdjustedTime = baseFlightTime * (1.0 + targetSpeedAlongThrow * 0.8);

        double adjustedTime = speedAdjustedTime * relativeSpeedFactor * distanceFactor;

        adjustedTime = adjustTimeByMovement(adjustedTime, pattern, targetSpeed, horizontalDist);

        double bestHitTime = adjustedTime;
        double minError = Double.MAX_VALUE;

        double searchRadius = Math.max(adjustedTime * 0.25, 5.0);
        double searchStep = Math.max(adjustedTime * 0.05, 0.5);

        for (double timeGuess = adjustedTime - searchRadius;
             timeGuess <= adjustedTime + searchRadius;
             timeGuess += searchStep) {

            if (timeGuess <= 0) continue;

            double error = calculateProjectilePath(target, timeGuess, velocity, airResistance, gravity);

            if (error < minError) {
                minError = error;
                bestHitTime = timeGuess;
            }
        }

        double refinedSearchRadius = searchStep * 2;
        double refinedStep = searchStep / 4;

        for (double timeGuess = bestHitTime - refinedSearchRadius;
             timeGuess <= bestHitTime + refinedSearchRadius;
             timeGuess += refinedStep) {

            if (timeGuess <= 0) continue;

            double error = calculateProjectilePath(target, timeGuess, velocity, airResistance, gravity);

            if (error < minError) {
                minError = error;
                bestHitTime = timeGuess;
            }
        }

        double finalTime = bestHitTime * 1.05;
/*
        if (showDebug.getValue()) ClientUtils.log(String.format(
                "Target speed: %.2f, Angle diff: %.2f°, Time factor: %.2f, Final time: %.2f",
                targetSpeed * 20,
                angleDiff * 180 / Math.PI,
                relativeSpeedFactor * distanceFactor,
                finalTime
        ));

 */

        return Math.max(finalTime, 1.0);
    }
    private static double calcYVelocity(double targetY, double time, double gravity, double airResistance) {
        double vy;
        double y;
        double dt = 0.5;

        double minVy = -10;
        double maxVy = 10;

        for (int iter = 0; iter < 10; iter++) {
            double midVy = (minVy + maxVy) / 2;

            y = 0;
            vy = midVy;

            for (double t = 0; t < time; t += dt) {
                y += vy * dt;
                vy = vy * airResistance - gravity * dt;
            }

            if (y < targetY) {
                minVy = midVy;
            } else {
                maxVy = midVy;
            }
        }

        return (minVy + maxVy) / 2;
    }
    private static double calcHorizVelocity(double distance, double time, double airResistance) {
        double minV = 0;
        double maxV = 5;

        for (int iter = 0; iter < 10; iter++) {
            double midV = (minV + maxV) / 2;

            double d = 0;
            double v = midV;

            for (int t = 0; t < time; t++) {
                d += v;
                v *= airResistance;
            }

            if (d < distance) {
                minV = midV;
            } else {
                maxV = midV;
            }
        }

        return (minV + maxV) / 2 * 1.02;
    }

    private static double calculateProjectilePath(Entity target, double time,
                                           double velocity, double airResistance, double gravity) {
        Vec3 predictedTargetPos = predictAdvancedPosition(target, time);

        if (predictedTargetPos == null || mc.player == null || mc.level == null) return Double.MAX_VALUE;

        double dx = predictedTargetPos.x - mc.player.getX();
        double dy = predictedTargetPos.y - (mc.player.getY() + mc.player.getEyeHeight());
        double dz = predictedTargetPos.z - mc.player.getZ();

        double horizontalDist = Math.sqrt(dx * dx + dz * dz);

        double initialYVelocity = calcYVelocity(dy, time, gravity, airResistance);

        double horizontalVelocity = calcHorizVelocity(horizontalDist, time, airResistance);

        if (Math.sqrt(horizontalVelocity * horizontalVelocity + initialYVelocity * initialYVelocity) > velocity * 1.1) {
            return 100.0;
        }

        double vx = (dx / horizontalDist) * horizontalVelocity;
        double vz = (dz / horizontalDist) * horizontalVelocity;

        double snowballX = 0;
        double snowballY = 0;
        double snowballZ = 0;

        double motionX = vx;
        double motionY = initialYVelocity;
        double motionZ = vz;

        for (int tick = 0; tick < Math.ceil(time); tick++) {
            snowballX += motionX;
            snowballY += motionY;
            snowballZ += motionZ;

            motionX *= airResistance;
            motionY *= airResistance;
            motionZ *= airResistance;

            motionY -= gravity;
        }

        double finalX = mc.player.getX() + snowballX;
        double finalY = mc.player.getY() + mc.player.getEyeHeight() + snowballY;
        double finalZ = mc.player.getZ() + snowballZ;

        return Math.sqrt(
                Math.pow(finalX - predictedTargetPos.x, 2) +
                        Math.pow(finalY - predictedTargetPos.y, 2) +
                        Math.pow(finalZ - predictedTargetPos.z, 2)
        );
    }
    public static Vec3 forecastPlayerLocation(Entity target, double ticks) {
        if (target == null || mc.level == null) return null;

        recordPlayerMovement(target);

        MovementPattern pattern = determineMovementType(target.getId());

        double motionX = target.getX() - target.xOld;
        double motionY = target.getY() - target.yOld;
        double motionZ = target.getZ() - target.zOld;

        double predictedX = target.getX();
        double predictedY = target.getY();
        double predictedZ = target.getZ();

        boolean onGround = target.onGround();

        double speedMultiplier;
        double turnMultiplier = 0.0;

        speedMultiplier = switch (pattern.type) {
            case ACCELERATING -> 1.05 + Math.min(ticks * 0.01, 0.15);
            case START_ACCELERATING -> 1.03 + Math.min(ticks * 0.008, 0.12);
            case DECELERATING -> 0.97 - Math.min(ticks * 0.01, 0.15);
            case START_DECELERATING -> 0.98 - Math.min(ticks * 0.008, 0.12);
            case TURNING -> {
                turnMultiplier = pattern.turnTrend * 0.8;
                yield 0.95;
            }
            default -> 1.0;
        };

        motionX *= speedMultiplier;
        motionZ *= speedMultiplier;

        if (Math.abs(turnMultiplier) > 0.01) {
            double currentAngle = pattern.turnAngle;
            double speed = Math.sqrt(motionX * motionX + motionZ * motionZ);

            double newAngle = currentAngle + turnMultiplier;
            motionX = Math.cos(newAngle) * speed;
            motionZ = Math.sin(newAngle) * speed;
        }

        if (pattern.isInAir) {
            motionY = Math.max(motionY - 0.08, -3.92);
        }

        for (int i = 0; i < Math.min(ticks, 20); i++) {
            double friction = onGround ? 0.91 : 0.98;

            motionX *= friction;
            motionZ *= friction;

            if (onGround) {
                if (Math.abs(motionY) < 0.005) {
                    motionY = 0;
                } else {
                    motionY *= 0.98;
                }
            } else {
                motionY -= 0.08;
                motionY *= 0.98;

                if (motionY < -3.92) {
                    motionY = -3.92;
                }
            }

            predictedX += motionX;
            predictedY += motionY;
            predictedZ += motionZ;

            if (predictedY <= target.getY() && Math.abs(motionY) < 0.005) {
                predictedY = target.getY();
                onGround = true;
            } else if (motionY < 0) {
                BlockPos pos = new BlockPos((int)predictedX, (int)(predictedY - 0.2), (int)predictedZ);
                if (!mc.level.isEmptyBlock(pos)) {
                    predictedY = Math.floor(predictedY) + 1.0;
                    motionY = 0;
                    onGround = true;
                } else {
                    onGround = false;
                }
            } else {
                onGround = false;
            }
        }

        return new Vec3(predictedX, predictedY + target.getEyeHeight(), predictedZ);
    }


    private static Vec3 predictAdvancedPosition(Entity target, double ticks) {
        Vec3 basePosition = forecastPlayerLocation(target, ticks);

        if (basePosition == null) return null;

        List<Vec3> posHistory = playerPosHistory.get(target.getId());
        List<Vec3> motionHistory = playerMotionHistory.get(target.getId());

        if (posHistory == null || posHistory.size() < 2 ||
                motionHistory == null || motionHistory.size() < 2) {
            return basePosition;
        }

        Vec3 latestMotion = motionHistory.get(motionHistory.size() - 1);
        Vec3 prevMotion = motionHistory.get(motionHistory.size() - 2);

        double motionChange = Math.sqrt(
                Math.pow(latestMotion.x - prevMotion.x, 2) +
                        Math.pow(latestMotion.z - prevMotion.z, 2)
        );

        if (motionChange > 0.05) {
            double currentSpeed = Math.sqrt(latestMotion.x * latestMotion.x + latestMotion.z * latestMotion.z);

            double moveAngle = Math.atan2(latestMotion.z, latestMotion.x);

            double extraDistance = currentSpeed * ticks * 0.3;

            double newX = basePosition.x + Math.cos(moveAngle) * extraDistance;
            double newZ = basePosition.z + Math.sin(moveAngle) * extraDistance;

            return new Vec3(newX, basePosition.y, newZ);
        }

        return basePosition;
    }

    public static double adjustTimeByMovement(double time, MovementPattern pattern,
                                        double targetSpeed, double distance) {
        if (pattern == null || mc.player == null || mc.level == null) return time;

        double adjustFactor = 1.0;

        switch (pattern.type) {
            case ACCELERATING -> adjustFactor = 1.5 + Math.min(targetSpeed * 4, 0.6);
            case START_ACCELERATING -> adjustFactor = 1.15;
            case DECELERATING -> adjustFactor = 0.9;
            case START_DECELERATING -> adjustFactor = 0.95;
            case TURNING -> {
                double turnFactor = 1.05;
                double targetToPlayerAngle = Math.atan2(
                        mc.player.getZ() - pattern.lastPosition.z,
                        mc.player.getX() - pattern.lastPosition.x
                );
                double turnRelativeToPlayer = Math.cos(targetToPlayerAngle - pattern.turnAngle);
                if (turnRelativeToPlayer > 0.3) {
                    turnFactor = 0.9;
                } else if (turnRelativeToPlayer < -0.3) {
                    turnFactor = 1.3;
                }
                adjustFactor = turnFactor;
            }
            case CONSTANT -> {
                if (targetSpeed > 0.15) {
                    adjustFactor = 1.0 + Math.min(distance / 15.0, 0.5);
                }
            }
            default -> adjustFactor = 1.05;
        }

        return time * adjustFactor;
    }

}
