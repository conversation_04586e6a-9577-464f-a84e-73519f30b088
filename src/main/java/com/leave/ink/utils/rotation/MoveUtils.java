package com.leave.ink.utils.rotation;

import com.leave.ink.events.EventMoveInput;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.util.Mth;

public class MoveUtils implements IMinecraft {
    public static double direction(float rotationYaw, final double moveForward, final double moveStrafing) {
        if (moveForward < 0F) rotationYaw += 180F;

        float forward = 1F;

        if (moveForward < 0F) forward = -0.5F;
        else if (moveForward > 0F) forward = 0.5F;

        if (moveStrafing > 0F) rotationYaw -= 90F * forward;
        if (moveStrafing < 0F) rotationYaw += 90F * forward;

        return Math.toRadians(rotationYaw);
    }

    public static void fixMovement(final EventMoveInput event, final float yaw) {
        final float forward = event.getForward();
        final float strafe = event.getStrafe();
//        final double angle = Mth.wrapDegrees(Math.toDegrees(MoveUtils.direction(mc.player.getYRot(), forward, strafe)));
        final double angle = Mth.wrapDegrees(Math.toDegrees(MoveUtils.direction(Mth.wrapDegrees(mc.player.getYRot()), forward, strafe)));

        if (forward == 0 && strafe == 0) {
            return;
        }

        float closestForward = 0, closestStrafe = 0, closestDifference = Float.MAX_VALUE;

        for (float predictedForward = -1F; predictedForward <= 1F; predictedForward += 1F) {
            for (float predictedStrafe = -1F; predictedStrafe <= 1F; predictedStrafe += 1F) {
                if (predictedStrafe == 0 && predictedForward == 0) continue;

                final double predictedAngle = Mth.wrapDegrees(Math.toDegrees(MoveUtils.direction(Mth.wrapDegrees(yaw), predictedForward, predictedStrafe)));
                final double difference = Math.abs(angle - predictedAngle);

                if (predictedStrafe == 0.0f && predictedForward == 0.0f || !(difference < closestDifference))
                    continue;

                closestDifference = (float) difference;
                closestForward = predictedForward;
                closestStrafe = predictedStrafe;
            }
        }

        event.setForward(closestForward);
        event.setStrafe(closestStrafe);
    }
}
