package com.leave.ink.utils.rotation;

import com.leave.ink.utils.player.BlockUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

public class PlaceInfo implements IMinecraft {

    private final BlockPos blockPos;
    private final Direction enumFacing;
    private Vec3 vec3;

    public PlaceInfo(BlockPos blockPos, Direction enumFacing) {
        this.blockPos = blockPos;
        this.enumFacing = enumFacing;
        this.vec3 = new Vec3(blockPos.getX() + 0.5, blockPos.getY() + 0.5, blockPos.getZ() + 0.5);
    }

    public PlaceInfo(BlockPos blockPos, Direction enumFacing, Vec3 vec3) {
        this.blockPos = blockPos;
        this.enumFacing = enumFacing;
        this.vec3 = vec3;
    }

    public BlockPos getBlockPos() {
        return blockPos;
    }

    public Direction getEnumFacing() {
        return enumFacing;
    }

    public Vec3 getVec3() {
        return vec3;
    }

    public void setVec3(Vec3 vec3) {
        this.vec3 = vec3;
    }

    public static PlaceInfo get(BlockPos blockPos) {
        if (BlockUtils.canBeClicked(blockPos.above())) {
            return new PlaceInfo(blockPos.below(), Direction.UP);
        } else if (BlockUtils.canBeClicked(blockPos.north())) {
            return new PlaceInfo(blockPos.north(), Direction.NORTH);
        } else if (BlockUtils.canBeClicked(blockPos.east())) {
            return new PlaceInfo(blockPos.east(), Direction.EAST);
        } else if (BlockUtils.canBeClicked(blockPos.south())) {
            return new PlaceInfo(blockPos.south(), Direction.SOUTH);
        } else if (BlockUtils.canBeClicked(blockPos.west())) {
            return new PlaceInfo(blockPos.west(), Direction.WEST);
        } else {
            return null;
        }
    }

    public static  PlaceInfo checkNearBlocks(BlockPos blockPos) {
        Direction[] directions = {
                Direction.DOWN, Direction.EAST, Direction.WEST, Direction.NORTH, Direction.SOUTH
        };

        for (Direction direction : directions) {
            BlockPos checkPos = blockPos.relative(direction);
            BlockState state = mc.level.getBlockState(checkPos);

            if (state.isSolid() || state.isCollisionShapeFullBlock(mc.level, checkPos)) {
                return new PlaceInfo(checkPos, direction.getOpposite());
            }
        }

        return null;
    }
}
