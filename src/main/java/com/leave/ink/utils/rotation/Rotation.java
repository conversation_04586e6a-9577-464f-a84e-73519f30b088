package com.leave.ink.utils.rotation;

import com.leave.ink.utils.wrapper.IMinecraft;
import com.leave.ink.utils.Utils;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.player.Player;

public class Rotation implements IMinecraft {
    private float yaw;
    private float pitch;

    public Rotation(float yaw, float pitch) {
        this.yaw = yaw;
        this.pitch = pitch;

    }

    public float getYaw() {
        return yaw;
    }

    public static float getFixedYaw(float oriYaw) {
        if (Utils.isNull())
            return 0;

        int size = (int) (mc.player.getYRot() / 360);

        return oriYaw + (360 * size);
    }

    public static float getFixedPitch(float oriPitch) {
        if (Utils.isNull()) return 0;
        float finalPitch = mc.player.getXRot() + Mth.wrapDegrees(oriPitch - mc.player.getXRot());
        finalPitch = Mth.clamp(finalPitch, -90.0F, 90.0F);
        return finalPitch;
    }

    public static Rotation getFixedRotation(Rotation rotation) {
        return new Rotation(getFixedYaw(rotation.getYaw()), getFixedPitch(rotation.getPitch()));
    }

    public void setYaw(float yaw) {
        this.yaw = yaw;
    }

    public float getPitch() {
        return pitch;
    }

    public void setPitch(float pitch) {
        this.pitch = pitch;
    }

    public void toPlayer(Player player) {
        if (Float.isNaN(yaw) || Float.isNaN(pitch)) {
            return;
        }

        fixedSensitivity(mc.options.sensitivity().get().floatValue());

        player.setYRot(yaw);
        player.setXRot(pitch);
    }

    public void fixedSensitivity(double sensitivity) {
        double f = sensitivity * 0.6F + 0.2F;
        double gcd = f * f * f * 1.2F;

        Rotation rotation = new Rotation(yaw, pitch);

        float deltaYaw = yaw - rotation.getYaw();
        deltaYaw -= (float) (deltaYaw % gcd);
        yaw = rotation.getYaw() + deltaYaw;

        float deltaPitch = pitch - rotation.getPitch();
        deltaPitch -= (float) (deltaPitch % gcd);
        pitch = rotation.getPitch() + deltaPitch;
    }
}
