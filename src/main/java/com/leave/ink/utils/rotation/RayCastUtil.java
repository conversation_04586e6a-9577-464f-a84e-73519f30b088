package com.leave.ink.utils.rotation;

import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntitySelector;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.phys.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 11/17/2021
 */
public final class RayCastUtil implements IMinecraft {
    public static EntityHitResult rayCastEntity(final Rotation rotation, final double range) {
        HitResult hitResult = rayCast(rotation, range);
        if (hitResult == null) return null;
        if(!(hitResult instanceof EntityHitResult)) {
            return null;
        }
        return ((EntityHitResult) hitResult);
    }
    public static HitResult rayCast(final Rotation rotation, final double range) {

        return rayCast(rotation, range, 0);
    }

    public static HitResult rayCast(final Rotation rotation, final double range, final float expand) {
        return rayCast(rotation, range, expand, mc.getFrameTime());
    }

    public static HitResult rayCast(Rotation rots, double range, double hitBoxExpand, float partialTicks) {
        HitResult objectMouseOver;
        Entity entity = mc.getCameraEntity();
        if (mc.player == null || mc.level == null) {
            return null;
        }

        mc.getProfiler().push("pick");
        mc.crosshairPickEntity = null;
        double d0 = range;
        objectMouseOver = rayTraceCustom(entity, d0, partialTicks, rots.getYaw(), rots.getPitch());

        double d2 = d0;
        Vec3 eyePosition = entity.getEyePosition(partialTicks);
        boolean flag = false;

        if (mc.gameMode.hasFarPickRange()) {
            d0 = 6.0;
            d2 = 6.0;
        } else {
            if (d0 > 3.0) {
                flag = true;
            }
        }

        if (objectMouseOver != null) {
            d2 = objectMouseOver.getLocation().distanceTo(eyePosition);
        }

        Vec3 lookDirection = RotationUtils.getVectorForRotation(rots.getYaw(), rots.getPitch());
        Vec3 rayEnd = eyePosition.add(lookDirection.x * d0, lookDirection.y * d0, lookDirection.z * d0);

        List<Entity> entitiesInRange = mc.level.getEntities(entity, entity.getBoundingBox().inflate(d0), EntitySelector.NO_SPECTATORS);

        double closestDistance = d2;
        Entity closestEntity = null;
        Vec3 hitVec = null;

        for (Entity value : entitiesInRange) {
            AABB expandedBoundingBox = value.getBoundingBox().inflate(hitBoxExpand);
            Optional<Vec3> hitResult = expandedBoundingBox.clip(eyePosition, rayEnd);

            if (expandedBoundingBox.contains(eyePosition)) {
                if (closestDistance >= 0.0) {
                    closestEntity = entity;
                    hitVec = hitResult.orElse(eyePosition);
                    closestDistance = 0.0;
                }
            } else if (hitResult.isPresent()) {
                double distanceToHit = eyePosition.distanceTo(hitResult.get());
                if (distanceToHit < closestDistance || closestDistance == 0.0) {
                    closestEntity = entity;
                    hitVec = hitResult.get();
                    closestDistance = distanceToHit;
                }
            }
        }

        if (closestEntity != null && flag && eyePosition.distanceTo(hitVec) > range) {
            closestEntity = null;

            objectMouseOver = BlockHitResult.miss(hitVec, Direction.DOWN, BlockPos.containing(hitVec.x, hitVec.y, hitVec.z));
        }

        if (closestEntity != null && (closestDistance < d2 || objectMouseOver == null)) {
            objectMouseOver = new EntityHitResult(closestEntity, hitVec);
        }

        return objectMouseOver;
    }
    public static HitResult rayTraceCustom(Entity entity, double blockReachDistance,float partialTicks, float yaw, float pitch) {
        final Vec3 vec3 = entity.getEyePosition(partialTicks);
        final Vec3 vec31 = RotationUtils.getVectorForRotation(yaw, pitch);
        final Vec3 vec32 = vec3.add(vec31.x * blockReachDistance, vec31.y * blockReachDistance, vec31.z * blockReachDistance);
        return mc.level.clip(new ClipContext(vec3, vec32, ClipContext.Block.OUTLINE, ClipContext.Fluid.NONE, entity));
    }
}
