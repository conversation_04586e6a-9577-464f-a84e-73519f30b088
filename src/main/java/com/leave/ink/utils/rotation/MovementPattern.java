package com.leave.ink.utils.rotation;

import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MovementPattern {

    public final MovementType type;
    public final double turnAngle;
    public final double turnTrend;
    public final boolean isInAir;
    public final Vec3 lastPosition;

    public MovementPattern(MovementType type, double turnAngle, double turnTrend, boolean isInAir, Vec3 lastPosition) {
        this.type = type;
        this.turnAngle = turnAngle;
        this.turnTrend = turnTrend;
        this.isInAir = isInAir;
        this.lastPosition = lastPosition;
    }



    public enum MovementType {
        ACCELERATING,
        DECELERATING,
        CONSTANT,
        TURNING,
        START_ACCELERATING,
        START_DECELERATING,
        FLUCTUATING,
        UNKNOWN
    }
}
