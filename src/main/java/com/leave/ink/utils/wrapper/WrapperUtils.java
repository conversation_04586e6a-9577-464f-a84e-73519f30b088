package com.leave.ink.utils.wrapper;


import com.leave.ink.injection.transformers.client.ClientLevelTransformer;
import com.leave.ink.injection.transformers.client.TimerTransformer;
import com.leave.ink.utils.Utils;

import com.leave.ink.utils.reflect.ObfuscationReflectionHelper;
import lombok.experimental.UtilityClass;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.multiplayer.MultiPlayerGameMode;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.network.protocol.game.ServerboundMovePlayerPacket;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.entity.TransientEntitySectionManager;

@SuppressWarnings("all")
@UtilityClass
public class WrapperUtils implements IMinecraft {
    public void setPacketYRot(ServerboundMovePlayerPacket packet, float yRot) {
        ObfuscationReflectionHelper.setPrivateValue(ServerboundMovePlayerPacket.class, packet, yRot, "yRot");
    }

    public TransientEntitySectionManager<Entity> getEntityStorage() {
        if (mc.level == null) return null;
        return ObfuscationReflectionHelper.getPrivateValue(ClientLevel.class, mc.level,  "entityStorage");
    }

    public boolean getWasSprinting() {
        if (Utils.isNull()) return false;
        return Boolean.TRUE.equals(
                ObfuscationReflectionHelper.getPrivateValue(LocalPlayer.class, mc.player, "wasSprinting")
        );
    }

    public void setYRotLast(float yRot) {
        if (Utils.isNull()) return;
        ObfuscationReflectionHelper.setPrivateValue(LocalPlayer.class, mc.player, yRot, "yRotLast");
    }

    public float getYRotLast() {
        if (Utils.isNull()) return 0.0F;
        return ObfuscationReflectionHelper.getPrivateValue(LocalPlayer.class, mc.player,"yRotLast");
    }

    public void setPositionReminder(int positionReminder) {
        if (mc.player == null) return;
        ObfuscationReflectionHelper.setPrivateValue(LocalPlayer.class, mc.player,positionReminder,"positionReminder");
    }

    public float getXRotLast() {
        if (mc.player == null) return 0;
        return ObfuscationReflectionHelper.getPrivateValue(LocalPlayer.class, mc.player, "xRotLast");
    }

    public void setXRotLast(float xRot) {
        if (mc.player == null) return;
        // 确保pitch在合法范围内
        float safePitch = com.leave.ink.utils.misc.MathUtils.clampPitch_To90(xRot);
        ObfuscationReflectionHelper.setPrivateValue(LocalPlayer.class, mc.player, safePitch, "xRotLast");
    }

    public float getPacketYRot(ServerboundMovePlayerPacket packet) {
        if (mc.gameMode == null) return 0.0f;
        return ObfuscationReflectionHelper.getPrivateValue(ServerboundMovePlayerPacket.class, packet, "yRot");
    }

    public float getPacketXRot(ServerboundMovePlayerPacket packet) {
        if (mc.gameMode == null) return 0.0f;
        return ObfuscationReflectionHelper.getPrivateValue(ServerboundMovePlayerPacket.class, packet, "xRot");
    }

    public void setPacketXRot(ServerboundMovePlayerPacket packet, float xRot) {
        if (mc.gameMode == null) return;
        ObfuscationReflectionHelper.setPrivateValue(ServerboundMovePlayerPacket.class, packet, xRot, "xRot");
    }
    public int getSkipTicks() {
        return ClientLevelTransformer.getSkipTicks();
    }
    public void setSkipTicks(int skipTicks) {
        ClientLevelTransformer.setSkipTicks(skipTicks);
    }
    public void setMissTime(int missTime) {
        ObfuscationReflectionHelper.setPrivateValue(Minecraft.class, mc, missTime, "missTime");
    }

    public float getDestroyProgress() {
        if (mc.gameMode == null) return 0.0F;
        return ObfuscationReflectionHelper.getPrivateValue(MultiPlayerGameMode.class, mc.gameMode, "destroyProgress");
    }
}
