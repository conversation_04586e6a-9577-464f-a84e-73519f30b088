package com.external.ui;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ColorSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;

import java.util.Iterator;

import static com.external.ui.ExternalUI.*;

public class UISystem {
    public static void api_setDisplay(Setting<?> setting, SettingManager settingManager) {
        if(setting.getTexts() == null || settingManager == null) return;
        String name;

        if(settingManager instanceof Module) {
            name = ((Module) settingManager).getNameKey();
             UI_SetDisplay(setting.getName(),name, setting.isDisplay(), setting.getLevel());
        }else if(settingManager instanceof AbsHudElement) {
            name = ((AbsHudElement) settingManager).getElementName();
            UI_SetDisplay(setting.getName(),name, setting.isDisplay(), setting.getLevel());
        }
    }
    public static void updateModule() {
        for (Module module : Main.INSTANCE.moduleManager.getModules()) {
            UI_UpdateModuleKey(module.getNameKey(), module.getKey());
        }

    }
    public static void UI_Initialize(){
        ExternalUI.UI_Initialize();
    }

    //TODO
    public static void UpdateSetting() {
        //UI_UpdateScreen();
        for (Module module : Main.INSTANCE.moduleManager.getModules()) {
            for (Setting<?> setting : SettingManager.getSettings(module)) {
                if (setting instanceof BooleanSetting) {
                    UI_UpdateBooleanSettingsState(module.getNameKey(), setting.getName(), ((BooleanSetting) setting).getValue());
                } else if (setting instanceof NumberSetting) {
                    UI_UpdateNumberSettingsState(module.getNameKey(), setting.getName(), ((NumberSetting) setting).getValue().doubleValue());
                } else if (setting instanceof ModeSetting) {
                    //System.out.println("ModeSetting " + setting.getName() + " " + setting.isDisplay());
                    UI_UpdateStringSettingsState(module.getNameKey(), setting.getName(), ((ModeSetting) setting).getValue());
                }else if (setting instanceof ColorSetting) {
                    UI_UpdateColorSettingsState(module.getNameKey(), setting.getName(), ((ColorSetting) setting).getValue());
                }
            }
        }

//        ExternalUI.execute(() -> {
//            for (AbsHudElement absHudElement : Main.INSTANCE.hudManager.getHudElements()) {
//                System.out.println(absHudElement.getElementName());
//                for (Setting<?> setting : SettingManager.getSettings(absHudElement)) {
//                    if (setting instanceof BooleanSetting) {
//                        UI_UpdateBooleanSettingsState(absHudElement.getElementName(), setting.getName(), ((BooleanSetting) setting).getValue());
//                    } else if (setting instanceof NumberSetting) {
//                        UI_UpdateNumberSettingsState(absHudElement.getElementName(), setting.getName(), ((NumberSetting) setting).getValue().doubleValue());
//                    } else if (setting instanceof ModeSetting) {
//                        UI_UpdateStringSettingsState(absHudElement.getElementName(), setting.getName(), ((ModeSetting) setting).getValue());
//                        //System.out.println("ModeSetting " + setting.getName() + " " + setting.isDisplay());
//                    }else if (setting instanceof ColorSetting) {
//                        UI_UpdateColorSettingsState(absHudElement.getElementName(), setting.getName(), ((ColorSetting) setting).getValue());
//                    }
//                }
//            }
//        });

    }
}
