package com.external.ui;

import com.leave.ink.Main;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.setting.Setting;
import com.leave.ink.features.setting.SettingManager;
import com.leave.ink.features.setting.settings.*;
import com.leave.ink.language.Language;

import java.awt.*;
import java.awt.event.KeyEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class ExternalUI {
    public static String configName = "Default.json";

    public static void setConfigName(String text) {
        configName = text;
    }

    public static boolean onSave() {
        return Main.INSTANCE.configManager.save();
    }

    public static boolean onLoad() {
        return Main.INSTANCE.configManager.load();
    }

    public static native String sendHeartbeat0(String gameId);

    public static native String getValidToken();
    //TODO
    public static void UI_OnRegisterModules() {
        System.out.println("Register modules");
//        Thread.getAllStackTraces()
//        System.out.println(Thread.currentThread().getName() + " " + Main.uiThread.getName());

        for (Module module : Main.INSTANCE.moduleManager.getModules()) {
            UI_RegisterModule(module);
            for (Setting<?> setting : SettingManager.getSettings(module)) {

                if (setting instanceof BooleanSetting) {
                    UI_RegisterBooleanSetting(module.getNameKey(), setting.getName(),setting.getName(Language.Chinese), ((BooleanSetting) setting).getValue());
                } else if (setting instanceof NumberSetting) {
                    String str = "%." + ((NumberSetting) setting).getPrecisePattern().chars().filter(t -> t == '0').count() + "f";
                    UI_RegisterNumberSetting(module.getNameKey(), setting.getName(),setting.getName(Language.Chinese), ((NumberSetting) setting).getValue().floatValue(), (float) ((NumberSetting) setting).getMin(), (float) ((NumberSetting) setting).getMax(),
                            ((NumberSetting) setting).getPrecisePattern().equals("#") ? "%.0f" : str);
                } else if (setting instanceof ModeSetting) {
                    UI_RegisterModeSetting(module.getNameKey(), setting.getName(),setting.getName(Language.Chinese), ((ModeSetting) setting).getValue(), ((ModeSetting) setting).getModes());
                } else if (setting instanceof ColorSetting) {
                    UI_RegisterColorSetting(module.getNameKey(), setting.getName(),setting.getName(Language.Chinese), ((ColorSetting) setting).getValue());
                } else if (setting instanceof ButtonSetting) {
                    UI_RegisterButtonSetting(module.getNameKey(), setting.getName(),setting.getName(Language.Chinese));
                }
                UI_SetSettingLevel(module.getNameKey(), setting.getName(), setting.getLevel());
            }

        }
        System.out.println("start render thread");
        UI_CreateRenderThread();
    }

    private static final BlockingQueue<Runnable> taskQueue = new LinkedBlockingQueue<>();
    private static final List<Runnable> tasks = new ArrayList<>();
    //jni
    public static void onRenderLoopCall() {
        try {
//            synchronized (tasks) {
//                if (!tasks.isEmpty()) {
//                    tasks.remove(0).run();
//                }
//            }
            Runnable task = taskQueue.poll();
            if (task != null) {
                task.run();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    public static boolean isOnRenderThread() {
        return Thread.currentThread() == Main.SkijaThread;
    }

    public static void execute(Runnable runnable) {
//        synchronized (tasks) {
//            tasks.add(runnable);
//        }
        boolean s = taskQueue.offer(runnable);
        if(!s) {
            System.out.println("Put runnable failed!!");
        }
    }
    public static native void log(String string);

    public static native String getGpuName();
    public static native double getCPUTemperature();
    public static native long getCPUFrequency();
    private static boolean g = true;
    public static long frequency;
    public static double temperature;
    public static void startGettingCPUTemperature(){
        g = true;
        new Thread(() -> {
            while (g) {

                try {
                    temperature = getCPUTemperature();
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

        }).start();
    }
    public static void startGettingCPUFrequency(){
        g = true;
        new Thread(() -> {
            while (g) {
                frequency = getCPUFrequency();
            }
        }).start();
    }
    public static void stopGettingCPUFrequencyAndTemp() {
        g = false;
    }

    public static native void UI_UpdateModuleKey(String name, int key);
    public static void UI_OnModuleToggled(String moduleName, boolean enable) {
        Main.INSTANCE.moduleManager.getModule(moduleName).setEnable(enable);
    }
    public static boolean isSettingDisplay(String moduleName, String settingName) {
        Module module = Main.INSTANCE.moduleManager.getModule(moduleName);
        if(module == null) {
            return true;
        }
        Setting<?> setting = SettingManager.getSettingByObj(module, settingName);
        if(setting == null) {
            return true;
        }
        return setting.isDisplay();
    }

    public static void UI_OnModuleKeyChanged(String moduleName, int key) {
        Main.INSTANCE.moduleManager.getModule(moduleName).setKey(key);
    }
    public static String getKeyName(int keycode) {
        switch (keycode) {
            case KeyEvent.VK_A: return "A";
            case KeyEvent.VK_B: return "B";
            case KeyEvent.VK_C: return "C";
            case KeyEvent.VK_D: return "D";
            case KeyEvent.VK_E: return "E";
            case KeyEvent.VK_F: return "F";
            case KeyEvent.VK_G: return "G";
            case KeyEvent.VK_H: return "H";
            case KeyEvent.VK_I: return "I";
            case KeyEvent.VK_J: return "J";
            case KeyEvent.VK_K: return "K";
            case KeyEvent.VK_L: return "L";
            case KeyEvent.VK_M: return "M";
            case KeyEvent.VK_N: return "N";
            case KeyEvent.VK_O: return "O";
            case KeyEvent.VK_P: return "P";
            case KeyEvent.VK_Q: return "Q";
            case KeyEvent.VK_R: return "R";
            case KeyEvent.VK_S: return "S";
            case KeyEvent.VK_T: return "T";
            case KeyEvent.VK_U: return "U";
            case KeyEvent.VK_V: return "V";
            case KeyEvent.VK_W: return "W";
            case KeyEvent.VK_X: return "X";
            case KeyEvent.VK_Y: return "Y";
            case KeyEvent.VK_Z: return "Z";
            case KeyEvent.VK_0: return "0";
            case KeyEvent.VK_1: return "1";
            case KeyEvent.VK_2: return "2";
            case KeyEvent.VK_3: return "3";
            case KeyEvent.VK_4: return "4";
            case KeyEvent.VK_5: return "5";
            case KeyEvent.VK_6: return "6";
            case KeyEvent.VK_7: return "7";
            case KeyEvent.VK_8: return "8";
            case KeyEvent.VK_9: return "9";

            case KeyEvent.VK_ENTER: return "ENTER";
            case KeyEvent.VK_SPACE: return "SPACE";
            case KeyEvent.VK_SHIFT: return "SHIFT";
            case KeyEvent.VK_CONTROL: return "CONTROL";
            case KeyEvent.VK_ALT: return "ALT";
            case KeyEvent.VK_ESCAPE: return "ESCAPE";
            case KeyEvent.VK_TAB: return "TAB";
            case KeyEvent.VK_BACK_SPACE: return "BACKSPACE";
            case KeyEvent.VK_DELETE: return "DELETE";
            case KeyEvent.VK_UP: return "UP ARROW";
            case KeyEvent.VK_DOWN: return "DOWN ARROW";
            case KeyEvent.VK_LEFT: return "LEFT ARROW";
            case KeyEvent.VK_RIGHT: return "RIGHT ARROW";
            case KeyEvent.VK_F1: return "F1";
            case KeyEvent.VK_F2: return "F2";
            case KeyEvent.VK_F3: return "F3";
            case KeyEvent.VK_F4: return "F4";
            case KeyEvent.VK_F5: return "F5";
            case KeyEvent.VK_F6: return "F6";
            case KeyEvent.VK_F7: return "F7";
            case KeyEvent.VK_F8: return "F8";
            case KeyEvent.VK_F9: return "F9";
            case KeyEvent.VK_F10: return "F10";
            case KeyEvent.VK_F11: return "F11";
            case KeyEvent.VK_F12: return "F12";
            default: return "Unknown keycode: " + keycode;
        }
    }
    public static String UI_GetKeyNameByKeycode(int key) {
        String a = getKeyName(key);
        return a.toUpperCase();
    }

    public static void UI_OnBooleanValueChanged(String moduleName, String settingName, boolean value) {
//        System.out.println(moduleName + " " + settingName + " " + Main.INSTANCE.hudManager.getHudElement(moduleName));
        SettingManager.getSettings(Main.INSTANCE.moduleManager.getModule(moduleName) == null ? Main.INSTANCE.hudManager.getHudElement(moduleName) : Main.INSTANCE.moduleManager.getModule(moduleName)).forEach(it -> {
//            System.out.println(it.getName());
            if (it.getName().equals(settingName)) {
                ((BooleanSetting) it).setValue(value);

            }
        });
    }

    public static void UI_OnNumberValueChanged(String moduleName, String settingName, double value) {
        SettingManager.getSettings(Main.INSTANCE.moduleManager.getModule(moduleName) == null ? Main.INSTANCE.hudManager.getHudElement(moduleName) : Main.INSTANCE.moduleManager.getModule(moduleName)).forEach(it -> {
            if (it.getName().equals(settingName)) {
                ((NumberSetting) it).setValue(value);
            }
        });

    }

    public static void UI_OnStringValueChanged(String moduleName, String settingName, String value) {
        SettingManager.getSettings(Main.INSTANCE.moduleManager.getModule(moduleName) == null ? Main.INSTANCE.hudManager.getHudElement(moduleName) : Main.INSTANCE.moduleManager.getModule(moduleName)).forEach(it -> {
            if (it.getName().equals(settingName)) {
                ((ModeSetting) it).setValue(value);
            }
        });
    }

    public static void UI_OnColorValueChanged(String moduleName, String settingName, int r, int g, int b, int a) {
        SettingManager.getSettings(Main.INSTANCE.moduleManager.getModule(moduleName) == null ? Main.INSTANCE.hudManager.getHudElement(moduleName) : Main.INSTANCE.moduleManager.getModule(moduleName)).forEach(it -> {
            if (it.getName().equals(settingName)) {

                ((ColorSetting) it).setValue(new Color(r, g, b, a));
            }
        });
    }
    public static void UI_OnButtonClicked(String moduleName, String settingName) {
        SettingManager.getSettings(Main.INSTANCE.moduleManager.getModule(moduleName) == null ? Main.INSTANCE.hudManager.getHudElement(moduleName) : Main.INSTANCE.moduleManager.getModule(moduleName)).forEach(it -> {

            if (it.getName().equals(settingName)) {
                ((ButtonSetting) it).onClickedButton();
            }
        });

    }

    public static native void UI_SetDisplay(String settingName,String moduleName, boolean value, int level);

    public static native void UI_OnSettingUpdated();

    public static native void UI_Initialize();
    public static native void SetWindowToTop();
    public static native void RemoveWindowTopmost();

    public static native void UI_Show();
    public static void Display_UI() {
        new Thread(ExternalUI::DisplayGui).start();
    }
    public static void Init_Gui() {
        new Thread(ExternalUI::InitGui).start();
    }
    public static native void DisplayGui();
    public static native void CloseGui();
    public static native void ClearGui();
    public static native void InitGui();
    public static native void UI_Hide();
    public static native void UI_RegisterHud(String name);
    public static native void UI_RemoveHud(String name);
    public static native void UI_RemoveSettings(String ptrName);

    public static native void UI_UpdateModulesState(String moduleName, boolean state);

    public static native void UI_UpdateBooleanSettingsState(String moduleName, String settingName, boolean state);

    public static native void UI_UpdateNumberSettingsState(String moduleName, String settingName, double state);

    public static native void UI_UpdateStringSettingsState(String moduleName, String settingName, String state);

    public static native void UI_UpdateColorSettingsState(String moduleName, String settingName, Color state);

    public static native void UI_RegisterBooleanSetting(String moduleName, String settingName, String cn, boolean value);

    public static native void UI_RegisterNumberSetting(String moduleName, String settingName,String cn, float current, float min, float max, String precisePattern);

    public static native void UI_RegisterModeSetting(String moduleName, String settingName,String cn, String current, List<String> values);

    public static native void UI_RegisterColorSetting(String moduleName, String settingName,String cn, Color value);
    public static native void UI_RegisterButtonSetting(String moduleName, String settingName,String cn);
    public static native void UI_SetSettingLevel(String moduleName, String settingName,int lv);

    public static native void UI_RegisterModule(Module module);
    public static void Test_Call() {
        System.out.println("Test call");
        InitGui();
        ExternalUI.DisplayGui();
    }
    public static native void Test();//thread

    public static native void UI_CreateRenderThread();
}
